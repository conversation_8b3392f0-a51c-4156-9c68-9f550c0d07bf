package com.hongminh54.storage.Manager;

import com.hongminh54.storage.Database.TransferHistory;
import com.hongminh54.storage.Storage;
import com.hongminh54.storage.Utils.Chat;
import com.hongminh54.storage.Utils.ErrorLogger;
import com.hongminh54.storage.Utils.File;
import com.hongminh54.storage.Utils.TransferMonitor;
import org.bukkit.Bukkit;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.entity.Player;

import java.sql.*;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Date;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.logging.Level;

/**
 * Quản lý các giao dịch chuyển tài nguyên
 */
public class TransferManager {
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
    private static final SimpleDateFormat DB_DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    private static final String TRANSFER_HISTORY_TABLE = "transfer_history";
    // Cache để kiểm tra thời gian chờ
    private static final ConcurrentHashMap<UUID, Long> lastTransferTime = new ConcurrentHashMap<>();
    // Thời gian chờ mặc định giữa các lần chuyển (giây)
    private static final int DEFAULT_COOLDOWN = 5;

    /**
     * Chuyển đổi chuỗi thời gian sang dạng long
     *
     * @param timestamp Chuỗi thời gian theo định dạng "yyyy-MM-dd HH:mm:ss"
     * @return Thời gian dạng long (milli giây từ epoch)
     */
    public static long parseTimestamp(String timestamp) {
        try {
            if (timestamp == null || timestamp.isEmpty()) {
                return System.currentTimeMillis();
            }

            synchronized (DB_DATE_FORMAT) {
                Date date = DB_DATE_FORMAT.parse(timestamp);
                return date.getTime();
            }
        } catch (Exception e) {
            Storage.getStorage().getLogger().warning("Lỗi khi phân tích chuỗi thời gian: " + e.getMessage());
            return System.currentTimeMillis();
        }
    }

    /**
     * Chuyển đổi thời gian dạng long sang chuỗi theo định dạng cơ sở dữ liệu
     *
     * @param timestamp Thời gian dạng long (milli giây từ epoch)
     * @return Chuỗi thời gian theo định dạng "yyyy-MM-dd HH:mm:ss"
     */
    public static String formatTimestamp(long timestamp) {
        synchronized (DB_DATE_FORMAT) {
            return DB_DATE_FORMAT.format(new Date(timestamp));
        }
    }

    /**
     * Lưu lịch sử giao dịch vào cơ sở dữ liệu
     *
     * @param sender   Người gửi
     * @param receiver Người nhận
     * @param material Loại tài nguyên
     * @param amount   Số lượng
     * @return true nếu lưu thành công, false nếu thất bại
     */
    public static boolean recordTransfer(Player sender, Player receiver, String material, int amount) {
        // Kiểm tra xem có bật ghi lịch sử không
        if (!File.getConfig().getBoolean("transfer.log_history", true)) {
            return true;
        }

        // Cập nhật thời gian chuyển giao gần nhất
        lastTransferTime.put(sender.getUniqueId(), System.currentTimeMillis());

        Connection conn = null;
        PreparedStatement ps = null;
        boolean success = false;
        boolean wasAutoCommit = true;

        try {
            conn = Storage.db.getConnection();
            if (conn == null) {
                Storage.getStorage().getLogger().severe("Không thể kết nối đến database khi ghi lịch sử chuyển giao.");
                return false;
            }

            // Lưu giá trị auto-commit hiện tại
            try {
                wasAutoCommit = conn.getAutoCommit();
            } catch (SQLException ex) {
                Storage.getStorage().getLogger().warning("Không thể lấy trạng thái auto-commit: " + ex.getMessage());
            }

            try {
                // Tắt auto-commit để sử dụng transaction
                conn.setAutoCommit(false);

                // Tăng timeout cho SQLite để tránh SQLITE_BUSY
                try {
                    conn.createStatement().execute("PRAGMA busy_timeout = 30000");
                } catch (Exception e) {
                    // Bỏ qua nếu không hỗ trợ
                }

                String insertSQL =
                        "INSERT INTO " + TRANSFER_HISTORY_TABLE +
                                " (sender, receiver, material, amount, timestamp) VALUES (?, ?, ?, ?, ?)";

                ps = conn.prepareStatement(insertSQL);
                ps.setString(1, sender.getName());
                ps.setString(2, receiver.getName());
                ps.setString(3, material);
                ps.setInt(4, amount);
                ps.setString(5, formatTimestamp(System.currentTimeMillis()));

                int result = ps.executeUpdate();

                // Commit transaction
                conn.commit();

                success = result > 0;

                // Ghi lại hoạt động chuyển khoáng sản
                StatsManager.recordTransfer(sender);
                StatsManager.recordTransfer(receiver);

            } catch (SQLException ex) {
                // Kiểm tra xem có phải lỗi do database bị khóa không
                boolean isSQLiteBusy = ex.getMessage() != null &&
                        (ex.getMessage().contains("SQLITE_BUSY") ||
                                ex.getMessage().contains("database is locked") ||
                                ex.getMessage().contains("database table is locked"));

                if (isSQLiteBusy) {
                    Storage.getStorage().getLogger().warning("Database bị khóa khi ghi lịch sử chuyển giao, sẽ thử lại sau");
                } else {
                    Storage.getStorage().getLogger().severe("Lỗi khi ghi lịch sử chuyển giao: " + ex.getMessage());
                }

                // Rollback transaction nếu có lỗi
                try {
                    if (conn != null && !conn.getAutoCommit()) {
                        conn.rollback();
                    }
                } catch (SQLException e) {
                    Storage.getStorage().getLogger().log(Level.SEVERE, "Lỗi khi rollback transaction: " + e.getMessage(), e);
                }

            } finally {
                // Khôi phục trạng thái auto-commit ban đầu
                try {
                    if (conn != null && !conn.isClosed() && conn.getAutoCommit() != wasAutoCommit) {
                        conn.setAutoCommit(wasAutoCommit);
                    }
                } catch (SQLException ex) {
                    Storage.getStorage().getLogger().warning("Không thể khôi phục trạng thái auto-commit: " + ex.getMessage());
                }
            }

        } finally {
            // Đóng tất cả tài nguyên
            try {
                if (ps != null) ps.close();
                if (conn != null) {
                    // Đảm bảo auto-commit được bật trước khi trả kết nối về pool
                    try {
                        if (!conn.getAutoCommit()) {
                            conn.setAutoCommit(true);
                        }
                    } catch (SQLException e) {
                        Storage.getStorage().getLogger().warning("Không thể bật auto-commit: " + e.getMessage());
                    }
                    Storage.db.returnConnection(conn);
                }
            } catch (SQLException e) {
                Storage.getStorage().getLogger().log(Level.SEVERE, "Lỗi khi đóng kết nối: " + e.getMessage(), e);
            }
        }

        return success;
    }

    /**
     * Lấy lịch sử giao dịch của người chơi (cả gửi và nhận)
     *
     * @param playerName Tên người chơi
     * @param limit      Số lượng tối đa lịch sử trả về
     * @return Danh sách lịch sử giao dịch
     */
    public static List<TransferHistory> getPlayerTransferHistory(String playerName, int limit) {
        List<TransferHistory> history = new ArrayList<>();

        // Kiểm tra bảng tồn tại
        if (!isTableExists()) {
            // Tạo bảng nếu chưa tồn tại
            if (!createTransferHistoryTable()) {
                Storage.getStorage().getLogger().warning("Không thể tạo bảng lịch sử giao dịch. Trả về danh sách trống.");
                return history;
            }
        }

        // Truy vấn SQL để lấy lịch sử giao dịch
        String sql = "SELECT * FROM " + TRANSFER_HISTORY_TABLE + " WHERE sender = ? OR receiver = ? ORDER BY timestamp DESC LIMIT ?";

        try (Connection conn = Storage.db.getConnection();
             PreparedStatement ps = conn.prepareStatement(sql)) {

            ps.setString(1, playerName);
            ps.setString(2, playerName);
            ps.setInt(3, limit);

            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    TransferHistory transfer = new TransferHistory(
                            rs.getInt("id"),
                            rs.getString("sender"),
                            rs.getString("receiver"),
                            rs.getString("material"),
                            rs.getInt("amount"),
                            parseTimestamp(rs.getString("timestamp"))
                    );

                    history.add(transfer);
                }
            }
        } catch (SQLException e) {
            Storage.getStorage().getLogger().severe("Lỗi khi lấy lịch sử giao dịch: " + e.getMessage());
            e.printStackTrace();
        }

        return history;
    }

    /**
     * Kiểm tra bảng lịch sử giao dịch có tồn tại không
     */
    public static boolean isTableExists() {
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        boolean exists = false;

        try {
            conn = Storage.db.getConnection();
            if (conn == null || conn.isClosed()) {
                Storage.getStorage().getLogger().warning("Không thể kết nối đến cơ sở dữ liệu để kiểm tra bảng lịch sử giao dịch");
                return false;
            }

            // Kiểm tra kết nối
            if (conn.isClosed()) {
                Storage.getStorage().getLogger().warning("Kết nối đến cơ sở dữ liệu đã đóng khi kiểm tra bảng lịch sử giao dịch");
                return false;
            }

            stmt = conn.createStatement();
            rs = stmt.executeQuery("SELECT name FROM sqlite_master WHERE type='table' AND name='" + TRANSFER_HISTORY_TABLE + "'");
            exists = rs.next();
        } catch (SQLException e) {
            Storage.getStorage().getLogger().log(Level.WARNING, "Lỗi khi kiểm tra bảng lịch sử giao dịch: " + e.getMessage(), e);
        } finally {
            try {
                if (rs != null) rs.close();
                if (stmt != null) stmt.close();
                if (conn != null) Storage.db.returnConnection(conn);
            } catch (SQLException e) {
                Storage.getStorage().getLogger().warning("Lỗi khi đóng kết nối kiểm tra bảng: " + e.getMessage());
            }
        }

        return exists;
    }

    /**
     * Di chuyển dữ liệu từ bảng cũ sang bảng mới
     *
     * @param conn Kết nối cơ sở dữ liệu
     */
    private static void migrateTransferHistory(Connection conn) {
        Storage.getStorage().getLogger().info("Bắt đầu di chuyển dữ liệu từ bảng cũ sang bảng mới...");

        try {
            // Tạo bảng mới nếu chưa tồn tại
            String createTableSQL =
                    "CREATE TABLE IF NOT EXISTS " + TRANSFER_HISTORY_TABLE + " (" +
                            "id INTEGER PRIMARY KEY AUTOINCREMENT, " +
                            "sender TEXT NOT NULL, " +
                            "receiver TEXT NOT NULL, " +
                            "material TEXT NOT NULL, " +
                            "amount INTEGER NOT NULL, " +
                            "timestamp TEXT NOT NULL)";

            try (PreparedStatement ps = conn.prepareStatement(createTableSQL)) {
                ps.executeUpdate();
            }

            // Lấy dữ liệu từ bảng cũ
            String selectSQL = "SELECT * FROM storage_transfers";
            try (PreparedStatement ps = conn.prepareStatement(selectSQL);
                 ResultSet rs = ps.executeQuery()) {

                // Chuẩn bị câu lệnh chèn dữ liệu vào bảng mới
                String insertSQL =
                        "INSERT INTO " + TRANSFER_HISTORY_TABLE +
                                " (sender, receiver, material, amount, timestamp) VALUES (?, ?, ?, ?, ?)";

                try (PreparedStatement psInsert = conn.prepareStatement(insertSQL)) {
                    int count = 0;

                    while (rs.next()) {
                        psInsert.setString(1, rs.getString("sender_name"));
                        psInsert.setString(2, rs.getString("receiver_name"));
                        psInsert.setString(3, rs.getString("material"));
                        psInsert.setInt(4, rs.getInt("amount"));

                        // Xử lý timestamp: nếu là long thì chuyển thành chuỗi
                        long timestamp = rs.getLong("timestamp");
                        String timestampStr;
                        if (timestamp > 0) {
                            timestampStr = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date(timestamp));
                        } else {
                            timestampStr = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
                        }
                        psInsert.setString(5, timestampStr);

                        psInsert.addBatch();
                        count++;

                        // Thực hiện theo lô để tránh quá tải
                        if (count % 100 == 0) {
                            psInsert.executeBatch();
                        }
                    }

                    if (count % 100 != 0) {
                        psInsert.executeBatch();
                    }

                    Storage.getStorage().getLogger().info("Đã di chuyển " + count + " bản ghi từ bảng cũ sang bảng mới.");
                }
            }

        } catch (SQLException e) {
            Storage.getStorage().getLogger().severe("Lỗi khi di chuyển dữ liệu: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Tạo bảng lịch sử giao dịch nếu chưa tồn tại
     *
     * @return true nếu tạo thành công, false nếu thất bại
     */
    public static boolean createTransferHistoryTable() {
        Connection conn = null;
        PreparedStatement ps = null;

        try {
            conn = Storage.db.getConnection();
            if (conn == null) {
                Storage.getStorage().getLogger().severe("Không thể kết nối đến cơ sở dữ liệu để tạo bảng lịch sử chuyển kho");
                return false;
            }

            // Kiểm tra xem bảng đã tồn tại chưa
            boolean tableExists = false;
            try {
                try (PreparedStatement checkPs = conn.prepareStatement("SELECT name FROM sqlite_master WHERE type='table' AND name=?")) {
                    checkPs.setString(1, TRANSFER_HISTORY_TABLE);
                    try (ResultSet rs = checkPs.executeQuery()) {
                        tableExists = rs.next();
                    }
                }
            } catch (SQLException e) {
                Storage.getStorage().getLogger().warning("Lỗi khi kiểm tra bảng: " + e.getMessage());
            }

            if (!tableExists) {
                String createTableSQL =
                        "CREATE TABLE IF NOT EXISTS " + TRANSFER_HISTORY_TABLE + " (" +
                                "id INTEGER PRIMARY KEY AUTOINCREMENT, " +
                                "sender TEXT NOT NULL, " +
                                "receiver TEXT NOT NULL, " +
                                "material TEXT NOT NULL, " +
                                "amount INTEGER NOT NULL, " +
                                "timestamp TEXT NOT NULL)";

                ps = conn.prepareStatement(createTableSQL);
                ps.executeUpdate();
                Storage.getStorage().getLogger().info("Đã tạo bảng " + TRANSFER_HISTORY_TABLE + " thành công.");

                // Kiểm tra lại xem bảng đã được tạo chưa
                try (PreparedStatement verifyPs = conn.prepareStatement("SELECT name FROM sqlite_master WHERE type='table' AND name=?")) {
                    verifyPs.setString(1, TRANSFER_HISTORY_TABLE);
                    try (ResultSet rs = verifyPs.executeQuery()) {
                        boolean verified = rs.next();
                        if (!verified) {
                            Storage.getStorage().getLogger().warning("Không thể xác minh bảng " + TRANSFER_HISTORY_TABLE + " đã được tạo");
                        }
                        return verified;
                    }
                }
            }

            return tableExists;
        } catch (SQLException e) {
            Storage.getStorage().getLogger().severe("Lỗi khi tạo bảng lịch sử chuyển giao: " + e.getMessage());
            e.printStackTrace();
            return false;
        } finally {
            try {
                if (ps != null) ps.close();
                if (conn != null) Storage.db.returnConnection(conn); // Trả kết nối về pool thay vì đóng
            } catch (SQLException e) {
                Storage.getStorage().getLogger().severe("Lỗi khi đóng kết nối: " + e.getMessage());
            }
        }
    }

    /**
     * Kiểm tra có thể chuyển tài nguyên hay không
     *
     * @param player Người chơi cần kiểm tra
     * @return true nếu có thể chuyển
     */
    public static boolean canTransfer(Player player) {
        if (player == null) {
            return false;
        }

        // Kiểm tra quyền
        if (!player.hasPermission("storage.transfer")) {
            return false;
        }

        // Đọc cài đặt thời gian chờ từ config
        int cooldownSeconds = File.getConfig().getInt("transfer.cooldown", DEFAULT_COOLDOWN);

        // Kiểm tra thời gian chờ
        long now = System.currentTimeMillis();
        UUID playerUUID = player.getUniqueId();

        if (lastTransferTime.containsKey(playerUUID)) {
            long lastTime = lastTransferTime.get(playerUUID);
            long diff = now - lastTime;

            return diff >= TimeUnit.SECONDS.toMillis(cooldownSeconds);
        }

        return true;
    }

    /**
     * Xử lý hiệu ứng chuyển giao thành công
     *
     * @param sender      Người gửi
     * @param receiver    Người nhận
     * @param totalAmount Tổng số lượng đã chuyển
     */
    public static void playTransferEffects(Player sender, Player receiver, int totalAmount) {
        // Kiểm tra xem có bật hiệu ứng không
        if (!File.getConfig().getBoolean("transfer.effects_enabled", true) ||
                !File.getConfig().getBoolean("effects.enabled", true)) {
            return;
        }

        FileConfiguration config = File.getConfig();

        // Phát âm thanh
        String senderSound = config.getString("effects.transfer_success.sender_sound", "ENTITY_PLAYER_LEVELUP:0.5:1.0");
        String receiverSound = config.getString("effects.transfer_success.receiver_sound", "ENTITY_EXPERIENCE_ORB_PICKUP:0.5:1.0");

        if (senderSound != null && !senderSound.isEmpty()) {
            try {
                String[] parts = senderSound.split(":");
                org.bukkit.Sound sound = org.bukkit.Sound.valueOf(parts[0]);
                float volume = parts.length > 1 ? Float.parseFloat(parts[1]) : 0.5f;
                float pitch = parts.length > 2 ? Float.parseFloat(parts[2]) : 1.0f;

                sender.playSound(sender.getLocation(), sound, volume, pitch);
            } catch (Exception e) {
                // Bỏ qua lỗi
            }
        }

        if (receiverSound != null && !receiverSound.isEmpty() && receiver.isOnline()) {
            try {
                String[] parts = receiverSound.split(":");
                org.bukkit.Sound sound = org.bukkit.Sound.valueOf(parts[0]);
                float volume = parts.length > 1 ? Float.parseFloat(parts[1]) : 0.5f;
                float pitch = parts.length > 2 ? Float.parseFloat(parts[2]) : 1.0f;

                receiver.playSound(receiver.getLocation(), sound, volume, pitch);
            } catch (Exception e) {
                // Bỏ qua lỗi
            }
        }

        // Hiệu ứng hạt
        String senderParticle = config.getString("effects.transfer_success.sender_particle", "VILLAGER_HAPPY:0.3:0.3:0.3:0.05:10");
        String receiverParticle = config.getString("effects.transfer_success.receiver_particle", "COMPOSTER:0.3:0.3:0.3:0.05:10");

        // Kiểm tra số lượng lớn và sử dụng hiệu ứng đặc biệt
        if (totalAmount > 32) {
            String largeTransferSenderParticle = config.getString("effects.large_transfer.sender_particle");
            String largeTransferReceiverParticle = config.getString("effects.large_transfer.receiver_particle");

            if (largeTransferSenderParticle != null && !largeTransferSenderParticle.isEmpty()) {
                senderParticle = largeTransferSenderParticle;
            }

            if (largeTransferReceiverParticle != null && !largeTransferReceiverParticle.isEmpty()) {
                receiverParticle = largeTransferReceiverParticle;
            }
        }

        // Giới hạn số lượng hạt
        int maxParticleCount = config.getInt("settings.max_particle_count", 15);

        // Phát hiệu ứng hạt cho người gửi
        if (senderParticle != null && !senderParticle.isEmpty()) {
            try {
                String[] parts = senderParticle.split(":");
                org.bukkit.Particle particleType = org.bukkit.Particle.valueOf(parts[0]);

                double offsetX = parts.length > 1 ? Double.parseDouble(parts[1]) : 0.3;
                double offsetY = parts.length > 2 ? Double.parseDouble(parts[2]) : 0.3;
                double offsetZ = parts.length > 3 ? Double.parseDouble(parts[3]) : 0.3;
                double speed = parts.length > 4 ? Double.parseDouble(parts[4]) : 0.05;
                int count = parts.length > 5 ? Integer.parseInt(parts[5]) : 10;

                // Giới hạn số lượng hạt
                count = Math.min(count, maxParticleCount);

                sender.spawnParticle(particleType, sender.getLocation().clone().add(0, 1, 0), count, offsetX, offsetY, offsetZ, speed);
            } catch (Exception e) {
                // Bỏ qua lỗi
            }
        }

        // Phát hiệu ứng hạt cho người nhận nếu online
        if (receiverParticle != null && !receiverParticle.isEmpty() && receiver.isOnline()) {
            try {
                String[] parts = receiverParticle.split(":");
                org.bukkit.Particle particleType = org.bukkit.Particle.valueOf(parts[0]);

                double offsetX = parts.length > 1 ? Double.parseDouble(parts[1]) : 0.3;
                double offsetY = parts.length > 2 ? Double.parseDouble(parts[2]) : 0.3;
                double offsetZ = parts.length > 3 ? Double.parseDouble(parts[3]) : 0.3;
                double speed = parts.length > 4 ? Double.parseDouble(parts[4]) : 0.05;
                int count = parts.length > 5 ? Integer.parseInt(parts[5]) : 10;

                // Giới hạn số lượng hạt
                count = Math.min(count, maxParticleCount);

                receiver.spawnParticle(particleType, receiver.getLocation().clone().add(0, 1, 0), count, offsetX, offsetY, offsetZ, speed);
            } catch (Exception e) {
                // Bỏ qua lỗi
            }
        }
    }

    /**
     * Xử lý hiệu ứng chuyển giao thất bại
     *
     * @param player Người chơi gặp lỗi
     */
    public static void playTransferFailEffect(Player player) {
        // Kiểm tra xem có bật hiệu ứng không
        if (!File.getConfig().getBoolean("transfer.effects_enabled", true) ||
                !File.getConfig().getBoolean("effects.enabled", true)) {
            return;
        }

        String soundConfig = File.getConfig().getString("effects.transfer_fail.sound", "ENTITY_VILLAGER_NO:1.0:1.0");

        if (soundConfig != null && !soundConfig.isEmpty()) {
            try {
                String[] parts = soundConfig.split(":");
                org.bukkit.Sound sound = org.bukkit.Sound.valueOf(parts[0]);
                float volume = parts.length > 1 ? Float.parseFloat(parts[1]) : 1.0f;
                float pitch = parts.length > 2 ? Float.parseFloat(parts[2]) : 1.0f;

                player.playSound(player.getLocation(), sound, volume, pitch);
            } catch (Exception e) {
                // Bỏ qua lỗi
            }
        }
    }

    /**
     * Lấy tổng số lịch sử giao dịch của người chơi
     *
     * @param playerName Tên người chơi
     * @return Tổng số giao dịch
     */
    public static int getTotalTransferHistoryCount(String playerName) {
        int count = 0;

        // Kiểm tra bảng tồn tại
        if (!isTableExists()) {
            // Tạo bảng nếu chưa tồn tại
            if (!createTransferHistoryTable()) {
                Storage.getStorage().getLogger().warning("Không thể tạo bảng lịch sử giao dịch. Trả về số lượng 0.");
                return 0;
            }
        }

        // Truy vấn SQL để đếm số lịch sử giao dịch
        String sql = "SELECT COUNT(*) as total FROM " + TRANSFER_HISTORY_TABLE + " WHERE sender = ? OR receiver = ?";

        try (Connection conn = Storage.db.getConnection();
             PreparedStatement ps = conn.prepareStatement(sql)) {

            ps.setString(1, playerName);
            ps.setString(2, playerName);

            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    count = rs.getInt("total");
                }
            }
        } catch (SQLException e) {
            Storage.getStorage().getLogger().severe("Lỗi khi đếm lịch sử giao dịch: " + e.getMessage());
            e.printStackTrace();
        }

        return count;
    }

    /**
     * Lấy lịch sử giao dịch của người chơi (cả gửi và nhận) với phân trang
     *
     * @param playerName   Tên người chơi
     * @param page         Số trang (bắt đầu từ 0)
     * @param itemsPerPage Số lượng mục trên mỗi trang
     * @return Danh sách lịch sử giao dịch theo trang
     */
    public static List<TransferHistory> getPlayerTransferHistoryPaged(String playerName, int page, int itemsPerPage) {
        int offset = page * itemsPerPage;
        List<TransferHistory> history = new ArrayList<>();

        // Kiểm tra bảng tồn tại
        if (!isTableExists()) {
            // Tạo bảng nếu chưa tồn tại
            if (!createTransferHistoryTable()) {
                Storage.getStorage().getLogger().warning("Không thể tạo bảng lịch sử giao dịch. Trả về danh sách trống.");
                return history;
            }
        }

        // Truy vấn SQL để lấy lịch sử giao dịch với phân trang
        String sql = "SELECT * FROM " + TRANSFER_HISTORY_TABLE + " WHERE sender = ? OR receiver = ? ORDER BY timestamp DESC LIMIT ? OFFSET ?";

        try (Connection conn = Storage.db.getConnection();
             PreparedStatement ps = conn.prepareStatement(sql)) {

            ps.setString(1, playerName);
            ps.setString(2, playerName);
            ps.setInt(3, itemsPerPage);
            ps.setInt(4, offset);

            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    TransferHistory transfer = new TransferHistory(
                            rs.getInt("id"),
                            rs.getString("sender"),
                            rs.getString("receiver"),
                            rs.getString("material"),
                            rs.getInt("amount"),
                            parseTimestamp(rs.getString("timestamp"))
                    );

                    history.add(transfer);
                }
            }
        } catch (SQLException e) {
            Storage.getStorage().getLogger().severe("Lỗi khi lấy lịch sử giao dịch: " + e.getMessage());
            e.printStackTrace();
        }

        return history;
    }

    /**
     * Lấy lịch sử giao dịch của người chơi (cả gửi và nhận) với giới hạn số lượng
     *
     * @param playerName   Tên người chơi
     * @param page         Số trang (bắt đầu từ 0)
     * @param itemsPerPage Số lượng mục trên mỗi trang
     * @param maxItems     Số lượng tối đa mục sẽ xem xét
     * @return Danh sách lịch sử giao dịch theo trang nhưng giới hạn tối đa chỉ sẽ đến maxItems
     */
    public static List<TransferHistory> getPlayerTransferHistoryLimited(String playerName, int page, int itemsPerPage, int maxItems) {
        int offset = page * itemsPerPage;

        // Đảm bảo offset không vượt quá giới hạn tối đa
        if (offset >= maxItems) {
            offset = Math.max(0, maxItems - itemsPerPage);
        }

        List<TransferHistory> history = new ArrayList<>();

        // Kiểm tra bảng tồn tại
        if (!isTableExists()) {
            // Tạo bảng nếu chưa tồn tại
            if (!createTransferHistoryTable()) {
                Storage.getStorage().getLogger().warning("Không thể tạo bảng lịch sử giao dịch. Trả về danh sách trống.");
                return history;
            }
        }

        // Truy vấn SQL để lấy lịch sử giao dịch với giới hạn tối đa
        String sql = "SELECT * FROM " + TRANSFER_HISTORY_TABLE + " WHERE (sender = ? OR receiver = ?) ORDER BY timestamp DESC LIMIT ? OFFSET ?";

        try (Connection conn = Storage.db.getConnection();
             PreparedStatement ps = conn.prepareStatement(sql)) {

            ps.setString(1, playerName);
            ps.setString(2, playerName);
            ps.setInt(3, itemsPerPage);
            ps.setInt(4, offset);

            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    TransferHistory transfer = new TransferHistory(
                            rs.getInt("id"),
                            rs.getString("sender"),
                            rs.getString("receiver"),
                            rs.getString("material"),
                            rs.getInt("amount"),
                            parseTimestamp(rs.getString("timestamp"))
                    );

                    history.add(transfer);
                }
            }
        } catch (SQLException e) {
            Storage.getStorage().getLogger().severe("Lỗi khi lấy lịch sử giao dịch giới hạn: " + e.getMessage());
            e.printStackTrace();
        }

        return history;
    }

    /**
     * Lấy lịch sử giao dịch giữa hai người chơi
     *
     * @param playerName1 Tên người chơi thứ nhất
     * @param playerName2 Tên người chơi thứ hai
     * @param page        Trang hiện tại (bắt đầu từ 0)
     * @param pageSize    Số lượng giao dịch trên mỗi trang
     * @return Danh sách lịch sử giao dịch
     */
    public static List<TransferHistory> getTransferHistoryBetweenPlayers(String playerName1, String playerName2, int page, int pageSize) {
        List<TransferHistory> history = new ArrayList<>();

        // Kiểm tra bảng tồn tại
        if (!isTableExists()) {
            return history;
        }

        // Tính offset dựa trên trang và kích thước trang
        int offset = page * pageSize;

        // Truy vấn SQL để lấy lịch sử giao dịch giữa hai người chơi
        String sql = "SELECT * FROM " + TRANSFER_HISTORY_TABLE + " WHERE (sender = ? AND receiver = ?) OR (sender = ? AND receiver = ?) ORDER BY timestamp DESC LIMIT ? OFFSET ?";

        try (Connection conn = Storage.db.getConnection();
             PreparedStatement ps = conn.prepareStatement(sql)) {

            ps.setString(1, playerName1);
            ps.setString(2, playerName2);
            ps.setString(3, playerName2);
            ps.setString(4, playerName1);
            ps.setInt(5, pageSize);
            ps.setInt(6, offset);

            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    TransferHistory transfer = new TransferHistory(
                            rs.getInt("id"),
                            rs.getString("sender"),
                            rs.getString("receiver"),
                            rs.getString("material"),
                            rs.getInt("amount"),
                            parseTimestamp(rs.getString("timestamp"))
                    );

                    history.add(transfer);
                }
            }
        } catch (SQLException e) {
            Storage.getStorage().getLogger().severe("Lỗi khi lấy lịch sử giao dịch: " + e.getMessage());
            e.printStackTrace();
        }

        return history;
    }

    /**
     * Đếm tổng số giao dịch giữa hai người chơi
     *
     * @param playerName1 Tên người chơi thứ nhất
     * @param playerName2 Tên người chơi thứ hai
     * @return Tổng số giao dịch
     */
    public static int countTransferHistoryBetweenPlayers(String playerName1, String playerName2) {
        // Kiểm tra bảng tồn tại
        if (!isTableExists()) {
            return 0;
        }

        // Truy vấn SQL để đếm tổng số giao dịch giữa hai người chơi
        String sql = "SELECT COUNT(*) as total FROM " + TRANSFER_HISTORY_TABLE + " WHERE (sender = ? AND receiver = ?) OR (sender = ? AND receiver = ?)";

        try (Connection conn = Storage.db.getConnection();
             PreparedStatement ps = conn.prepareStatement(sql)) {

            ps.setString(1, playerName1);
            ps.setString(2, playerName2);
            ps.setString(3, playerName2);
            ps.setString(4, playerName1);

            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt("total");
                }
            }
        } catch (SQLException e) {
            Storage.getStorage().getLogger().severe("Lỗi khi đếm lịch sử giao dịch: " + e.getMessage());
            e.printStackTrace();
        }

        return 0;
    }

    /**
     * Lấy danh sách tin nhắn lịch sử giao dịch của người chơi
     *
     * @param playerName Tên người chơi
     * @param limit      Số lượng tối đa lịch sử trả về
     * @return Danh sách tin nhắn lịch sử
     */
    public static List<String> getTransferHistoryMessages(String playerName, int limit) {
        List<String> messages = new ArrayList<>();

        // Kiểm tra bảng tồn tại
        if (!isTableExists()) {
            messages.add(Chat.colorize("&cKhông tìm thấy dữ liệu lịch sử giao dịch."));
            return messages;
        }

        List<TransferHistory> history = getPlayerTransferHistory(playerName, limit);

        if (history.isEmpty()) {
            messages.add(Chat.colorize("&eBạn chưa có giao dịch nào."));
            return messages;
        }

        for (TransferHistory transfer : history) {
            String materialName = File.getConfig().getString("items." + transfer.getMaterial(), transfer.getMaterial().split(";")[0]);

            // Định dạng thời gian
            String time = DATE_FORMAT.format(transfer.getDate());

            // Kiểm tra xem người chơi là người gửi hay người nhận
            if (transfer.getSenderName().equalsIgnoreCase(playerName)) {
                // Người chơi là người gửi
                messages.add(Chat.colorize(
                        "&8[&e" + time + "&8] &fBạn đã chuyển &a" + transfer.getAmount() + " " + materialName +
                                " &fcho &e" + transfer.getReceiverName()
                ));
            } else {
                // Người chơi là người nhận
                messages.add(Chat.colorize(
                        "&8[&e" + time + "&8] &fBạn đã nhận &a" + transfer.getAmount() + " " + materialName +
                                " &ftừ &e" + transfer.getSenderName()
                ));
            }
        }

        return messages;
    }

    /**
     * Ghi một giao dịch chuyển khoáng sản vào lịch sử một cách bất đồng bộ
     */
    public static void recordTransferAsync(String sender, String receiver, String material, int amount) {
        if (sender == null || receiver == null || material == null || amount <= 0) {
            return;
        }

        // Lưu dữ liệu cần thiết cho task bất đồng bộ
        final String finalSender = sender;
        final String finalReceiver = receiver;
        final String finalMaterial = material;
        final int finalAmount = amount;

        // Thực hiện ghi lịch sử trong luồng bất đồng bộ
        Bukkit.getScheduler().runTaskAsynchronously(Storage.getStorage(), () -> {
            Connection conn = null;
            PreparedStatement ps = null;

            try {
                // Đảm bảo bảng lịch sử tồn tại
                try {
                    if (!isTableExists()) {
                        createTransferHistoryTable();
                    }
                } catch (Exception e) {
                    Storage.getStorage().getLogger().warning("Lỗi khi kiểm tra bảng lịch sử giao dịch: " + e.getMessage());
                    // Tiếp tục thực hiện, vì bảng có thể đã tồn tại
                }

                conn = Storage.db.getConnection();
                if (conn == null) {
                    Storage.getStorage().getLogger().severe("Không thể kết nối đến cơ sở dữ liệu để ghi lịch sử chuyển khoản");
                    return;
                }

                // Kiểm tra kết nối có mở không
                if (conn.isClosed()) {
                    Storage.getStorage().getLogger().severe("Kết nối cơ sở dữ liệu đã đóng khi ghi lịch sử chuyển khoản");
                    conn = Storage.db.getConnection(); // Thử lấy kết nối mới
                    if (conn == null || conn.isClosed()) {
                        Storage.getStorage().getLogger().severe("Không thể tạo kết nối mới để ghi lịch sử chuyển khoản");
                        return;
                    }
                }

                // Chuẩn bị timestamp
                String timestamp = formatTimestamp(System.currentTimeMillis());

                // Thêm giao dịch vào bảng lịch sử
                ps = conn.prepareStatement(
                        "INSERT INTO transfer_history (sender, receiver, material, amount, timestamp) VALUES (?, ?, ?, ?, ?)"
                );
                ps.setString(1, finalSender);
                ps.setString(2, finalReceiver);
                ps.setString(3, finalMaterial);
                ps.setInt(4, finalAmount);
                ps.setString(5, timestamp);
                ps.executeUpdate();

                if (Storage.getStorage().isDebug()) {
                    Storage.getStorage().getLogger().info(
                            "Đã ghi lịch sử chuyển khoản: " + finalSender + " -> " + finalReceiver +
                                    ", " + finalMaterial + " x" + finalAmount + " (bất đồng bộ)"
                    );
                }

            } catch (SQLException e) {
                Storage.getStorage().getLogger().log(Level.SEVERE, "Lỗi SQL khi ghi lịch sử chuyển khoản: " + e.getMessage(), e);
            } finally {
                try {
                    if (ps != null) ps.close();
                    if (conn != null) Storage.db.returnConnection(conn);
                } catch (SQLException e) {
                    Storage.getStorage().getLogger().log(Level.SEVERE, "Lỗi khi đóng kết nối: " + e.getMessage(), e);
                }
            }
        });
    }

    /**
     * Chuyển tài nguyên từ người gửi đến người nhận với kiểm tra tính nhất quán dữ liệu
     *
     * @param sender    Người gửi
     * @param receiver  Người nhận
     * @param resources Map chứa tài nguyên và số lượng cần chuyển
     * @return Map chứa tài nguyên và số lượng đã chuyển thành công
     */
    public static Map<String, Integer> transferResourcesWithConsistencyCheck(Player sender, Player receiver, Map<String, Integer> resources) {
        if (sender == null || receiver == null || resources == null || resources.isEmpty()) {
            return new java.util.HashMap<>();
        }

        // Bắt đầu một phiên chuyển mới và lấy ID
        String transactionId = TransferMonitor.startTransferSession(sender, receiver, resources);
        Storage.getStorage().getLogger().info("Transaction ID: " + transactionId + " bắt đầu.");

        try {
            // Khóa đồng bộ cho cả người gửi và người nhận trước khi thực hiện giao dịch
            String senderLockKey = "transfer_lock_" + sender.getUniqueId();
            String receiverLockKey = "transfer_lock_" + receiver.getUniqueId();

            synchronized (senderLockKey.intern()) {
                synchronized (receiverLockKey.intern()) {
                    // Kiểm tra dữ liệu đơn giản
                    MineManager.loadPlayerData(sender);
                    MineManager.loadPlayerData(receiver);

                    // Lưu trữ dữ liệu ban đầu để kiểm tra tính nhất quán sau khi chuyển
                    Map<String, Integer> senderOriginalData = new java.util.HashMap<>();
                    Map<String, Integer> receiverOriginalData = new java.util.HashMap<>();

                    for (String material : resources.keySet()) {
                        senderOriginalData.put(material, MineManager.getPlayerBlock(sender, material));
                        receiverOriginalData.put(material, MineManager.getPlayerBlock(receiver, material));
                    }

                    // Ghi log dữ liệu ban đầu trước khi chuyển
                    Storage.getStorage().getLogger().info("Transaction ID: " + transactionId +
                            " - Dữ liệu ban đầu người gửi " + sender.getName() + ": " + senderOriginalData);
                    Storage.getStorage().getLogger().info("Transaction ID: " + transactionId +
                            " - Dữ liệu ban đầu người nhận " + receiver.getName() + ": " + receiverOriginalData);

                    // Thực hiện chuyển tài nguyên trong khối đồng bộ
                    Map<String, Integer> transferredResources = new HashMap<>();
                    for (Map.Entry<String, Integer> entry : resources.entrySet()) {
                        String material = entry.getKey();
                        int amount = entry.getValue();

                        if (amount <= 0) continue;

                        // Kiểm tra người gửi có đủ tài nguyên không
                        int senderAmount = MineManager.getPlayerBlock(sender, material);
                        if (senderAmount < amount) {
                            Storage.getStorage().getLogger().info("Transaction ID: " + transactionId +
                                    " - Người gửi " + sender.getName() + " không đủ " + material +
                                    " (Cần: " + amount + ", Có: " + senderAmount + ")");
                            continue;
                        }

                        // Kiểm tra không gian người nhận
                        int receiverAmount = MineManager.getPlayerBlock(receiver, material);
                        int maxStorage = MineManager.getMaxBlock(receiver);
                        int availableSpace = maxStorage - receiverAmount;

                        if (availableSpace <= 0) {
                            Storage.getStorage().getLogger().info("Transaction ID: " + transactionId +
                                    " - Người nhận " + receiver.getName() + " không đủ không gian cho " + material);
                            continue;
                        }

                        // Tính toán số lượng có thể chuyển
                        int transferAmount = Math.min(amount, availableSpace);

                        // Thực hiện chuyển tài nguyên
                        if (MineManager.removeBlockAmount(sender, material, transferAmount)) {
                            if (MineManager.addBlockAmount(receiver, material, transferAmount)) {
                                // Lưu lại thông tin chuyển thành công
                                transferredResources.put(material, transferAmount);

                                // Ghi log
                                Storage.getStorage().getLogger().info("Transaction ID: " + transactionId +
                                        " - Chuyển thành công " + transferAmount + " " + material +
                                        " từ " + sender.getName() + " đến " + receiver.getName());
                            } else {
                                // Hoàn trả nếu không thể thêm cho người nhận
                                MineManager.addBlockAmount(sender, material, transferAmount);
                                Storage.getStorage().getLogger().warning("Transaction ID: " + transactionId +
                                        " - Không thể thêm " + material + " cho người nhận. Đã hoàn trả.");
                            }
                        } else {
                            Storage.getStorage().getLogger().warning("Transaction ID: " + transactionId +
                                    " - Không thể trừ " + material + " từ người gửi.");
                        }
                    }

                    // Kiểm tra kết quả
                    boolean success = !transferredResources.isEmpty();

                    if (success) {
                        // Lưu dữ liệu ngay lập tức
                        try {
                            Storage.getStorage().getLogger().info("Transaction ID: " + transactionId + " - Đang lưu dữ liệu người gửi...");
                            MineManager.savePlayerData(sender);
                            Storage.getStorage().getLogger().info("Transaction ID: " + transactionId + " - Đang lưu dữ liệu người nhận...");
                            MineManager.savePlayerData(receiver);
                        } catch (Exception e) {
                            Storage.getStorage().getLogger().severe("Transaction ID: " + transactionId +
                                    " - Lỗi khi lưu dữ liệu: " + e.getMessage());
                            e.printStackTrace();
                        }

                        // Ghi lại lịch sử chuyển khoản
                        for (Map.Entry<String, Integer> entry : transferredResources.entrySet()) {
                            recordTransferAsync(sender.getName(), receiver.getName(), entry.getKey(), entry.getValue());
                        }

                        // Ghi lại hoạt động chuyển khoáng sản
                        StatsManager.recordTransfer(sender);
                        StatsManager.recordTransfer(receiver);

                        // Kết thúc phiên và xác minh tính nhất quán
                        boolean finalConsistency = TransferMonitor.endTransferSession(transactionId, transferredResources);

                        // Ghi log kết thúc transaction
                        if (finalConsistency) {
                            Storage.getStorage().getLogger().info("Transaction ID: " + transactionId + " hoàn tất thành công.");
                        } else {
                            Storage.getStorage().getLogger().warning("Transaction ID: " + transactionId +
                                    " - Hoàn tất với cảnh báo về tính nhất quán dữ liệu.");
                        }
                    } else {
                        Storage.getStorage().getLogger().warning("Transaction ID: " + transactionId +
                                " - Không thể chuyển tài nguyên từ " + sender.getName() + " đến " + receiver.getName());

                        // Ghi log kết thúc transaction thất bại
                        Storage.getStorage().getLogger().info("Transaction ID: " + transactionId + " thất bại.");
                    }

                    return transferredResources;
                }
            }

        } catch (Exception e) {
            Storage.getStorage().getLogger().severe("Transaction ID: " + transactionId +
                    " - Lỗi nghiêm trọng khi chuyển tài nguyên: " + e.getMessage());
            e.printStackTrace();

            // Ghi log lỗi vào cơ sở dữ liệu
            ErrorLogger.logError(sender.getName() + " -> " + receiver.getName(),
                    "transfer_exception", "Session: " + transactionId + ", Error: " + e.getMessage());

            // Nếu có lỗi, làm mới dữ liệu của cả hai người chơi để đảm bảo tính nhất quán
            try {
                Storage.getStorage().getLogger().info("Transaction ID: " + transactionId +
                        " - Đang cố gắng phục hồi dữ liệu cho " + sender.getName() + " và " + receiver.getName());
                MineManager.loadPlayerData(sender);
                MineManager.loadPlayerData(receiver);
            } catch (Exception ex) {
                Storage.getStorage().getLogger().severe("Transaction ID: " + transactionId +
                        " - Không thể phục hồi dữ liệu: " + ex.getMessage());
            }

            // Ghi log kết thúc transaction lỗi
            Storage.getStorage().getLogger().info("Transaction ID: " + transactionId + " thất bại do lỗi: " + e.getMessage());

            return new java.util.HashMap<>();
        }
    }

    /**
     * Lớp lưu trữ thông tin chuyển giao
     */
    public static class TransferRecord {
        private final int id;
        private final String sender;
        private final String receiver;
        private final String material;
        private final int amount;
        private final String timestamp;

        public TransferRecord(int id, String sender, String receiver, String material, int amount, String timestamp) {
            this.id = id;
            this.sender = sender;
            this.receiver = receiver;
            this.material = material;
            this.amount = amount;
            this.timestamp = timestamp;
        }

        public int getId() {
            return id;
        }

        public String getSender() {
            return sender;
        }

        public String getReceiver() {
            return receiver;
        }

        public String getMaterial() {
            return material;
        }

        public int getAmount() {
            return amount;
        }

        public String getTimestamp() {
            return timestamp;
        }

        public String getMaterialDisplayName() {
            return MineManager.getMaterialDisplayName(material);
        }

        @Override
        public String toString() {
            String materialName = getMaterialDisplayName();
            String type = "";

            return timestamp + " - " + amount + "x " + materialName + " từ " + sender + " đến " + receiver;
        }
    }
} 