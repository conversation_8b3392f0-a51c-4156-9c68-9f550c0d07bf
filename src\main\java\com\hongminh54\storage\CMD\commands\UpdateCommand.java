package com.hongminh54.storage.CMD.commands;

import com.hongminh54.storage.Storage;
import com.hongminh54.storage.Utils.Chat;
import com.hongminh54.storage.Utils.UpdateChecker;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.bukkit.util.StringUtil;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class UpdateCommand implements IStorageCommand {

    @Override
    public boolean execute(CommandSender sender, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(Chat.colorize("&cLệnh này chỉ có thể được sử dụng bởi người chơi."));
            return true;
        }

        Player player = (Player) sender;

        // L<PERSON>y danh sách các UpdateChecker
        List<UpdateChecker> checkers = Storage.getUpdateCheckers();
        if (checkers.isEmpty()) {
            player.sendMessage(Chat.colorize("&cKhông có UpdateChecker nào được khởi tạo."));
            return true;
        }

        UpdateChecker checker = checkers.get(0); // Lấy checker đầu tiên

        if (args.length == 1) {
            // Hiển thị thông tin phiên bản hiện tại và kiểm tra cập nhật
            player.sendMessage(Chat.colorize("&e&l≫ Thông tin phiên bản:"));
            player.sendMessage(Chat.colorize("&7Phiên bản hiện tại: &a" + Storage.getStorage().getDescription().getVersion()));
            player.sendMessage(Chat.colorize("&7Đang kiểm tra cập nhật..."));

            // Hiển thị thông tin cập nhật
            checker.showUpdateConfirmation(player);

        } else if (args.length == 2) {
            String action = args[1].toLowerCase();

            if (action.equals("confirm")) {
                // Xác nhận tải xuống cập nhật
                player.sendMessage(Chat.colorize("&e&l⚠ Đang tải xuống bản cập nhật..."));
                player.sendMessage(Chat.colorize("&7Quá trình này có thể mất vài phút."));

                // Thực hiện tải xuống
                checker.downloadUpdate(player);

            } else if (action.equals("cancel")) {
                // Hủy bỏ cập nhật
                player.sendMessage(Chat.colorize("&c&l✕ Đã hủy bỏ quá trình cập nhật."));

            } else {
                // Lệnh không hợp lệ
                player.sendMessage(Chat.colorize("&cLệnh con không hợp lệ. Sử dụng: &e/kho update [confirm|cancel]"));
            }
        } else {
            // Quá nhiều tham số
            player.sendMessage(Chat.colorize("&cCách sử dụng: &e/kho update [confirm|cancel]"));
        }

        return true;
    }

    @Override
    public List<String> getTabComplete(CommandSender sender, String[] args) {
        List<String> completions = new ArrayList<>();

        if (args.length == 2) {
            List<String> updateOptions = Arrays.asList("confirm", "cancel");
            StringUtil.copyPartialMatches(args[1], updateOptions, completions);
        }

        return completions;
    }

    @Override
    public String getCommandName() {
        return "update";
    }

    @Override
    public List<String> getAliases() {
        return new ArrayList<>();
    }

    @Override
    public boolean hasPermission(CommandSender sender) {
        return sender.hasPermission("storage.admin");
    }
}
