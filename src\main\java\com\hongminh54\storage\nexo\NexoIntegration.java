package com.hongminh54.storage.nexo;

import com.hongminh54.storage.Storage;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

/**
 * Tích hợp Nexo plugin để xử lý custom model data
 * Hỗ trợ đa phiên bản Minecraft 1.12.2 - 1.21.4
 *
 * <AUTHOR>
 */
public class NexoIntegration {

    private static boolean nexoEnabled = false;
    private static boolean nexoChecked = false;

    /**
     * Kiểm tra xem Nexo có được cài đặt và hoạt động không
     *
     * @return true nếu Nexo có sẵn
     */
    public static boolean isNexoAvailable() {
        if (!nexoChecked) {
            nexoChecked = true;
            try {
                // Kiểm tra plugin Nexo có được load không
                if (Bukkit.getPluginManager().getPlugin("Nexo") != null &&
                        Bukkit.getPluginManager().isPluginEnabled("Nexo")) {

                    // Kiểm tra class API có tồn tại không
                    Class.forName("com.nexomc.nexo.api.NexoItems");
                    nexoEnabled = true;

                    if (Storage.getStorage().isDebug()) {
                        Storage.getStorage().getLogger().info("Nexo plugin đã được phát hiện và tích hợp thành công!");
                    }
                } else {
                    if (Storage.getStorage().isDebug()) {
                        Storage.getStorage().getLogger().info("Nexo plugin không được tìm thấy hoặc chưa được kích hoạt");
                    }
                }
            } catch (ClassNotFoundException e) {
                if (Storage.getStorage().isDebug()) {
                    Storage.getStorage().getLogger().warning("Nexo API không khả dụng: " + e.getMessage());
                }
            } catch (Exception e) {
                if (Storage.getStorage().isDebug()) {
                    Storage.getStorage().getLogger().warning("Lỗi khi kiểm tra Nexo: " + e.getMessage());
                }
            }
        }
        return nexoEnabled;
    }

    /**
     * Tạo ItemStack từ Nexo item ID
     *
     * @param nexoItemId ID của item trong Nexo
     * @return ItemStack từ Nexo hoặc null nếu không tìm thấy
     */
    public static ItemStack createNexoItem(String nexoItemId) {
        if (!isNexoAvailable() || nexoItemId == null || nexoItemId.isEmpty()) {
            return null;
        }

        try {
            // Sử dụng reflection để gọi NexoItems.itemFromId()
            Class<?> nexoItemsClass = Class.forName("com.nexomc.nexo.api.NexoItems");
            java.lang.reflect.Method itemFromIdMethod = nexoItemsClass.getMethod("itemFromId", String.class);

            Object result = itemFromIdMethod.invoke(null, nexoItemId);
            if (result instanceof ItemStack) {
                if (Storage.getStorage().isDebug()) {
                    Storage.getStorage().getLogger().info("Đã tạo Nexo item: " + nexoItemId);
                }
                return (ItemStack) result;
            }
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể tạo Nexo item '" + nexoItemId + "': " + e.getMessage());
            }
        }

        return null;
    }

    /**
     * Kiểm tra xem ItemStack có phải là Nexo item không
     *
     * @param itemStack ItemStack cần kiểm tra
     * @return true nếu là Nexo item
     */
    public static boolean isNexoItem(ItemStack itemStack) {
        if (!isNexoAvailable() || itemStack == null) {
            return false;
        }

        try {
            Class<?> nexoItemsClass = Class.forName("com.nexomc.nexo.api.NexoItems");
            java.lang.reflect.Method idFromItemMethod = nexoItemsClass.getMethod("idFromItem", ItemStack.class);

            Object result = idFromItemMethod.invoke(null, itemStack);
            return result != null && !result.toString().isEmpty();
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Lỗi khi kiểm tra Nexo item: " + e.getMessage());
            }
        }

        return false;
    }

    /**
     * Lấy Nexo item ID từ ItemStack
     *
     * @param itemStack ItemStack cần lấy ID
     * @return Nexo item ID hoặc null nếu không phải Nexo item
     */
    public static String getNexoItemId(ItemStack itemStack) {
        if (!isNexoAvailable() || itemStack == null) {
            return null;
        }

        try {
            Class<?> nexoItemsClass = Class.forName("com.nexomc.nexo.api.NexoItems");
            java.lang.reflect.Method idFromItemMethod = nexoItemsClass.getMethod("idFromItem", ItemStack.class);

            Object result = idFromItemMethod.invoke(null, itemStack);
            if (result != null && !result.toString().isEmpty()) {
                return result.toString();
            }
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Lỗi khi lấy Nexo item ID: " + e.getMessage());
            }
        }

        return null;
    }

    /**
     * Tạo ItemStack với hỗ trợ Nexo hoặc fallback về custom model data thông thường
     *
     * @param materialName    Tên material hoặc Nexo item ID
     * @param customModelData Custom model data (sử dụng nếu không phải Nexo item)
     * @return ItemStack đã được tạo
     */
    public static ItemStack createItemWithNexoSupport(String materialName, int customModelData) {
        if (materialName == null || materialName.isEmpty()) {
            return new ItemStack(Material.PAPER);
        }

        // Thử tạo Nexo item trước
        if (isNexoAvailable()) {
            ItemStack nexoItem = createNexoItem(materialName);
            if (nexoItem != null) {
                return nexoItem;
            }
        }

        // Fallback về cách tạo item thông thường với custom model data
        ItemStack item = com.hongminh54.storage.compatibility.MaterialCompatibility.createCompatibleItemStack(materialName);

        // Áp dụng custom model data nếu có và phiên bản hỗ trợ
        if (customModelData > 0 && com.hongminh54.storage.compatibility.AdvancedCompatibility.supportsCustomModelData()) {
            ItemMeta meta = item.getItemMeta();
            if (meta != null) {
                com.hongminh54.storage.compatibility.AdvancedCompatibility.setCustomModelData(meta, customModelData);
                item.setItemMeta(meta);
            }
        }

        return item;
    }

    /**
     * Lấy custom model data từ ItemStack, ưu tiên Nexo item
     *
     * @param itemStack ItemStack cần lấy custom model data
     * @return Custom model data hoặc 0 nếu không có
     */
    public static int getCustomModelDataWithNexoSupport(ItemStack itemStack) {
        if (itemStack == null) {
            return 0;
        }

        // Nếu là Nexo item, trả về một giá trị đặc biệt để phân biệt
        if (isNexoItem(itemStack)) {
            // Nexo items có custom model data riêng, không cần xử lý thêm
            ItemMeta meta = itemStack.getItemMeta();
            if (meta != null && meta.hasCustomModelData()) {
                return meta.getCustomModelData();
            }
            return -1; // Giá trị đặc biệt cho Nexo items
        }

        // Fallback về cách lấy custom model data thông thường
        return com.hongminh54.storage.compatibility.AdvancedCompatibility.getCustomModelData(itemStack.getItemMeta());
    }
}
