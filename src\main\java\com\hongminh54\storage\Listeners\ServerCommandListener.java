package com.hongminh54.storage.Listeners;

import com.hongminh54.storage.Manager.MineManager;
import com.hongminh54.storage.Manager.StatsManager;
import com.hongminh54.storage.Storage;
import com.hongminh54.storage.Utils.Chat;
import com.hongminh54.storage.Utils.File;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerCommandPreprocessEvent;
import org.bukkit.event.server.ServerCommandEvent;

/**
 * Listener xử lý sự kiện lệnh server để tự động lưu dữ liệu plugin
 * khi admin sử dụng lệnh /save hoặc /save-all
 * <p>
 * Tính năng này giúp admin lưu dữ liệu gấp khi gặp vấn đề
 * Tương thích với Minecraft 1.12.2 - 1.21.4
 */
public class ServerCommandListener implements Listener {

    private final Storage plugin;

    public ServerCommandListener(Storage plugin) {
        this.plugin = plugin;
    }

    /**
     * Xử lý lệnh từ console server
     * Bắt các lệnh /save và /save-all từ console
     */
    @EventHandler(priority = EventPriority.MONITOR, ignoreCancelled = true)
    public void onServerCommand(ServerCommandEvent event) {
        // Kiểm tra cấu hình có bật tính năng này không
        if (!File.getConfig().getBoolean("database.save_on_server_save", true)) {
            return;
        }

        String command = event.getCommand().toLowerCase().trim();

        // Kiểm tra lệnh save hoặc save-all
        if (command.equals("save") || command.equals("save-all") ||
                command.startsWith("save ") || command.startsWith("save-all ")) {

            // Thực hiện lưu dữ liệu plugin bất đồng bộ để không ảnh hưởng đến lệnh gốc
            Bukkit.getScheduler().runTaskAsynchronously(plugin, () -> {
                saveAllPluginData("Console");
            });
        }
    }

    /**
     * Xử lý lệnh từ người chơi (admin)
     * Bắt các lệnh /save và /save-all từ người chơi có quyền
     */
    @EventHandler(priority = EventPriority.MONITOR, ignoreCancelled = true)
    public void onPlayerCommand(PlayerCommandPreprocessEvent event) {
        // Kiểm tra cấu hình có bật tính năng này không
        if (!File.getConfig().getBoolean("database.save_on_server_save", true)) {
            return;
        }

        Player player = event.getPlayer();
        String command = event.getMessage().toLowerCase().trim();

        // Kiểm tra quyền admin
        if (!player.hasPermission("storage.admin") && !player.isOp()) {
            return;
        }

        // Kiểm tra lệnh save hoặc save-all (bỏ qua dấu /)
        String cmd = command.substring(1); // Bỏ dấu /
        if (cmd.equals("save") || cmd.equals("save-all") ||
                cmd.startsWith("save ") || cmd.startsWith("save-all ")) {

            // Thực hiện lưu dữ liệu plugin bất đồng bộ
            Bukkit.getScheduler().runTaskAsynchronously(plugin, () -> {
                saveAllPluginData(player.getName());
            });
        }
    }

    /**
     * Lưu tất cả dữ liệu plugin cho người chơi đang online
     *
     * @param executor Người thực thi lệnh (để ghi log)
     */
    private void saveAllPluginData(String executor) {
        try {
            // Lấy thông báo từ file message.yml
            String startMessage = File.getMessage().getString("admin.auto_save.start");
            if (startMessage == null) {
                startMessage = "&e[Auto-Save] Bắt đầu lưu dữ liệu plugin...";
            }
            plugin.getLogger().info(Chat.colorize(startMessage) + " (do " + executor + " thực thi lệnh save)");

            int savedPlayers = 0;
            int totalPlayers = Bukkit.getOnlinePlayers().size();

            // Lưu dữ liệu cho tất cả người chơi online với cách tiếp cận an toàn
            for (Player player : Bukkit.getOnlinePlayers()) {
                try {
                    // Sử dụng phương thức lưu an toàn để tránh xung đột PRAGMA
                    savePlayerDataSafely(player);

                    savedPlayers++;

                    // Log tiến trình mỗi 10 người chơi để tránh spam
                    if (savedPlayers % 10 == 0 || savedPlayers == totalPlayers) {
                        plugin.getLogger().info("§a[Auto-Save] Đã lưu: " + savedPlayers + "/" + totalPlayers + " người chơi");
                    }

                } catch (Exception e) {
                    plugin.getLogger().warning("§c[Auto-Save] Lỗi khi lưu dữ liệu cho " + player.getName() + ": " + e.getMessage());
                    // Tiếp tục với người chơi tiếp theo
                }
            }

            // Thông báo hoàn thành
            String completeMessage = File.getMessage().getString("admin.auto_save.complete");
            if (completeMessage == null) {
                completeMessage = "&a[Auto-Save] Hoàn thành! Đã lưu dữ liệu cho #saved#/#total# người chơi";
            }
            plugin.getLogger().info(Chat.colorize(completeMessage
                    .replace("#saved#", String.valueOf(savedPlayers))
                    .replace("#total#", String.valueOf(totalPlayers))));

            // Gửi thông báo cho admin nếu là người chơi thực thi
            if (!executor.equals("Console")) {
                Player adminPlayer = Bukkit.getPlayer(executor);
                if (adminPlayer != null && adminPlayer.isOnline()) {
                    // Chạy trên main thread để gửi message
                    final int finalSavedPlayers = savedPlayers;
                    Bukkit.getScheduler().runTask(plugin, () -> {
                        String successMessage = File.getMessage().getString("admin.auto_save.success");
                        if (successMessage == null) {
                            successMessage = "&a[Storage] Đã tự động lưu dữ liệu cho #count# người chơi!";
                        }
                        adminPlayer.sendMessage(Chat.colorize(successMessage
                                .replace("#count#", String.valueOf(finalSavedPlayers))));
                    });
                }
            }

        } catch (Exception e) {
            plugin.getLogger().severe("§c[Auto-Save] Lỗi nghiêm trọng khi lưu dữ liệu: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Lưu dữ liệu người chơi một cách an toàn để tránh xung đột PRAGMA SQLite
     *
     * @param player Người chơi cần lưu dữ liệu
     */
    private void savePlayerDataSafely(Player player) {
        try {
            // Lưu dữ liệu kho khoáng sản trước (sử dụng phương thức đồng bộ)
            MineManager.savePlayerData(player);

            // Delay nhỏ để đảm bảo transaction hoàn thành
            Thread.sleep(100);

            // Kiểm tra cấu hình có cho phép lưu thống kê không
            boolean saveStats = File.getConfig().getBoolean("database.save_stats_on_server_save", true);
            if (saveStats) {
                // Lưu dữ liệu thống kê bằng phương thức async để tránh PRAGMA conflicts
                saveStatsWithoutPragma(player);
            }

            // Delay nhỏ giữa các người chơi để tránh database lock
            Thread.sleep(50);

        } catch (InterruptedException ie) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("Bị gián đoạn khi lưu dữ liệu", ie);
        } catch (Exception e) {
            throw new RuntimeException("Lỗi khi lưu dữ liệu cho " + player.getName(), e);
        }
    }

    /**
     * Lưu dữ liệu thống kê mà không thiết lập PRAGMA để tránh xung đột
     *
     * @param player Người chơi cần lưu thống kê
     */
    private void saveStatsWithoutPragma(Player player) {
        try {
            // Sử dụng phương thức async để tránh xung đột PRAGMA
            // Phương thức này chỉ thêm vào queue mà không thiết lập PRAGMA
            StatsManager.savePlayerStatsAsync(player);

            // Đợi một chút để đảm bảo dữ liệu được thêm vào queue
            Thread.sleep(10);

        } catch (InterruptedException ie) {
            Thread.currentThread().interrupt();
            plugin.getLogger().fine("Bị gián đoạn khi lưu thống kê cho " + player.getName());
        } catch (Exception e) {
            // Log nhưng không throw exception để không dừng quá trình lưu
            plugin.getLogger().fine("Không thể lưu thống kê cho " + player.getName() + ": " + e.getMessage());
        }
    }
}
