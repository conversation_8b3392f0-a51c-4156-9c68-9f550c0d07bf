package com.hongminh54.storage.Manager;

import com.hongminh54.storage.Database.PlayerData;
import com.hongminh54.storage.Database.SQLite;
import com.hongminh54.storage.Storage;
import com.hongminh54.storage.Utils.File;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;

import java.util.*;

/**
 * Quản lý các chức năng liên quan đến dữ liệu: auto-save, backup, và kiểm tra tính toàn vẹn
 */
public class DataManager {

    /**
     * Khởi tạo tất cả các scheduler cho data management
     */
    public static void initialize() {
        scheduleAutoSave();
        scheduleDataBackup();
        scheduleDataIntegrityCheck();
        scheduleRealTimeSync(); // Thêm real-time sync cho mining
    }

    /**
     * Lên lịch auto-save nếu được bật trong config
     */
    private static void scheduleAutoSave() {
        boolean autoSaveEnabled = File.getConfig().getBoolean("database.auto_save", true);

        if (!autoSaveEnabled) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().info("Auto-save đã bị tắt trong config.");
            }
            return;
        }

        // Lấy thời gian auto-save từ config (mặc định 5 phút = 300 giây)
        long autoSaveInterval = File.getConfig().getLong("database.auto_save_interval", 300) * 20L; // Chuyển đổi sang ticks

        // Kiểm tra giá trị tối thiểu (5 phút)
        if (autoSaveInterval < 6000L) { // Tối thiểu 5 phút (6000 ticks)
            autoSaveInterval = 6000L;
            Storage.getStorage().getLogger().info("Đã thiết lập lại thời gian auto-save tối thiểu là 5 phút để tối ưu hiệu suất.");
        }

        Storage.getStorage().getLogger().info("Đã bật auto-save mỗi " + (autoSaveInterval / 20 / 60) + " phút.");

        // Lên lịch task auto-save
        Bukkit.getScheduler().runTaskTimerAsynchronously(Storage.getStorage(), () -> {
            try {
                // Kiểm tra server load trước khi thực hiện
                if (!shouldPerformOperation()) {
                    Storage.getStorage().getLogger().warning("Server load cao, bỏ qua auto-save lần này.");
                    return;
                }

                Collection<? extends Player> onlinePlayers = Bukkit.getOnlinePlayers();
                int onlineCount = onlinePlayers.size();

                if (onlineCount == 0) {
                    return; // Không cần save nếu không có người chơi
                }

                // Ưu tiên save cho người chơi đang mining trước
                List<Player> miningPlayers = new ArrayList<>();
                List<Player> otherPlayers = new ArrayList<>();

                for (Player player : onlinePlayers) {
                    if (player != null && player.isOnline()) {
                        if (isPlayerActiveMining(player)) {
                            miningPlayers.add(player);
                        } else {
                            otherPlayers.add(player);
                        }
                    }
                }

                // Save mining players ngay lập tức
                if (!miningPlayers.isEmpty()) {
                    for (Player player : miningPlayers) {
                        try {
                            MineManager.savePlayerDataAsync(player);
                        } catch (Exception e) {
                            Storage.getStorage().getLogger().warning("Lỗi khi auto-save dữ liệu mining cho " + player.getName() + ": " + e.getMessage());
                        }
                    }
                }

                // Điều chỉnh strategy cho người chơi khác dựa trên số lượng và server load
                if (otherPlayers.size() > 25) { // Với nhiều người chơi
                    savePlayersInBatchesFromList(otherPlayers);
                } else if (otherPlayers.size() > 8) { // Với số lượng vừa phải
                    savePlayersInSmallBatchesFromList(otherPlayers);
                } else { // Với ít người chơi
                    if (Storage.getStorage().isDebug()) {
                        Storage.getStorage().getLogger().info("Đang thực hiện auto-save cho " + otherPlayers.size() + " người chơi khác...");
                    }

                    // Auto-save dữ liệu người chơi còn lại
                    for (Player player : otherPlayers) {
                        try {
                            MineManager.savePlayerDataAsync(player);
                        } catch (Exception e) {
                            Storage.getStorage().getLogger().warning("Lỗi khi auto-save dữ liệu cho " + player.getName() + ": " + e.getMessage());
                        }
                    }
                }

                if (Storage.getStorage().isDebug()) {
                    Storage.getStorage().getLogger().info("Hoàn thành auto-save cho " + miningPlayers.size() + " mining players và " + otherPlayers.size() + " người chơi khác.");
                }
            } catch (Exception e) {
                Storage.getStorage().getLogger().severe("Lỗi trong quá trình auto-save: " + e.getMessage());
                e.printStackTrace();
            }
        }, autoSaveInterval, autoSaveInterval);
    }

    /**
     * Lưu dữ liệu người chơi theo batch từ list cụ thể
     */
    private static void savePlayersInBatchesFromList(List<Player> players) {
        if (players.isEmpty()) return;

        int batchSize = Math.max(2, Math.min(8, players.size() / 6));
        int totalBatches = (players.size() + batchSize - 1) / batchSize;

        for (int i = 0; i < players.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, players.size());
            List<Player> batch = players.subList(i, endIndex);
            final int batchIndex = i / batchSize;

            final long delay = batchIndex * 60L; // 3 giây delay giữa các batch

            Bukkit.getScheduler().runTaskLaterAsynchronously(Storage.getStorage(), () -> {
                for (Player player : batch) {
                    if (player != null && player.isOnline()) {
                        try {
                            MineManager.savePlayerDataAsync(player);
                        } catch (Exception e) {
                            Storage.getStorage().getLogger().warning("Lỗi khi auto-save dữ liệu cho " + player.getName() + ": " + e.getMessage());
                        }
                    }
                }
            }, delay);
        }

        if (Storage.getStorage().isDebug()) {
            Storage.getStorage().getLogger().info("Auto-save (batch): Đã lên lịch lưu " + players.size() + " người chơi trong " + totalBatches + " batch");
        }
    }

    /**
     * Lưu người chơi theo batch nhỏ từ list cụ thể
     */
    private static void savePlayersInSmallBatchesFromList(List<Player> players) {
        if (players.isEmpty()) return;

        int batchSize = Math.max(1, Math.min(4, players.size() / 5));
        int totalBatches = (players.size() + batchSize - 1) / batchSize;

        for (int i = 0; i < players.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, players.size());
            List<Player> batch = players.subList(i, endIndex);
            final int batchIndex = i / batchSize;

            final long delay = batchIndex * 80L; // 4 giây delay giữa các batch

            Bukkit.getScheduler().runTaskLaterAsynchronously(Storage.getStorage(), () -> {
                for (Player player : batch) {
                    if (player != null && player.isOnline()) {
                        try {
                            MineManager.savePlayerDataAsync(player);
                        } catch (Exception e) {
                            Storage.getStorage().getLogger().warning("Lỗi khi auto-save dữ liệu cho " + player.getName() + ": " + e.getMessage());
                        }
                    }
                }
            }, delay);
        }

        if (Storage.getStorage().isDebug()) {
            Storage.getStorage().getLogger().info("Auto-save (small batch): Đã lên lịch lưu " + players.size() + " người chơi trong " + totalBatches + " batch nhỏ");
        }
    }

    /**
     * Lưu dữ liệu người chơi theo batch để tránh lag
     */
    private static void savePlayersInBatches(int onlineCount) {
        Collection<? extends Player> onlinePlayers = Bukkit.getOnlinePlayers();
        if (onlinePlayers.isEmpty()) return;

        List<Player> players = new ArrayList<>(onlinePlayers);
        int batchSize = Math.max(2, Math.min(8, onlineCount / 6)); // Tối ưu batch size: 2-8
        int totalBatches = (players.size() + batchSize - 1) / batchSize;

        for (int i = 0; i < players.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, players.size());
            List<Player> batch = players.subList(i, endIndex);
            final int batchIndex = i / batchSize;

            // Tăng delay giữa các batch để giảm tải
            final long delay = batchIndex * 60L; // 3 giây delay giữa các batch

            Bukkit.getScheduler().runTaskLaterAsynchronously(Storage.getStorage(), () -> {
                for (Player player : batch) {
                    if (player != null && player.isOnline()) {
                        try {
                            MineManager.savePlayerDataAsync(player);
                        } catch (Exception e) {
                            Storage.getStorage().getLogger().warning("Lỗi khi auto-save dữ liệu cho " + player.getName() + ": " + e.getMessage());
                        }
                    }
                }
            }, delay);
        }

        if (Storage.getStorage().isDebug()) {
            Storage.getStorage().getLogger().info("Auto-save (batch): Đã lên lịch lưu " + players.size() + " người chơi trong " + totalBatches + " batch");
        }
    }

    /**
     * Lưu người chơi theo batch nhỏ (cho server vừa phải)
     */
    private static void savePlayersInSmallBatches(int onlineCount) {
        Collection<? extends Player> onlinePlayers = Bukkit.getOnlinePlayers();
        if (onlinePlayers.isEmpty()) return;

        List<Player> players = new ArrayList<>(onlinePlayers);
        int batchSize = Math.max(1, Math.min(4, onlineCount / 5)); // Batch size nhỏ hơn: 1-4
        int totalBatches = (players.size() + batchSize - 1) / batchSize;

        for (int i = 0; i < players.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, players.size());
            List<Player> batch = players.subList(i, endIndex);
            final int batchIndex = i / batchSize;

            // Delay lớn hơn giữa các batch để tránh lag
            final long delay = batchIndex * 80L; // 4 giây delay giữa các batch

            Bukkit.getScheduler().runTaskLaterAsynchronously(Storage.getStorage(), () -> {
                for (Player player : batch) {
                    if (player != null && player.isOnline()) {
                        try {
                            MineManager.savePlayerDataAsync(player);
                        } catch (Exception e) {
                            Storage.getStorage().getLogger().warning("Lỗi khi auto-save dữ liệu cho " + player.getName() + ": " + e.getMessage());
                        }
                    }
                }
            }, delay);
        }

        if (Storage.getStorage().isDebug()) {
            Storage.getStorage().getLogger().info("Auto-save (small batch): Đã lên lịch lưu " + players.size() + " người chơi trong " + totalBatches + " batch nhỏ");
        }
    }

    /**
     * Lên lịch sao lưu dữ liệu theo định kỳ
     */
    private static void scheduleDataBackup() {
        // Lấy thời gian từ config (mặc định 1 giờ = 3600 giây)
        long backupInterval = File.getConfig().getLong("backup.interval", 3600) * 20L; // Chuyển đổi sang ticks

        // Kiểm tra xem có giá trị mặc định quá thấp không và áp dụng giá trị tối thiểu
        if (backupInterval < 72000L) { // Tối thiểu 1 giờ (72000 ticks)
            backupInterval = 72000L;
            Storage.getStorage().getLogger().info("Đã thiết lập lại thời gian sao lưu tự động tối thiểu là 1 giờ để tối ưu hiệu suất.");

            // Cập nhật lại giá trị trong config
            if (File.getConfig().getLong("backup.interval", 3600) < 3600) {
                File.getConfig().set("backup.interval", 3600);
                try {
                    File.getConfig().save(new java.io.File(Storage.getStorage().getDataFolder(), "config.yml"));
                } catch (Exception e) {
                    Storage.getStorage().getLogger().warning("Không thể lưu cấu hình với giá trị backup.interval mới: " + e.getMessage());
                }
            }
        }

        // Kiểm tra nếu tính năng sao lưu đã được bật
        boolean backupEnabled = File.getConfig().getBoolean("backup.enabled", true);
        if (!backupEnabled) {
            Storage.getStorage().getLogger().info("Tính năng sao lưu tự động đã bị tắt trong cấu hình.");
            return;
        }

        Storage.getStorage().getLogger().info("Đã thiết lập lịch sao lưu dữ liệu mỗi " + (backupInterval / 20 / 60) + " phút.");

        // Lên lịch sao lưu dữ liệu với độ trễ ban đầu để tránh backup liên tục
        Bukkit.getScheduler().runTaskTimerAsynchronously(Storage.getStorage(), () -> {
            try {
                Collection<? extends Player> onlinePlayers = Bukkit.getOnlinePlayers();
                if (onlinePlayers.isEmpty()) {
                    Storage.getStorage().getLogger().info("Không có người chơi online, bỏ qua backup.");
                    return;
                }

                // Dọn dẹp các file backup cũ trước khi sao lưu mới (chỉ thực hiện định kỳ)
                try {
                    MineManager.cleanupAllBackups();
                } catch (Exception e) {
                    Storage.getStorage().getLogger().warning("Không thể dọn dẹp các file backup cũ: " + e.getMessage());
                }

                Storage.getStorage().getLogger().info("Đang sao lưu dữ liệu của " + onlinePlayers.size() + " người chơi đang online...");

                // Sao lưu dữ liệu theo batch để tránh lag
                List<Player> playerList = new ArrayList<>(onlinePlayers);
                int batchSize = Math.max(3, Math.min(8, playerList.size() / 4));
                int savedCount = 0;

                for (int i = 0; i < playerList.size(); i += batchSize) {
                    int endIndex = Math.min(i + batchSize, playerList.size());
                    List<Player> batch = playerList.subList(i, endIndex);
                    final int batchIndex = i / batchSize;

                    // Delay giữa các batch để tránh lag
                    Bukkit.getScheduler().runTaskLaterAsynchronously(Storage.getStorage(), () -> {
                        for (Player p : batch) {
                            if (p != null && p.isOnline()) {
                                try {
                                    MineManager.savePlayerDataAsync(p);
                                } catch (Exception e) {
                                    Storage.getStorage().getLogger().warning("Lỗi khi sao lưu dữ liệu của " + p.getName() + ": " + e.getMessage());
                                }
                            }
                        }
                    }, batchIndex * 40L); // 2 giây delay giữa các batch

                    savedCount += batch.size();
                }

                Storage.getStorage().getLogger().info("Đã lên lịch sao lưu dữ liệu cho " + savedCount + " người chơi.");

                // Kiểm tra xem có cần sao lưu cơ sở dữ liệu
                boolean backupDatabase = File.getConfig().getBoolean("backup.database", false);
                if (backupDatabase) {
                    // Delay database backup để không xung đột với player backup
                    Bukkit.getScheduler().runTaskLaterAsynchronously(Storage.getStorage(), DataManager::backupDatabase, 200L); // 10 giây sau
                }
            } catch (Exception e) {
                Storage.getStorage().getLogger().warning("Lỗi trong quá trình backup: " + e.getMessage());
            }
        }, 36000L, backupInterval); // Chạy lần đầu sau 30 phút để giảm lag startup
    }

    /**
     * Kiểm tra và xác minh tính nhất quán của dữ liệu trước khi sao lưu
     */
    private static void verifyDataConsistency() {
        Storage.getStorage().getLogger().info("Đang kiểm tra tính nhất quán dữ liệu trước khi sao lưu...");

        try {
            int playerCount = 0;
            int fixedCount = 0;

            for (Player player : Bukkit.getOnlinePlayers()) {
                playerCount++;

                // Đếm số lượng vật phẩm trong bộ nhớ
                int memoryItemCount = 0;
                for (String material : MineManager.getPluginBlocks()) {
                    if (MineManager.hasPlayerBlock(player, material)) {
                        memoryItemCount += MineManager.getPlayerBlock(player, material);
                    }
                }

                // Lấy dữ liệu từ database
                PlayerData dbData = Storage.db.getData(player.getName());

                // Bỏ qua nếu không có dữ liệu
                if (dbData == null) continue;

                // So sánh dữ liệu database với bộ nhớ để phát hiện xung đột
                String dataStr = dbData.getData();
                Map<String, Integer> dbItems = new HashMap<>();
                int dbItemCount = 0;

                // Phân tích chuỗi dữ liệu để đếm số lượng vật phẩm
                if (!dataStr.equals("{}")) {
                    String content = dataStr.substring(1, dataStr.length() - 1);
                    if (!content.isEmpty()) {
                        for (String pair : content.split(", ")) {
                            String[] parts = pair.split("=");
                            if (parts.length == 2) {
                                try {
                                    String material = parts[0];
                                    int count = Integer.parseInt(parts[1]);
                                    dbItems.put(material, count);
                                    dbItemCount += count;
                                } catch (NumberFormatException e) {
                                    // Bỏ qua nếu không phân tích được
                                }
                            }
                        }
                    }
                }

                // Phát hiện sự khác biệt đáng kể (>10 vật phẩm) giữa bộ nhớ và database
                if (Math.abs(memoryItemCount - dbItemCount) > 10) {
                    Storage.getStorage().getLogger().warning("Phát hiện khác biệt dữ liệu đáng kể cho " + player.getName() + ": Memory=" + memoryItemCount + ", DB=" + dbItemCount);

                    if (memoryItemCount > dbItemCount) {
                        // Nếu bộ nhớ có nhiều vật phẩm hơn, lưu dữ liệu xuống database
                        Storage.getStorage().getLogger().info("Đồng bộ hóa dữ liệu bộ nhớ -> database cho " + player.getName());
                        MineManager.savePlayerData(player);
                    } else if (dbItemCount > memoryItemCount) {
                        // Nếu database có nhiều vật phẩm hơn, tải lại dữ liệu từ database
                        Storage.getStorage().getLogger().info("Đồng bộ hóa dữ liệu database -> bộ nhớ cho " + player.getName());
                        MineManager.loadPlayerData(player);
                    }

                    fixedCount++;
                }
            }

            if (fixedCount > 0) {
                Storage.getStorage().getLogger().info("Đã sửa chữa dữ liệu cho " + fixedCount + "/" + playerCount + " người chơi.");
            } else {
                Storage.getStorage().getLogger().info("Dữ liệu của tất cả " + playerCount + " người chơi đều nhất quán. Tiếp tục sao lưu.");
            }

        } catch (Exception e) {
            Storage.getStorage().getLogger().severe("Lỗi khi kiểm tra tính nhất quán dữ liệu: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Sao lưu cơ sở dữ liệu SQLite
     */
    private static void backupDatabase() {
        if (!(Storage.db instanceof SQLite)) {
            return;
        }

        try {
            Storage.getStorage().getLogger().info("Đang sao lưu cơ sở dữ liệu...");

            String dbName = "PlayerData";
            String dbPath = Storage.getStorage().getDataFolder() + "/" + dbName + ".db";

            // Kiểm tra file tồn tại trước khi sao lưu
            java.io.File dbFile = new java.io.File(dbPath);
            if (!dbFile.exists()) {
                Storage.getStorage().getLogger().severe("Không thể sao lưu cơ sở dữ liệu: File " + dbPath + " không tồn tại!");

                // Ghi log thêm thông tin về thư mục để kiểm tra
                Storage.getStorage().getLogger().severe("Thông tin debug:");
                Storage.getStorage().getLogger().severe("- Đường dẫn: " + dbFile.getAbsolutePath());
                Storage.getStorage().getLogger().severe("- Thư mục: " + Storage.getStorage().getDataFolder().exists());
                Storage.getStorage().getLogger().severe("- Nội dung thư mục:");

                java.io.File[] files = Storage.getStorage().getDataFolder().listFiles();
                if (files != null) {
                    for (java.io.File file : files) {
                        Storage.getStorage().getLogger().severe("  + " + file.getName() + " (" + file.length() + " bytes)");
                    }
                } else {
                    Storage.getStorage().getLogger().severe("  (Không thể liệt kê files)");
                }

                return;
            }

            // Tạo thư mục sao lưu nếu nó không tồn tại
            java.io.File backupDir = new java.io.File(Storage.getStorage().getDataFolder(), "backups");
            if (!backupDir.exists()) {
                backupDir.mkdirs();
            }

            // Tạo tên tệp sao lưu với nhãn thời gian
            java.text.SimpleDateFormat dateFormat = new java.text.SimpleDateFormat("yyyyMMdd_HHmmss");
            String timestamp = dateFormat.format(new java.util.Date());
            String backupFile = backupDir.getPath() + "/" + dbName + "_" + timestamp + ".db";

            // Sao lưu tệp
            java.io.File source = new java.io.File(dbPath);
            java.io.File dest = new java.io.File(backupFile);

            // Sao chép tệp
            java.nio.file.Files.copy(source.toPath(), dest.toPath(), java.nio.file.StandardCopyOption.REPLACE_EXISTING);

            Storage.getStorage().getLogger().info("Đã sao lưu cơ sở dữ liệu thành công: " + backupFile);

            // Xóa các bản sao lưu cũ (giữ 10 bản mới nhất)
            cleanupOldBackups(backupDir, 10);

        } catch (Exception e) {
            Storage.getStorage().getLogger().severe("Lỗi khi sao lưu cơ sở dữ liệu: " + e.getMessage());

            // Thêm thông tin chi tiết về lỗi
            if (e instanceof java.nio.file.NoSuchFileException) {
                Storage.getStorage().getLogger().severe("File database không tồn tại tại đường dẫn được chỉ định. Kiểm tra lại tên và vị trí file.");
                Storage.getStorage().getLogger().severe("Đường dẫn: " + ((java.nio.file.NoSuchFileException) e).getFile());
            }

            e.printStackTrace();
        }
    }

    /**
     * Xóa các bản sao lưu cũ
     *
     * @param backupDir Thư mục sao lưu
     * @param keepCount Số lượng bản sao lưu cần giữ lại
     */
    private static void cleanupOldBackups(java.io.File backupDir, int keepCount) {
        // Lấy danh sách tất cả các tệp sao lưu
        java.io.File[] backups = backupDir.listFiles((dir, name) -> name.startsWith("storage_") && name.endsWith(".db"));

        if (backups == null || backups.length <= keepCount) {
            return;
        }

        // Sắp xếp theo thời gian sửa đổi (mới nhất đầu tiên)
        Arrays.sort(backups, (f1, f2) -> Long.compare(f2.lastModified(), f1.lastModified()));

        // Xóa các tệp cũ
        for (int i = keepCount; i < backups.length; i++) {
            if (backups[i].delete()) {
                Storage.getStorage().getLogger().info("Đã xóa bản sao lưu cũ: " + backups[i].getName());
            }
        }
    }

    /**
     * Lên lịch kiểm tra tính toàn vẹn dữ liệu theo định kỳ
     */
    private static void scheduleDataIntegrityCheck() {
        // Kiểm tra mỗi 45 phút = 54000 ticks (tăng để giảm tải)
        long checkInterval = 54000L;
        boolean enableIntegrityCheck = Storage.getStorage().getConfig().getBoolean("settings.enable_integrity_check", false);

        if (!enableIntegrityCheck) {
            Storage.getStorage().getLogger().info("Tính năng kiểm tra toàn vẹn dữ liệu đã bị tắt trong cấu hình.");
            return;
        }

        Storage.getStorage().getLogger().info("Đã lập lịch kiểm tra tính toàn vẹn dữ liệu mỗi 45 phút.");

        Bukkit.getScheduler().runTaskTimerAsynchronously(Storage.getStorage(), () -> {
            try {
                Collection<? extends Player> onlinePlayers = Bukkit.getOnlinePlayers();
                if (onlinePlayers.isEmpty()) {
                    return; // Không cần kiểm tra nếu không có người chơi
                }

                Storage.getStorage().getLogger().info("Đang kiểm tra tính toàn vẹn dữ liệu cho " + onlinePlayers.size() + " người chơi...");

                int playerCount = 0;
                int fixedCount = 0;
                int errorCount = 0;
                List<String> errorPlayers = new ArrayList<>();

                // Giới hạn số lượng người chơi kiểm tra mỗi lần để tránh lag
                int maxPlayersPerCheck = Math.min(20, onlinePlayers.size());
                List<Player> allPlayers = new ArrayList<>(onlinePlayers);
                List<Player> playersToCheck = allPlayers.subList(0, maxPlayersPerCheck);

                // Duyệt qua người chơi được chọn
                for (Player player : playersToCheck) {
                    if (player == null || !player.isOnline()) continue;
                    playerCount++;

                    try {
                        // Kiểm tra dữ liệu trên RAM vs Database
                        PlayerData dbData = Storage.db.getData(player.getName());
                        if (dbData == null) {
                            continue; // Bỏ qua nếu không có dữ liệu
                        }

                        // Đếm tổng số vật phẩm trong database
                        int dbItemCount = 0;
                        String dbDataStr = dbData.getData();
                        if (!dbDataStr.equals("{}")) {
                            String content = dbDataStr.substring(1, dbDataStr.length() - 1);
                            if (!content.isEmpty()) {
                                String[] pairs = content.split(", ");
                                for (String pair : pairs) {
                                    String[] keyValue = pair.split("=");
                                    if (keyValue.length == 2) {
                                        try {
                                            dbItemCount += Integer.parseInt(keyValue[1]);
                                        } catch (NumberFormatException e) {
                                            // Bỏ qua định dạng không hợp lệ
                                        }
                                    }
                                }
                            }
                        }

                        // Đếm tổng số vật phẩm trong bộ nhớ
                        int memItemCount = 0;
                        for (String material : MineManager.getPluginBlocks()) {
                            if (MineManager.hasPlayerBlock(player, material)) {
                                memItemCount += MineManager.getPlayerBlock(player, material);
                            }
                        }

                        // Phát hiện sự khác biệt đáng kể (tăng ngưỡng để giảm false positive)
                        if (Math.abs(dbItemCount - memItemCount) > 50) {
                            Storage.getStorage().getLogger().warning("Phát hiện sự khác biệt dữ liệu cho " + player.getName() + ": DB=" + dbItemCount + ", Memory=" + memItemCount);

                            // Chỉ sửa nếu sự khác biệt thực sự lớn
                            if (Math.abs(dbItemCount - memItemCount) > 100) {
                                if (dbItemCount > memItemCount) {
                                    // Nếu DB có nhiều dữ liệu hơn, tải dữ liệu từ DB
                                    Storage.getStorage().getLogger().info("Đang khôi phục dữ liệu từ database cho " + player.getName());
                                    Bukkit.getScheduler().runTaskLaterAsynchronously(Storage.getStorage(), () -> MineManager.loadPlayerData(player), 20L);
                                    fixedCount++;
                                } else {
                                    // Nếu bộ nhớ có nhiều dữ liệu hơn, lưu dữ liệu xuống DB
                                    Storage.getStorage().getLogger().info("Đang lưu dữ liệu bộ nhớ xuống database cho " + player.getName());
                                    Bukkit.getScheduler().runTaskLaterAsynchronously(Storage.getStorage(), () -> MineManager.savePlayerData(player), 20L);
                                    fixedCount++;
                                }
                            }
                        }
                    } catch (Exception e) {
                        Storage.getStorage().getLogger().warning("Lỗi khi kiểm tra dữ liệu cho " + player.getName() + ": " + e.getMessage());
                        errorCount++;
                        errorPlayers.add(player.getName());
                    }

                    // Thêm delay nhỏ giữa các lần kiểm tra để tránh lag
                    if (playerCount % 5 == 0) {
                        try {
                            Thread.sleep(50); // 50ms delay mỗi 5 người chơi
                        } catch (InterruptedException e) {
                            Thread.currentThread().interrupt();
                            break;
                        }
                    }
                }

                Storage.getStorage().getLogger().info("Đã kiểm tra toàn vẹn dữ liệu cho " + playerCount + "/" + onlinePlayers.size() + " người chơi. Đã sửa: " + fixedCount + ", Lỗi: " + errorCount);

                if (!errorPlayers.isEmpty()) {
                    Storage.getStorage().getLogger().warning("Danh sách người chơi gặp lỗi: " + String.join(", ", errorPlayers));
                }

            } catch (Exception e) {
                Storage.getStorage().getLogger().severe("Lỗi trong quá trình kiểm tra toàn vẹn dữ liệu: " + e.getMessage());
                e.printStackTrace();
            }
        }, 72000L, checkInterval); // Bắt đầu sau 60 phút để tránh lag startup
    }

    /**
     * Lên lịch đồng bộ real-time cho mining để đảm bảo không có delay nhận item
     */
    private static void scheduleRealTimeSync() {
        // Sync nhanh mỗi 30 giây cho người chơi đang mining
        Bukkit.getScheduler().runTaskTimerAsynchronously(Storage.getStorage(), () -> {
            try {
                Collection<? extends Player> onlinePlayers = Bukkit.getOnlinePlayers();
                if (onlinePlayers.isEmpty()) return;

                // Chỉ sync cho người chơi có hoạt động mining gần đây
                for (Player player : onlinePlayers) {
                    if (player != null && player.isOnline()) {
                        // Kiểm tra xem người chơi có đang mining không
                        if (isPlayerActiveMining(player)) {
                            try {
                                // Sync nhanh chỉ cho người chơi đang mining
                                MineManager.savePlayerDataAsync(player);
                            } catch (Exception e) {
                                // Bỏ qua lỗi để không ảnh hưởng performance
                            }
                        }
                    }
                }
            } catch (Exception e) {
                // Bỏ qua lỗi để không ảnh hưởng performance
            }
        }, 600L, 600L); // Mỗi 30 giây (600 ticks)
    }

    /**
     * Kiểm tra xem người chơi có đang mining không dựa trên metadata hoặc activity
     */
    private static boolean isPlayerActiveMining(Player player) {
        try {
            // Kiểm tra xem người chơi có toggle bật không
            if (!MineManager.toggle.getOrDefault(player, false)) {
                return false;
            }

            // Kiểm tra xem người chơi có đang cầm tool không
            if (player.getItemInHand() != null) {
                String itemName = player.getItemInHand().getType().name();
                if (itemName.contains("PICKAXE") || itemName.contains("SHOVEL") || itemName.contains("AXE") || itemName.contains("HOE")) {
                    return true;
                }
            }

            // Fallback: luôn sync nếu có toggle bật
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * Kiểm tra tải server để điều chỉnh batch size động
     */
    private static int getOptimalBatchSize(int playerCount) {
        // Điều chỉnh batch size dựa trên số lượng người chơi và memory usage
        Runtime runtime = Runtime.getRuntime();
        long usedMemory = runtime.totalMemory() - runtime.freeMemory();
        long maxMemory = runtime.maxMemory();
        double memoryUsage = (double) usedMemory / maxMemory;

        // Nếu memory usage cao, giảm batch size
        if (memoryUsage > 0.8) {
            return Math.max(1, playerCount / 10);
        } else if (memoryUsage > 0.6) {
            return Math.max(2, playerCount / 8);
        } else {
            return Math.max(3, playerCount / 6);
        }
    }

    /**
     * Kiểm tra xem có nên thực hiện operation không dựa trên server load
     */
    private static boolean shouldPerformOperation() {
        Runtime runtime = Runtime.getRuntime();
        long usedMemory = runtime.totalMemory() - runtime.freeMemory();
        long maxMemory = runtime.maxMemory();
        double memoryUsage = (double) usedMemory / maxMemory;

        // Không thực hiện nếu memory usage quá cao
        return memoryUsage < 0.9;
    }

    /**
     * Force save cho người chơi đang mining để đảm bảo không mất item
     */
    public static void forceSaveMiningPlayer(Player player) {
        if (player == null || !player.isOnline()) return;

        try {
            // Kiểm tra xem có đang mining không
            if (isPlayerActiveMining(player)) {
                // Force save ngay lập tức
                Bukkit.getScheduler().runTaskAsynchronously(Storage.getStorage(), () -> {
                    try {
                        MineManager.savePlayerData(player); // Sử dụng sync version để đảm bảo
                    } catch (Exception e) {
                        Storage.getStorage().getLogger().warning("Lỗi khi force save mining player " + player.getName() + ": " + e.getMessage());
                    }
                });
            }
        } catch (Exception e) {
            // Bỏ qua lỗi
        }
    }

    /**
     * Kiểm tra và save nhanh cho người chơi có nhiều hoạt động mining
     */
    public static void checkAndSaveActiveMiner(Player player, int mineCount) {
        if (player == null || !player.isOnline()) return;

        // Nếu đào nhiều hơn 100 blocks, save ngay để tránh mất data
        if (mineCount > 100) {
            forceSaveMiningPlayer(player);
        }
    }
}
