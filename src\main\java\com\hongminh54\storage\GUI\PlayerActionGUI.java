package com.hongminh54.storage.GUI;

import com.hongminh54.storage.GUI.manager.IGUI;
import com.hongminh54.storage.Handler.ResourceInputChatHandler;
import com.hongminh54.storage.Manager.SoundManager;
import com.hongminh54.storage.NMS.NMSAssistant;
import com.hongminh54.storage.Storage;
import com.hongminh54.storage.Utils.Chat;
import com.hongminh54.storage.Utils.File;
import com.hongminh54.storage.Utils.GUIText;
import com.hongminh54.storage.compatibility.AdvancedCompatibility;
import com.hongminh54.storage.compatibility.InventoryCompatibility;
import com.hongminh54.storage.compatibility.MaterialCompatibility;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.Sound;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.HandlerList;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.InventoryCloseEvent;
import org.bukkit.event.inventory.InventoryDragEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.jetbrains.annotations.NotNull;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Giao diện thao tác với người chơi được chọn
 */
public class PlayerActionGUI implements IGUI, Listener {

    // Cache cho performance
    private static final Map<String, ItemStack> decorativeItemCache = new HashMap<>();
    private static final Map<String, Long> cacheTimestamps = new HashMap<>();
    private static final long CACHE_DURATION = 300000; // 5 phút
    // NMS Assistant cho version checking
    private static final NMSAssistant nmsAssistant = new NMSAssistant();
    private final Player sender;
    private final Player target;
    private final FileConfiguration config;
    private Inventory inventory;
    private boolean listenerRegistered = false;

    /**
     * Khởi tạo giao diện thao tác với người chơi
     *
     * @param sender Người gửi
     * @param target Người nhận
     */
    public PlayerActionGUI(Player sender, Player target) {
        this.sender = sender;
        this.target = target;
        this.config = File.getGUIConfig("transfer");

        // Validate config trước khi sử dụng
        if (!validateConfig()) {
            Storage.getStorage().getLogger().warning("Config transfer.yml có vấn đề cho PlayerActionGUI");
        }

        // Đăng ký Listener khi tạo GUI
        registerListener();
    }

    /**
     * Xóa cache cũ để tối ưu bộ nhớ
     */
    public static void clearExpiredCache() {
        long currentTime = System.currentTimeMillis();
        cacheTimestamps.entrySet().removeIf(entry -> {
            if ((currentTime - entry.getValue()) >= CACHE_DURATION) {
                decorativeItemCache.remove(entry.getKey());
                return true;
            }
            return false;
        });
    }

    /**
     * Đăng ký listener cho GUI
     */
    private void registerListener() {
        if (!listenerRegistered) {
            Bukkit.getPluginManager().registerEvents(this, Storage.getStorage());
            listenerRegistered = true;
        }
    }

    /**
     * Hủy đăng ký listener
     */
    private void unregisterListener() {
        if (listenerRegistered) {
            HandlerList.unregisterAll(this);
            listenerRegistered = false;
        }
    }

    @NotNull
    @Override
    public Inventory getInventory() {
        // Đọc cấu hình từ YAML
        String title = config.getString("player_action_gui.title", "&8Thao tác với &a{target}")
                .replace("{target}", target.getName());
        int size = config.getInt("player_action_gui.size", 27);
        inventory = Bukkit.createInventory(sender, size, GUIText.format(title));

        // Thêm viền trang trí từ config
        addDecorativeItems();

        // Thêm các nút chức năng từ config
        addFunctionButtons();

        return inventory;
    }

    /**
     * Validate config để đảm bảo các section cần thiết tồn tại
     *
     * @return true nếu config hợp lệ
     */
    private boolean validateConfig() {
        try {
            // Kiểm tra section chính
            if (config.getConfigurationSection("player_action_gui") == null) {
                Storage.getStorage().getLogger().severe("Không tìm thấy section 'player_action_gui' trong transfer.yml!");
                return false;
            }

            // Kiểm tra các section con cần thiết
            String[] requiredSections = {
                    "player_action_gui.buttons.transfer_single",
                    "player_action_gui.buttons.transfer_multi"
            };

            for (String section : requiredSections) {
                if (config.get(section) == null) {
                    Storage.getStorage().getLogger().warning("Không tìm thấy section '" + section + "' trong transfer.yml");
                }
            }

            return true;
        } catch (Exception e) {
            Storage.getStorage().getLogger().severe("Lỗi khi validate config transfer.yml: " + e.getMessage());
            return false;
        }
    }

    /**
     * Xử lý lỗi và hiển thị thông báo cho người chơi
     *
     * @param error        Thông điệp lỗi
     * @param showToPlayer Có hiển thị cho người chơi không
     */
    private void handleError(String error, boolean showToPlayer) {
        Storage.getStorage().getLogger().warning("PlayerActionGUI Error: " + error);
        // Chỉ ghi log mà không hiển thị tin nhắn lỗi cho người chơi
    }

    /**
     * Thêm các item trang trí từ config với error handling
     */
    private void addDecorativeItems() {
        try {
            String decorateSlots = config.getString("player_action_gui.decorates.slot", "");
            if (!decorateSlots.isEmpty()) {
                String[] slots = decorateSlots.split(", ");

                // Tạo item trang trí với compatibility
                ItemStack borderItem = createCompatibleDecorativeItem();

                // Đặt item vào các slot với validation
                for (String slotStr : slots) {
                    try {
                        int slot = Integer.parseInt(slotStr.trim());
                        if (isValidSlot(slot)) {
                            inventory.setItem(slot, borderItem);
                        } else {
                            handleError("Invalid slot: " + slot, false);
                        }
                    } catch (NumberFormatException e) {
                        handleError("Invalid slot format: " + slotStr, false);
                    }
                }
            }
        } catch (Exception e) {
            handleError("Failed to add decorative items: " + e.getMessage(), false);
        }
    }

    /**
     * Tạo item trang trí tương thích với các phiên bản Minecraft với cache
     *
     * @return ItemStack trang trí
     */
    private ItemStack createCompatibleDecorativeItem() {
        try {
            String materialName = config.getString("player_action_gui.decorates.material", "GRAY_STAINED_GLASS_PANE");
            String displayName = config.getString("player_action_gui.decorates.name", "&r");
            String cacheKey = materialName + ":" + displayName;

            // Kiểm tra cache
            if (isCacheValid(cacheKey)) {
                return decorativeItemCache.get(cacheKey).clone();
            }

            ItemStack borderItem;
            if (MaterialCompatibility.isPre113()) {
                borderItem = new ItemStack(Material.valueOf("STAINED_GLASS_PANE"), 1, (byte) 7); // GRAY
            } else {
                try {
                    borderItem = new ItemStack(Material.valueOf(materialName));
                } catch (IllegalArgumentException e) {
                    borderItem = new ItemStack(Material.GRAY_STAINED_GLASS_PANE);
                    handleError("Unknown material: " + materialName + ", using default", false);
                }
            }

            ItemMeta borderMeta = borderItem.getItemMeta();
            if (borderMeta != null) {
                borderMeta.setDisplayName(Chat.colorize(displayName));

                // Áp dụng custom model data nếu có và phiên bản hỗ trợ (1.14+)
                applyCustomModelData(borderMeta, "player_action_gui.decorates.custom-model-data");

                borderItem.setItemMeta(borderMeta);
            }

            // Cache item
            decorativeItemCache.put(cacheKey, borderItem.clone());
            cacheTimestamps.put(cacheKey, System.currentTimeMillis());

            return borderItem;
        } catch (Exception e) {
            handleError("Failed to create decorative item: " + e.getMessage(), false);
            // Fallback item
            return new ItemStack(Material.STONE);
        }
    }

    /**
     * Kiểm tra cache có hợp lệ không
     *
     * @param cacheKey Key cache
     * @return true nếu cache hợp lệ
     */
    private boolean isCacheValid(String cacheKey) {
        if (!decorativeItemCache.containsKey(cacheKey) || !cacheTimestamps.containsKey(cacheKey)) {
            return false;
        }

        long timestamp = cacheTimestamps.get(cacheKey);
        return (System.currentTimeMillis() - timestamp) < CACHE_DURATION;
    }

    /**
     * Áp dụng custom model data cho ItemMeta nếu phiên bản hỗ trợ
     *
     * @param meta       ItemMeta cần áp dụng
     * @param configPath Đường dẫn config cho custom model data
     */
    private void applyCustomModelData(ItemMeta meta, String configPath) {
        if (meta == null) return;

        try {
            // Kiểm tra phiên bản có hỗ trợ Custom Model Data không (1.14+)
            if (MaterialCompatibility.isPre113() || nmsAssistant.isVersionLessThan(14)) {
                return; // Không hỗ trợ trong phiên bản cũ
            }

            // Đọc custom model data từ config
            if (config.contains(configPath)) {
                int customModelData = config.getInt(configPath, -1);
                if (customModelData > 0) {
                    // Sử dụng AdvancedCompatibility để set custom model data an toàn
                    AdvancedCompatibility.setCustomModelData(meta, customModelData);

                    if (Storage.getStorage().isDebug()) {
                        Storage.getStorage().getLogger().info("Đã áp dụng CustomModelData " + customModelData + " cho " + configPath);
                    }
                }
            }
        } catch (Exception e) {
            handleError("Failed to apply custom model data: " + e.getMessage(), false);
        }
    }

    /**
     * Tạo ItemStack với custom model data support
     *
     * @param material        Material của item
     * @param name            Tên hiển thị
     * @param lore            Mô tả item
     * @param customModelData Custom model data (0 = không áp dụng)
     * @return ItemStack đã được tùy chỉnh
     */
    private ItemStack createItemWithCustomModel(Material material, String name, List<String> lore, int customModelData) {
        try {
            ItemStack item = new ItemStack(material);
            ItemMeta meta = item.getItemMeta();

            if (meta != null) {
                // Set name và lore
                meta.setDisplayName(Chat.colorize(name.replace("{target}", target.getName())));

                if (lore != null && !lore.isEmpty()) {
                    meta.setLore(lore.stream()
                            .map(line -> Chat.colorize(line.replace("{target}", target.getName())))
                            .collect(java.util.stream.Collectors.toList()));
                }

                // Áp dụng custom model data
                if (customModelData > 0) {
                    // Sử dụng AdvancedCompatibility để set custom model data an toàn
                    AdvancedCompatibility.setCustomModelData(meta, customModelData);
                }

                item.setItemMeta(meta);
            }

            return item;
        } catch (Exception e) {
            handleError("Failed to create item with custom model: " + e.getMessage(), false);
            return new ItemStack(material);
        }
    }

    /**
     * Kiểm tra slot có hợp lệ không
     *
     * @param slot Slot cần kiểm tra
     * @return true nếu hợp lệ
     */
    private boolean isValidSlot(int slot) {
        return slot >= 0 && slot < inventory.getSize();
    }

    /**
     * Thêm các nút chức năng từ config
     */
    private void addFunctionButtons() {
        // Nút chuyển một loại tài nguyên
        addTransferSingleButton();

        // Nút chuyển nhiều loại tài nguyên
        addTransferMultiButton();
    }

    /**
     * Thêm nút chuyển một loại tài nguyên với error handling
     */
    private void addTransferSingleButton() {
        try {
            int slot = config.getInt("player_action_gui.buttons.transfer_single.slot", 12);
            String materialName = config.getString("player_action_gui.buttons.transfer_single.material", "CHEST");
            String name = config.getString("player_action_gui.buttons.transfer_single.name", "&aChuyển một loại tài nguyên");
            List<String> lore = config.getStringList("player_action_gui.buttons.transfer_single.lore");

            // Validate slot
            if (!isValidSlot(slot)) {
                handleError("Invalid transfer_single slot: " + slot, false);
                return;
            }

            // Tạo item với compatibility
            ItemStack item = createCompatibleButton(materialName, Material.CHEST, name, lore);
            inventory.setItem(slot, item);

        } catch (Exception e) {
            handleError("Failed to add transfer single button: " + e.getMessage(), false);
        }
    }

    /**
     * Thêm nút chuyển nhiều loại tài nguyên với error handling
     */
    private void addTransferMultiButton() {
        try {
            int slot = config.getInt("player_action_gui.buttons.transfer_multi.slot", 14);
            String materialName = config.getString("player_action_gui.buttons.transfer_multi.material", "ENDER_CHEST");
            String name = config.getString("player_action_gui.buttons.transfer_multi.name", "&aChuyển nhiều loại tài nguyên");
            List<String> lore = config.getStringList("player_action_gui.buttons.transfer_multi.lore");

            // Validate slot
            if (!isValidSlot(slot)) {
                handleError("Invalid transfer_multi slot: " + slot, false);
                return;
            }

            // Tạo item với compatibility
            Material fallbackMaterial = com.hongminh54.storage.compatibility.MaterialCompatibility.getMaterial("ENDER_CHEST");
            ItemStack item = createCompatibleButton(materialName, fallbackMaterial, name, lore);
            inventory.setItem(slot, item);

        } catch (Exception e) {
            handleError("Failed to add transfer multi button: " + e.getMessage(), false);
        }
    }

    /**
     * Tạo button tương thích với các phiên bản Minecraft
     *
     * @param materialName     Tên material từ config
     * @param fallbackMaterial Material dự phòng
     * @param name             Tên button
     * @param lore             Mô tả button
     * @return ItemStack button
     */
    private ItemStack createCompatibleButton(String materialName, Material fallbackMaterial, String name, List<String> lore) {
        try {
            // Tạo material
            Material material;
            try {
                material = Material.valueOf(materialName);
            } catch (IllegalArgumentException e) {
                material = fallbackMaterial;
                handleError("Unknown material: " + materialName + ", using fallback", false);
            }

            ItemStack item = new ItemStack(material);
            ItemMeta meta = item.getItemMeta();

            if (meta != null) {
                // Set name với placeholder replacement
                meta.setDisplayName(Chat.colorize(name.replace("{target}", target.getName())));

                // Set lore với placeholder replacement
                if (lore != null && !lore.isEmpty()) {
                    meta.setLore(lore.stream()
                            .map(line -> Chat.colorize(line.replace("{target}", target.getName())))
                            .collect(java.util.stream.Collectors.toList()));
                }

                // Áp dụng custom model data cho buttons
                String configPath = materialName.equals("CHEST") ?
                        "player_action_gui.buttons.transfer_single.custom-model-data" :
                        "player_action_gui.buttons.transfer_multi.custom-model-data";
                applyCustomModelData(meta, configPath);

                item.setItemMeta(meta);
            }

            return item;
        } catch (Exception e) {
            handleError("Failed to create compatible button: " + e.getMessage(), false);
            // Return fallback item
            return new ItemStack(fallbackMaterial);
        }
    }

    /**
     * Xử lý sự kiện click vào inventory
     */
    @EventHandler(priority = EventPriority.HIGHEST)
    public void onInventoryClick(InventoryClickEvent e) {
        // Kiểm tra nếu không phải GUI này thì không xử lý
        Inventory topInventory = InventoryCompatibility.getTopInventorySafely(e);
        if (topInventory == null || !topInventory.equals(inventory)) {
            return;
        }

        // Luôn hủy tất cả sự kiện click (kể cả bottom inventory) để ngăn lấy item
        e.setCancelled(true);

        // Cập nhật inventory ngay lập tức để đảm bảo thay đổi được áp dụng
        if (e.getWhoClicked() instanceof Player) {
            ((Player) e.getWhoClicked()).updateInventory();
        }

        // Nếu không phải Player thì không xử lý
        if (!(e.getWhoClicked() instanceof Player)) {
            return;
        }

        Player clicker = (Player) e.getWhoClicked();

        // Kiểm tra xem người chơi đích còn online không
        if (!target.isOnline()) {
            clicker.sendMessage(Chat.colorize("&8[&4&l✕&8] &cHiện người chơi &f" + target.getName() + " &ckhông còn online!"));
            SoundManager.playSound(clicker, Sound.ENTITY_VILLAGER_NO, 0.5f, 1.0f);
            clicker.closeInventory();
            return;
        }

        // Kiểm tra nếu click vào inventory phía dưới hoặc không có item, bỏ qua
        if (e.getClickedInventory() == null || !e.getClickedInventory().equals(inventory) ||
                e.getCurrentItem() == null || e.getCurrentItem().getType() == Material.AIR) {
            return;
        }

        // Xử lý các nút chức năng
        int slot = e.getRawSlot();

        // Debug thông tin click
        if (Storage.getStorage().isDebug()) {
            Storage.getStorage().getLogger().info("Player " + clicker.getName() + " clicked on slot " + slot +
                    " in PlayerActionGUI, item: " + (e.getCurrentItem() != null ? e.getCurrentItem().getType().name() : "null"));
        }

        // Lấy slot từ config
        int transferSingleSlot = config.getInt("player_action_gui.buttons.transfer_single.slot", 12);
        int transferMultiSlot = config.getInt("player_action_gui.buttons.transfer_multi.slot", 14);

        if (slot == transferSingleSlot) {
            // Chuyển một loại tài nguyên
            String soundConfig = config.getString("player_action_gui.buttons.transfer_single.sound", "UI_BUTTON_CLICK:0.5:1.0");
            SoundManager.playSoundFromConfig(clicker, soundConfig);

            // Lưu trữ tham chiếu đến target để sử dụng trong lambda
            final Player finalTarget = target;

            // Đóng inventory trước khi mở khung nhập
            clicker.closeInventory();

            // Chờ 3 tick trước khi hiển thị khung nhập để đảm bảo inventory đã đóng hoàn toàn
            Bukkit.getScheduler().runTaskLater(Storage.getStorage(), () -> {
                if (!clicker.isOnline() || !finalTarget.isOnline()) {
                    if (clicker.isOnline()) {
                        clicker.sendMessage(Chat.colorize("&8[&4&l✕&8] &cNgười chơi &f" + finalTarget.getName() + " &ckhông còn trực tuyến!"));
                        com.hongminh54.storage.compatibility.SoundCompatibility.playSound(clicker, "ENTITY_VILLAGER_NO", 0.5f, 1.0f);
                    }
                    return;
                }

                // Người dùng nhập tên tài nguyên sử dụng ResourceInputChatHandler
                ResourceInputChatHandler.startResourceInput(
                        clicker,
                        File.getMessage().getString("user.action.transfer.resource_input", "&8[&d&l⛏&8] &dNhập tên khoáng sản để chuyển:"),
                        (material) -> {
                            if (material == null || material.isEmpty()) {
                                clicker.sendMessage(Chat.colorize("&8[&c&l✕&8] &cĐã hủy thao tác chuyển tài nguyên."));
                                return;
                            }

                            // Kiểm tra lại xem người nhận có online không
                            if (!finalTarget.isOnline()) {
                                clicker.sendMessage(Chat.colorize("&8[&4&l✕&8] &cNgười chơi &f" + finalTarget.getName() + " &ckhông còn trực tuyến!"));
                                SoundManager.playSound(clicker, Sound.ENTITY_VILLAGER_NO, 0.5f, 1.0f);
                                return;
                            }

                            // Mở giao diện chuyển đơn sau một khoảng thời gian ngắn
                            Bukkit.getScheduler().runTaskLater(Storage.getStorage(), () -> {
                                if (clicker.isOnline() && finalTarget.isOnline()) {
                                    clicker.openInventory(new TransferGUI(clicker, finalTarget, material).getInventory());
                                } else if (clicker.isOnline()) {
                                    clicker.sendMessage(Chat.colorize("&8[&4&l✕&8] &cNgười chơi &f" + finalTarget.getName() + " &ckhông còn trực tuyến!"));
                                    com.hongminh54.storage.compatibility.SoundCompatibility.playSound(clicker, "ENTITY_VILLAGER_NO", 0.5f, 1.0f);
                                }
                            }, 3L);
                        }
                );
            }, 3L);

        } else if (slot == transferMultiSlot) {
            // Chuyển nhiều loại tài nguyên
            String soundConfig = config.getString("player_action_gui.buttons.transfer_multi.sound", "UI_BUTTON_CLICK:0.5:1.0");
            SoundManager.playSoundFromConfig(clicker, soundConfig);

            // Lưu trữ tham chiếu đến target để sử dụng trong lambda
            final Player finalTarget = target;

            // Đóng inventory trước khi mở GUI mới
            clicker.closeInventory();

            // Chờ 3 tick trước khi mở GUI mới để đảm bảo inventory đã đóng hoàn toàn
            Bukkit.getScheduler().runTaskLater(Storage.getStorage(), () -> {
                // Kiểm tra lại xem người nhận có online không
                if (!clicker.isOnline() || !finalTarget.isOnline()) {
                    if (clicker.isOnline()) {
                        clicker.sendMessage(Chat.colorize("&8[&4&l✕&8] &cNgười chơi &f" + finalTarget.getName() + " &ckhông còn trực tuyến!"));
                        SoundManager.playSound(clicker, Sound.ENTITY_VILLAGER_NO, 0.5f, 1.0f);
                    }
                    return;
                }

                // Debug log trước khi mở GUI
                if (Storage.getStorage().isDebug()) {
                    Storage.getStorage().getLogger().info("Opening MultiTransferGUI for " + clicker.getName() + " to " + finalTarget.getName());
                }

                // Mở giao diện chuyển nhiều loại
                clicker.openInventory(new MultiTransferGUI(clicker, finalTarget).getInventory());
            }, 3L);
        }
    }

    /**
     * Xử lý sự kiện đóng inventory
     */
    @EventHandler
    public void onInventoryClose(InventoryCloseEvent e) {
        if (e.getInventory().equals(inventory)) {
            // Hủy đăng ký listener khi đóng inventory
            Bukkit.getScheduler().runTaskLater(Storage.getStorage(), this::unregisterListener, 1L);
        }
    }

    /**
     * Xử lý sự kiện kéo vật phẩm trong inventory
     * Ngăn người chơi kéo vật phẩm trong GUI
     */
    @EventHandler(priority = EventPriority.HIGHEST)
    public void onInventoryDrag(InventoryDragEvent e) {
        Inventory topInventory = InventoryCompatibility.getTopInventorySafely(e);
        if (topInventory != null && topInventory.equals(inventory)) {
            // Hủy tất cả các sự kiện kéo vật phẩm trong GUI này
            e.setCancelled(true);

            // Cập nhật inventory ngay lập tức để đảm bảo thay đổi được áp dụng
            if (e.getWhoClicked() instanceof Player) {
                ((Player) e.getWhoClicked()).updateInventory();
            }
        }
    }
} 