package com.hongminh54.storage.CMD.commands;

import com.hongminh54.storage.Database.TransferHistory;
import com.hongminh54.storage.Manager.TransferManager;
import com.hongminh54.storage.Storage;
import com.hongminh54.storage.Utils.Chat;
import com.hongminh54.storage.Utils.File;
import org.bukkit.Bukkit;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;

public class LogCommand implements IStorageCommand {

    // Biến để theo dõi trạng thái xem toàn bộ log của người chơi (playerUUID -> targetPlayer)
    private static final Map<String, String> viewFullLogPlayers = new HashMap<>();
    // Biến để theo dõi thời gian hết hạn của lệnh logall
    private static final Map<String, Long> logAllExpiryTimes = new HashMap<>();

    @Override
    public boolean execute(CommandSender sender, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(Chat.colorize("&cLệnh này chỉ có thể được sử dụng bởi người chơi."));
            return true;
        }

        Player player = (Player) sender;

        // Xử lý lệnh logall
        if (args[0].equalsIgnoreCase("logall") || args[0].equalsIgnoreCase("lichsuall") || args[0].equalsIgnoreCase("historyall")) {
            return handleLogAllCommand(player, args);
        }

        // Xử lý lệnh log thông thường
        return handleLogCommand(player, args);
    }

    private boolean handleLogAllCommand(Player player, String[] args) {
        String targetPlayer = player.getName();

        // Kiểm tra nếu người chơi muốn xem lịch sử của người khác
        if (args.length >= 2) {
            targetPlayer = args[1];

            // Kiểm tra quyền nếu xem người khác
            if (!targetPlayer.equalsIgnoreCase(player.getName()) && !player.hasPermission("storage.transfer.others")) {
                player.sendMessage(Chat.colorize(File.getMessage().getString("user.action.transfer.history_no_permission",
                        "&8[&c&l✕&8] &cBạn không có quyền xem lịch sử chuyển kho của người khác.")));
                return true;
            }
        }

        // Thiết lập chế độ xem đầy đủ log
        setViewFullLog(player.getUniqueId().toString(), targetPlayer);

        // Hiển thị thông báo
        String enabledMsg = File.getMessage().getString("user.action.transfer.log_all_enabled",
                        "&a&l✓ &aĐã hiển thị toàn bộ lịch sử giao dịch của &f#player#&a.")
                .replace("#player#", targetPlayer);
        player.sendMessage(Chat.colorize(enabledMsg));

        // Tự động hiển thị log
        String[] logArgs = new String[]{"log"};
        if (!targetPlayer.equals(player.getName())) {
            logArgs = new String[]{"log", targetPlayer};
        }
        return handleLogCommand(player, logArgs);
    }

    private boolean handleLogCommand(Player player, String[] args) {
        // Kiểm tra nếu người dùng yêu cầu trợ giúp
        for (int i = 1; i < args.length; i++) {
            if (args[i].equalsIgnoreCase("-help") || args[i].equalsIgnoreCase("-h") || args[i].equalsIgnoreCase("-?")) {
                displayLogHelp(player);
                return true;
            }
        }

        // Các biến tìm kiếm
        String targetPlayer = player.getName();
        int page = 0;
        String materialFilter = null;
        boolean showStats = false;
        long startTime = 0;
        long endTime = System.currentTimeMillis();
        String sortOrder = "desc"; // mặc định là từ mới đến cũ

        // Phân tích các tham số
        List<String> remainingArgs = new ArrayList<>();
        for (int i = 1; i < args.length; i++) {
            String arg = args[i];

            if (arg.startsWith("-m:")) {
                // Lọc theo loại tài nguyên
                materialFilter = arg.substring(3);
            } else if (arg.startsWith("-from:")) {
                // Lọc từ số ngày trước
                try {
                    int daysAgo = Integer.parseInt(arg.substring(6));
                    startTime = System.currentTimeMillis() - TimeUnit.DAYS.toMillis(daysAgo);
                } catch (NumberFormatException e) {
                    player.sendMessage(Chat.colorize("&cSố ngày không hợp lệ trong tham số -from: " + arg.substring(6)));
                    return true;
                }
            } else if (arg.startsWith("-to:")) {
                // Lọc đến số ngày trước
                try {
                    int daysAgo = Integer.parseInt(arg.substring(4));
                    endTime = System.currentTimeMillis() - TimeUnit.DAYS.toMillis(daysAgo);
                } catch (NumberFormatException e) {
                    player.sendMessage(Chat.colorize("&cSố ngày không hợp lệ trong tham số -to: " + arg.substring(4)));
                    return true;
                }
            } else if (arg.equalsIgnoreCase("-stats")) {
                showStats = true;
            } else if (arg.equalsIgnoreCase("-asc")) {
                sortOrder = "asc";
            } else if (arg.equalsIgnoreCase("-desc")) {
                sortOrder = "desc";
            } else {
                remainingArgs.add(arg);
            }
        }

        // Xử lý các tham số còn lại (tên người chơi và trang)
        if (!remainingArgs.isEmpty()) {
            // Tham số đầu tiên có thể là tên người chơi hoặc số trang
            String firstArg = remainingArgs.get(0);

            try {
                // Thử parse thành số trang
                page = Integer.parseInt(firstArg);
                if (page < 0) page = 0;
            } catch (NumberFormatException e) {
                // Không phải số, coi như tên người chơi
                targetPlayer = firstArg;

                // Kiểm tra quyền nếu xem người khác
                if (!targetPlayer.equalsIgnoreCase(player.getName()) && !player.hasPermission("storage.transfer.others")) {
                    player.sendMessage(Chat.colorize(File.getMessage().getString("user.action.transfer.history_no_permission",
                            "&8[&c&l✕&8] &cBạn không có quyền xem lịch sử chuyển kho của người khác.")));
                    return true;
                }

                // Tham số thứ hai có thể là số trang
                if (remainingArgs.size() > 1) {
                    try {
                        page = Integer.parseInt(remainingArgs.get(1));
                        if (page < 0) page = 0;
                    } catch (NumberFormatException ex) {
                        player.sendMessage(Chat.colorize("&cSố trang không hợp lệ: " + remainingArgs.get(1)));
                        return true;
                    }
                }
            }
        }

        // Lấy danh sách lịch sử với bộ lọc
        List<TransferHistory> historyList = getFilteredTransferHistory(
                targetPlayer, page,
                File.getConfig().getInt("settings.history_items_per_page_chat", 20),
                materialFilter, startTime, endTime, sortOrder
        );

        // Hiển thị thống kê nếu được yêu cầu
        if (showStats) {
            displayTransferStats(player, targetPlayer, materialFilter, startTime, endTime);
        }

        // Tính tổng số mục phù hợp với bộ lọc (để phân trang)
        int totalFilteredItems = countFilteredTransferHistory(
                targetPlayer, materialFilter, startTime, endTime
        );

        int limit = File.getConfig().getInt("settings.history_items_per_page_chat", 20);
        int totalPages = (int) Math.ceil((double) totalFilteredItems / limit);

        if (page >= totalPages) {
            page = Math.max(0, totalPages - 1);
        }

        // Hiển thị header
        String headerMsg = File.getMessage().getString("user.action.transfer.history_header",
                        "&e&l≫ Lịch sử chuyển kho của &f#player# &e(Trang #page#/#total_pages#)")
                .replace("#player#", targetPlayer)
                .replace("#page#", String.valueOf(page + 1))
                .replace("#total_pages#", String.valueOf(Math.max(1, totalPages)));
        player.sendMessage(Chat.colorize(headerMsg));

        // Hiển thị bộ lọc đang áp dụng
        if (materialFilter != null || startTime > 0 || endTime < System.currentTimeMillis() - 86400000) {
            StringBuilder filterInfo = new StringBuilder("&7Bộ lọc: ");
            if (materialFilter != null) {
                filterInfo.append("&eTài nguyên: &f").append(materialFilter).append(" ");
            }
            if (startTime > 0) {
                SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");
                filterInfo.append("&eTừ: &f").append(dateFormat.format(new Date(startTime))).append(" ");
            }
            if (endTime < System.currentTimeMillis() - 86400000) {
                SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");
                filterInfo.append("&eĐến: &f").append(dateFormat.format(new Date(endTime))).append(" ");
            }
            player.sendMessage(Chat.colorize(filterInfo.toString()));
        }

        if (historyList.isEmpty()) {
            String noHistoryMsg = File.getMessage().getString("user.action.transfer.no_history",
                    "&7Không có lịch sử chuyển kho nào được tìm thấy.");
            player.sendMessage(Chat.colorize(noHistoryMsg));
            player.sendMessage(Chat.colorize("&aTip: &fSử dụng &e/kho log -help &fđể xem các tùy chọn tìm kiếm"));
            return true;
        }

        // Hiển thị danh sách lịch sử
        SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");

        for (TransferHistory history : historyList) {
            String materialName = File.getConfig().getString("items." + history.getMaterial(),
                    history.getMaterial().contains(";") ? history.getMaterial().split(";")[0] : history.getMaterial());

            // Định dạng thời gian
            String time = dateFormat.format(history.getDate());

            // Xác định loại giao dịch (gửi hay nhận)
            boolean isSender = history.getSenderName().equalsIgnoreCase(targetPlayer);
            String transactionType = isSender ? "&c↗ Gửi" : "&a↙ Nhận";
            String otherPlayer = isSender ? history.getReceiverName() : history.getSenderName();

            // Hiển thị thông tin giao dịch
            String historyMsg = File.getMessage().getString("user.action.transfer.history_item",
                            "&7[#time#] #type# &f#amount# #material# &7#direction# &e#player#")
                    .replace("#time#", time)
                    .replace("#type#", transactionType)
                    .replace("#amount#", String.valueOf(history.getAmount()))
                    .replace("#material#", materialName)
                    .replace("#direction#", isSender ? "cho" : "từ")
                    .replace("#player#", otherPlayer);

            player.sendMessage(Chat.colorize(historyMsg));
        }

        // Hiển thị footer với thông tin phân trang
        if (totalPages > 1) {
            String footerMsg = File.getMessage().getString("user.action.transfer.history_footer",
                            "&7Trang #page#/#total_pages# - Sử dụng &e/kho log #target# #next_page# &7để xem trang tiếp theo")
                    .replace("#page#", String.valueOf(page + 1))
                    .replace("#total_pages#", String.valueOf(totalPages))
                    .replace("#target#", targetPlayer.equals(player.getName()) ? "" : targetPlayer + " ")
                    .replace("#next_page#", String.valueOf(Math.min(page + 1, totalPages - 1)));
            player.sendMessage(Chat.colorize(footerMsg));
        }

        return true;
    }

    @Override
    public List<String> getTabComplete(CommandSender sender, String[] args) {
        List<String> completions = new ArrayList<>();

        if (args.length == 2) {
            // Tab completion cho tên người chơi (nếu có quyền xem người khác)
            if (sender.hasPermission("storage.transfer.others")) {
                return getOnlinePlayerNames(args[1]);
            }
        }

        return completions;
    }

    @Override
    public String getCommandName() {
        return "log";
    }

    @Override
    public List<String> getAliases() {
        return Arrays.asList("lichsu", "history", "logall", "lichsuall", "historyall");
    }

    @Override
    public boolean hasPermission(CommandSender sender) {
        return sender.hasPermission("storage.transfer");
    }

    // Helper methods
    private void setViewFullLog(String playerUUID, String targetPlayer) {
        viewFullLogPlayers.put(playerUUID, targetPlayer);
        // Thiết lập thời gian hết hạn (30 phút)
        logAllExpiryTimes.put(playerUUID, System.currentTimeMillis() + 1800000);
    }

    private void displayLogHelp(Player player) {
        player.sendMessage(Chat.colorize("&e&l≫ Hướng dẫn tìm kiếm lịch sử chuyển kho:"));
        player.sendMessage(Chat.colorize("&e/kho log [tên người chơi] [trang] [các tùy chọn]"));
        player.sendMessage(Chat.colorize("&7Các tùy chọn tìm kiếm:"));
        player.sendMessage(Chat.colorize("&7• &f-m:<tên vật phẩm> &7- Lọc theo loại tài nguyên"));
        player.sendMessage(Chat.colorize("&7• &f-from:<số ngày> &7- Lọc từ số ngày trước"));
        player.sendMessage(Chat.colorize("&7• &f-to:<số ngày> &7- Lọc đến số ngày trước"));
        player.sendMessage(Chat.colorize("&7• &f-stats &7- Hiển thị thống kê giao dịch"));
        player.sendMessage(Chat.colorize("&7• &f-asc &7- Sắp xếp từ cũ đến mới"));
        player.sendMessage(Chat.colorize("&7• &f-desc &7- Sắp xếp từ mới đến cũ (mặc định)"));
        player.sendMessage(Chat.colorize("&7• &f-help &7- Hiển thị hướng dẫn này"));
        player.sendMessage(Chat.colorize("&eVí dụ: &f/kho log -m:DIAMOND -from:7 -stats"));
        player.sendMessage(Chat.colorize("&7(Xem lịch sử kim cương trong 7 ngày qua kèm thống kê)"));
    }

    private List<String> getOnlinePlayerNames(String input) {
        List<String> names = new ArrayList<>();
        String lowerInput = input.toLowerCase();

        for (Player player : Bukkit.getOnlinePlayers()) {
            if (player.getName().toLowerCase().startsWith(lowerInput)) {
                names.add(player.getName());
            }
        }

        return names;
    }

    // Database methods
    private List<TransferHistory> getFilteredTransferHistory(String playerName, int page, int pageSize,
                                                             String material, long startTime, long endTime, String sortOrder) {
        List<TransferHistory> result = new ArrayList<>();

        try (java.sql.Connection conn = Storage.db.getConnection()) {
            if (conn == null) {
                Storage.getStorage().getLogger().warning("Không thể kết nối đến cơ sở dữ liệu để lấy lịch sử chuyển kho.");
                return result;
            }

            StringBuilder sqlBuilder = new StringBuilder();
            sqlBuilder.append("SELECT id, sender, receiver, material, amount, timestamp FROM transfer_history WHERE ");

            // Điều kiện người chơi
            sqlBuilder.append("(sender = ? OR receiver = ?) ");

            // Thêm điều kiện lọc theo loại tài nguyên
            if (material != null && !material.isEmpty()) {
                sqlBuilder.append("AND material = ? ");
            }

            // Thêm điều kiện lọc theo thời gian
            if (startTime > 0) {
                sqlBuilder.append("AND timestamp >= ? ");
            }

            if (endTime < System.currentTimeMillis()) {
                sqlBuilder.append("AND timestamp <= ? ");
            }

            // Thêm sắp xếp
            sqlBuilder.append("ORDER BY timestamp ").append(sortOrder.equalsIgnoreCase("asc") ? "ASC" : "DESC").append(" ");

            // Thêm phân trang
            sqlBuilder.append("LIMIT ? OFFSET ?");

            String sql = sqlBuilder.toString();

            try (java.sql.PreparedStatement ps = conn.prepareStatement(sql)) {
                int paramIndex = 1;

                // Thiết lập tham số người chơi
                ps.setString(paramIndex++, playerName);
                ps.setString(paramIndex++, playerName);

                // Thiết lập tham số lọc theo loại tài nguyên
                if (material != null && !material.isEmpty()) {
                    ps.setString(paramIndex++, material);
                }

                // Thiết lập tham số lọc theo thời gian
                if (startTime > 0) {
                    ps.setString(paramIndex++, TransferManager.formatTimestamp(startTime));
                }

                if (endTime < System.currentTimeMillis()) {
                    ps.setString(paramIndex++, TransferManager.formatTimestamp(endTime));
                }

                // Thiết lập tham số phân trang
                ps.setInt(paramIndex++, pageSize);
                ps.setInt(paramIndex, page * pageSize);

                try (java.sql.ResultSet rs = ps.executeQuery()) {
                    while (rs.next()) {
                        // Sử dụng TransferManager.parseTimestamp để chuyển đổi timestamp đúng cách
                        TransferHistory transfer = new TransferHistory(
                                rs.getInt("id"),
                                rs.getString("sender"),
                                rs.getString("receiver"),
                                rs.getString("material"),
                                rs.getInt("amount"),
                                TransferManager.parseTimestamp(rs.getString("timestamp"))
                        );
                        result.add(transfer);
                    }
                }
            }
        } catch (Exception e) {
            Storage.getStorage().getLogger().severe("Lỗi khi lấy lịch sử chuyển kho: " + e.getMessage());
            e.printStackTrace();
        }

        return result;
    }

    private int countFilteredTransferHistory(String playerName, String material, long startTime, long endTime) {
        int count = 0;

        try (java.sql.Connection conn = Storage.db.getConnection()) {
            if (conn == null) {
                Storage.getStorage().getLogger().warning("Không thể kết nối đến cơ sở dữ liệu để đếm lịch sử chuyển kho.");
                return 0;
            }

            StringBuilder sqlBuilder = new StringBuilder();
            sqlBuilder.append("SELECT COUNT(*) as total FROM transfer_history WHERE ");

            // Điều kiện người chơi
            sqlBuilder.append("(sender = ? OR receiver = ?) ");

            // Thêm điều kiện lọc theo loại tài nguyên
            if (material != null && !material.isEmpty()) {
                sqlBuilder.append("AND material = ? ");
            }

            // Thêm điều kiện lọc theo thời gian
            if (startTime > 0) {
                sqlBuilder.append("AND timestamp >= ? ");
            }

            if (endTime < System.currentTimeMillis()) {
                sqlBuilder.append("AND timestamp <= ? ");
            }

            String sql = sqlBuilder.toString();

            try (java.sql.PreparedStatement ps = conn.prepareStatement(sql)) {
                int paramIndex = 1;

                // Thiết lập tham số người chơi
                ps.setString(paramIndex++, playerName);
                ps.setString(paramIndex++, playerName);

                // Thiết lập tham số lọc theo loại tài nguyên
                if (material != null && !material.isEmpty()) {
                    ps.setString(paramIndex++, material);
                }

                // Thiết lập tham số lọc theo thời gian
                if (startTime > 0) {
                    ps.setString(paramIndex++, TransferManager.formatTimestamp(startTime));
                }

                if (endTime < System.currentTimeMillis()) {
                    ps.setString(paramIndex++, TransferManager.formatTimestamp(endTime));
                }

                try (java.sql.ResultSet rs = ps.executeQuery()) {
                    if (rs.next()) {
                        count = rs.getInt("total");
                    }
                }
            }
        } catch (Exception e) {
            Storage.getStorage().getLogger().severe("Lỗi khi đếm lịch sử chuyển kho: " + e.getMessage());
            e.printStackTrace();
        }

        return count;
    }

    private void displayTransferStats(Player player, String targetPlayer, String material, long startTime, long endTime) {
        try (java.sql.Connection conn = Storage.db.getConnection()) {
            if (conn == null) {
                Storage.getStorage().getLogger().warning("Không thể kết nối đến cơ sở dữ liệu để lấy thống kê chuyển kho.");
                return;
            }

            // Thống kê tổng quan
            player.sendMessage(Chat.colorize("&e&l≫ Thống kê giao dịch của &f" + targetPlayer));

            // Đếm số giao dịch gửi và nhận
            StringBuilder sqlBuilder = new StringBuilder();
            sqlBuilder.append("SELECT ");
            sqlBuilder.append("SUM(CASE WHEN sender = ? THEN amount ELSE 0 END) as total_sent, ");
            sqlBuilder.append("SUM(CASE WHEN receiver = ? THEN amount ELSE 0 END) as total_received, ");
            sqlBuilder.append("COUNT(CASE WHEN sender = ? THEN 1 END) as transactions_sent, ");
            sqlBuilder.append("COUNT(CASE WHEN receiver = ? THEN 1 END) as transactions_received ");
            sqlBuilder.append("FROM transfer_history WHERE (sender = ? OR receiver = ?) ");

            // Thêm điều kiện lọc
            if (material != null && !material.isEmpty()) {
                sqlBuilder.append("AND material = ? ");
            }
            if (startTime > 0) {
                sqlBuilder.append("AND timestamp >= ? ");
            }
            if (endTime < System.currentTimeMillis()) {
                sqlBuilder.append("AND timestamp <= ? ");
            }

            String sql = sqlBuilder.toString();

            try (java.sql.PreparedStatement ps = conn.prepareStatement(sql)) {
                int paramIndex = 1;

                // Thiết lập tham số
                ps.setString(paramIndex++, targetPlayer);
                ps.setString(paramIndex++, targetPlayer);
                ps.setString(paramIndex++, targetPlayer);
                ps.setString(paramIndex++, targetPlayer);
                ps.setString(paramIndex++, targetPlayer);
                ps.setString(paramIndex++, targetPlayer);

                // Thêm điều kiện lọc
                if (material != null && !material.isEmpty()) {
                    ps.setString(paramIndex++, material);
                }
                if (startTime > 0) {
                    ps.setString(paramIndex++, TransferManager.formatTimestamp(startTime));
                }
                if (endTime < System.currentTimeMillis()) {
                    ps.setString(paramIndex++, TransferManager.formatTimestamp(endTime));
                }

                try (java.sql.ResultSet rs = ps.executeQuery()) {
                    if (rs.next()) {
                        long totalSent = rs.getLong("total_sent");
                        long totalReceived = rs.getLong("total_received");
                        int transactionsSent = rs.getInt("transactions_sent");
                        int transactionsReceived = rs.getInt("transactions_received");

                        player.sendMessage(Chat.colorize("&7Đã gửi: &c" + totalSent + " &7tài nguyên (&c" + transactionsSent + " &7giao dịch)"));
                        player.sendMessage(Chat.colorize("&7Đã nhận: &a" + totalReceived + " &7tài nguyên (&a" + transactionsReceived + " &7giao dịch)"));

                        // Hiển thị cân bằng
                        long balance = totalReceived - totalSent;
                        String balancePrefix = balance >= 0 ? "&a+" : "&c";
                        player.sendMessage(Chat.colorize("&7Cân bằng: " + balancePrefix + balance + " &7tài nguyên"));
                    }
                }
            }
        } catch (Exception e) {
            Storage.getStorage().getLogger().severe("Lỗi khi lấy thống kê chuyển kho: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
