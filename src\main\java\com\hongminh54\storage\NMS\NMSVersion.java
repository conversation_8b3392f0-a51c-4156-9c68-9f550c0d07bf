package com.hongminh54.storage.NMS;

import org.bukkit.Bukkit;

import java.util.Objects;

/**
 * Lớp x<PERSON>c định phiên bản NMS cho Minecraft 1.12.2 - 1.21.x
 */
public class NMSVersion {

    /**
     * The major version of Minecraft.
     * <p>
     * Usually, and probably always going to be... '1'.
     * </p>
     */
    private final int major;

    /**
     * The minor version of Minecraft.
     * <p>
     * The minor version, for example '12' or '16' or most-recently '21'.
     * </p>
     */
    private final int minor;

    /**
     * The revision, 1.16.5 = 'R0' etc. etc.
     */
    private final int revision;

    /**
     * Constructor to initialise the NMSVersion data-type.
     * <p>
     * Initialises the {@link #major}, {@link #minor} and {@link #revision} variables,
     * for usage with the {@link NMSAssistant} or by Ponder Developers.
     * </p>
     */
    public NMSVersion() {
        String version = Bukkit.getServer().getBukkitVersion();
        version = version.split("-")[0];
        final String[] versionDetails = version.split("\\.");
        major = Integer.parseInt(versionDetails[0]); // Always probably going to be '1'.
        minor = Integer.parseInt(versionDetails[1]); // 12/16/18/7/8 etc. etc.
        revision = versionDetails.length == 3 ? Integer.parseInt(versionDetails[2]) : 0;

        // Kiểm tra phiên bản hỗ trợ
        if (minor < 12) {
            Bukkit.getLogger().warning("Plugin này yêu cầu phiên bản Minecraft 1.12.2 trở lên!");
            Bukkit.getLogger().warning("Phiên bản hiện tại: 1." + minor + "." + revision);
            Bukkit.getLogger().warning("Vui lòng nâng cấp lên phiên bản Minecraft mới hơn để sử dụng plugin này.");
        }
    }

    public int getMajor() {
        return major;
    }

    public int getMinor() {
        return minor;
    }

    public int getRevision() {
        return revision;
    }

    /**
     * Kiểm tra xem phiên bản hiện tại có được hỗ trợ không (1.12.2 - 1.25.x)
     *
     * @return true nếu phiên bản được hỗ trợ
     */
    public boolean isSupported() {
        // Kiểm tra phiên bản chính
        if (minor > 12) {
            // Hỗ trợ từ 1.13+ đến 1.25+ để tương thích với các phiên bản tương lai
            return minor <= 25;
        } else if (minor == 12) {
            // Chỉ hỗ trợ 1.12.2 trở lên
            return revision >= 2;
        }

        // Không hỗ trợ phiên bản nhỏ hơn 1.12.2
        return false;
    }

    /**
     * Kiểm tra xem có phải phiên bản 1.21.4 trở lên không
     *
     * @return true nếu là 1.21.4+
     */
    public boolean is1_21_4OrHigher() {
        return (minor > 21) || (minor == 21 && revision >= 4);
    }

    /**
     * Kiểm tra xem có phải phiên bản 1.20.5 trở lên không
     *
     * @return true nếu là 1.20.5+
     */
    public boolean is1_20_5OrHigher() {
        return (minor > 20) || (minor == 20 && revision >= 5);
    }

    @Override
    public String toString() {
        return "v" + getMajor() + "_" + getMinor() + "_R" + getRevision();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        NMSVersion that = (NMSVersion) o;
        return major == that.major && minor == that.minor && revision == that.revision;
    }

    @Override
    public int hashCode() {
        return Objects.hash(major, minor, revision);
    }
}
