package com.hongminh54.storage.CMD.commands;

import com.hongminh54.storage.GUI.StatsGUI;
import com.hongminh54.storage.Utils.Chat;
import com.hongminh54.storage.Utils.File;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class StatsCommand implements IStorageCommand {

    @Override
    public boolean execute(CommandSender sender, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(Chat.colorize("&cLệnh này chỉ có thể được sử dụng bởi người chơi."));
            return true;
        }

        Player player = (Player) sender;

        try {
            player.openInventory(new StatsGUI(player).getInventory());
        } catch (IndexOutOfBoundsException e) {
            player.sendMessage(Chat.colorize(File.getMessage().getString("admin.not_enough_slot")));
        } catch (Exception e) {
            player.sendMessage(Chat.colorize("&c<PERSON><PERSON> xảy ra lỗi khi mở GUI thống kê."));
            e.printStackTrace();
        }

        return true;
    }

    @Override
    public List<String> getTabComplete(CommandSender sender, String[] args) {
        return new ArrayList<>();
    }

    @Override
    public String getCommandName() {
        return "stats";
    }

    @Override
    public List<String> getAliases() {
        return Arrays.asList("thongke", "statistics");
    }

    @Override
    public boolean hasPermission(CommandSender sender) {
        return sender.hasPermission("storage.stats");
    }
}
