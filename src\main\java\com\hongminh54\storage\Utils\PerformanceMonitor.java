package com.hongminh54.storage.Utils;

import com.hongminh54.storage.Storage;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * <PERSON>ớ<PERSON> monitor hiệu suất plugin và server cho Minecraft 1.12.2 - 1.21.x
 * Theo dõi TPS, memory usage, và performance metrics
 */
public class PerformanceMonitor {

    private static final ConcurrentHashMap<String, Long> operationTimes = new ConcurrentHashMap<>();
    private static final ConcurrentHashMap<String, AtomicLong> operationCounts = new ConcurrentHashMap<>();
    private static final ConcurrentHashMap<String, AtomicLong> operationTotalTime = new ConcurrentHashMap<>();
    private static PerformanceMonitor instance;
    private BukkitTask monitorTask;
    private double currentTPS = 20.0;
    private long lastMemoryUsage = 0;
    private boolean monitoring = false;

    private PerformanceMonitor() {
    }

    public static PerformanceMonitor getInstance() {
        if (instance == null) {
            instance = new PerformanceMonitor();
        }
        return instance;
    }

    /**
     * Bắt đầu đo thời gian operation
     *
     * @param operationName Tên operation
     */
    public static void startTiming(String operationName) {
        if (operationName == null || operationName.isEmpty()) {
            return;
        }

        operationTimes.put(operationName, System.nanoTime());
    }

    /**
     * Kết thúc đo thời gian operation
     *
     * @param operationName Tên operation
     * @return Thời gian thực hiện (milliseconds)
     */
    public static long endTiming(String operationName) {
        if (operationName == null || operationName.isEmpty()) {
            return 0;
        }

        Long startTime = operationTimes.remove(operationName);
        if (startTime == null) {
            return 0;
        }

        long duration = (System.nanoTime() - startTime) / 1_000_000; // Convert to milliseconds

        // Cập nhật statistics
        operationCounts.computeIfAbsent(operationName, k -> new AtomicLong(0)).incrementAndGet();
        operationTotalTime.computeIfAbsent(operationName, k -> new AtomicLong(0)).addAndGet(duration);

        return duration;
    }

    /**
     * Đo thời gian thực hiện của một Runnable
     *
     * @param operationName Tên operation
     * @param runnable      Runnable cần đo
     * @return Thời gian thực hiện (milliseconds)
     */
    public static long timeOperation(String operationName, Runnable runnable) {
        if (runnable == null) {
            return 0;
        }

        startTiming(operationName);
        try {
            runnable.run();
        } finally {
            return endTiming(operationName);
        }
    }

    /**
     * Lấy số lần operation đã được thực hiện
     *
     * @param operationName Tên operation
     * @return Số lần thực hiện
     */
    public static long getOperationCount(String operationName) {
        AtomicLong count = operationCounts.get(operationName);
        return count != null ? count.get() : 0;
    }

    /**
     * Lấy tổng thời gian của operation
     *
     * @param operationName Tên operation
     * @return Tổng thời gian (milliseconds)
     */
    public static long getTotalOperationTime(String operationName) {
        AtomicLong total = operationTotalTime.get(operationName);
        return total != null ? total.get() : 0;
    }

    /**
     * Lấy thời gian trung bình của operation
     *
     * @param operationName Tên operation
     * @return Thời gian trung bình (milliseconds)
     */
    public static double getAverageOperationTime(String operationName) {
        long count = getOperationCount(operationName);
        long total = getTotalOperationTime(operationName);
        return count > 0 ? (double) total / count : 0;
    }

    /**
     * Reset statistics cho operation
     *
     * @param operationName Tên operation
     */
    public static void resetOperationStats(String operationName) {
        operationCounts.remove(operationName);
        operationTotalTime.remove(operationName);
        operationTimes.remove(operationName);
    }

    /**
     * Reset tất cả statistics
     */
    public static void resetAllStats() {
        operationCounts.clear();
        operationTotalTime.clear();
        operationTimes.clear();
    }

    /**
     * Bắt đầu monitoring performance
     */
    public void startMonitoring() {
        if (monitoring) {
            return;
        }

        monitoring = true;
        monitorTask = new BukkitRunnable() {
            @Override
            public void run() {
                updateMetrics();
            }
        }.runTaskTimer(Storage.getStorage(), 100L, 100L); // Chạy mỗi 5 giây thay vì mỗi giây

        if (Storage.getStorage().isDebug()) {
            Storage.getStorage().getLogger().info("Performance monitoring started");
        }
    }

    /**
     * Dừng monitoring performance
     */
    public void stopMonitoring() {
        if (!monitoring) {
            return;
        }

        monitoring = false;
        if (monitorTask != null && !monitorTask.isCancelled()) {
            monitorTask.cancel();
        }

        if (Storage.getStorage().isDebug()) {
            Storage.getStorage().getLogger().info("Performance monitoring stopped");
        }
    }

    /**
     * Lấy TPS hiện tại
     *
     * @return TPS hiện tại
     */
    public double getCurrentTPS() {
        return currentTPS;
    }

    /**
     * Kiểm tra server có đang lag không
     *
     * @return true nếu TPS < 18
     */
    public boolean isServerLagging() {
        return currentTPS < 18.0;
    }

    /**
     * Kiểm tra server có đang lag nặng không
     *
     * @return true nếu TPS < 15
     */
    public boolean isServerHeavyLagging() {
        return currentTPS < 15.0;
    }

    /**
     * Lấy memory usage hiện tại
     *
     * @return Memory usage (MB)
     */
    public long getCurrentMemoryUsage() {
        Runtime runtime = Runtime.getRuntime();
        return (runtime.totalMemory() - runtime.freeMemory()) / 1024 / 1024;
    }

    /**
     * Lấy max memory
     *
     * @return Max memory (MB)
     */
    public long getMaxMemory() {
        return Runtime.getRuntime().maxMemory() / 1024 / 1024;
    }

    /**
     * Lấy memory usage percentage
     *
     * @return Memory usage percentage (0-100)
     */
    public double getMemoryUsagePercentage() {
        long current = getCurrentMemoryUsage();
        long max = getMaxMemory();
        return max > 0 ? (double) current / max * 100 : 0;
    }

    /**
     * Kiểm tra memory có đang cao không
     *
     * @return true nếu memory usage > 80%
     */
    public boolean isHighMemoryUsage() {
        return getMemoryUsagePercentage() > 80.0;
    }

    /**
     * Lấy performance report
     *
     * @return Performance report string
     */
    public String getPerformanceReport() {
        StringBuilder report = new StringBuilder();
        report.append("=== Performance Report ===\n");
        report.append("TPS: ").append(String.format("%.2f", currentTPS)).append("\n");
        report.append("Memory: ").append(getCurrentMemoryUsage()).append("MB / ").append(getMaxMemory()).append("MB (")
                .append(String.format("%.1f", getMemoryUsagePercentage())).append("%)\n");
        report.append("Server Status: ");

        if (isServerHeavyLagging()) {
            report.append("Heavy Lag");
        } else if (isServerLagging()) {
            report.append("Lagging");
        } else {
            report.append("Good");
        }

        if (isHighMemoryUsage()) {
            report.append(" | High Memory Usage");
        }

        report.append("\n\n=== Operation Statistics ===\n");
        for (String operation : operationCounts.keySet()) {
            long count = getOperationCount(operation);
            double avgTime = getAverageOperationTime(operation);
            report.append(operation).append(": ").append(count).append(" calls, avg ")
                    .append(String.format("%.2f", avgTime)).append("ms\n");
        }

        return report.toString();
    }

    /**
     * Cập nhật metrics
     */
    private void updateMetrics() {
        try {
            // Cập nhật TPS
            currentTPS = com.hongminh54.storage.compatibility.ServerCompatibility.getServerTPS();

            // Cập nhật memory usage
            lastMemoryUsage = getCurrentMemoryUsage();

            // Log warning nếu performance kém
            if (isServerHeavyLagging() && Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Server đang lag nặng! TPS: " + String.format("%.2f", currentTPS));
            }

            if (isHighMemoryUsage() && Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Memory usage cao! " + String.format("%.1f", getMemoryUsagePercentage()) + "%");
            }
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Lỗi khi cập nhật performance metrics: " + e.getMessage());
            }
        }
    }

    /**
     * Cleanup khi plugin disable
     */
    public void cleanup() {
        stopMonitoring();
        resetAllStats();
    }
}
