package com.hongminh54.storage.compatibility;

import com.hongminh54.storage.NMS.NMSAssistant;
import com.hongminh54.storage.Storage;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.event.Event;
import org.bukkit.event.HandlerList;
import org.bukkit.event.block.BlockBreakEvent;
import org.bukkit.event.block.BlockPlaceEvent;
import org.bukkit.event.entity.EntityDamageEvent;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.event.player.PlayerQuitEvent;

/**
 * Lớp hỗ trợ tương thích Event API cho Minecraft 1.12.2 - 1.21.x
 * Xử lý event handling, event calling, và các tính năng liên quan
 */
public class EventCompatibility {

    private static final NMSAssistant nmsAssistant = new NMSAssistant();
    private static final boolean IS_PRE_113 = nmsAssistant.isVersionLessThan(13);
    private static final boolean IS_PRE_116 = nmsAssistant.isVersionLessThan(16);
    private static final boolean IS_1_20_OR_HIGHER = nmsAssistant.isVersionGreaterThanOrEqualTo(20);
    private static final boolean IS_1_20_5_OR_HIGHER = nmsAssistant.is1_20_5OrHigher();
    private static final boolean IS_1_21_4_OR_HIGHER = nmsAssistant.is1_21_4OrHigher();

    /**
     * Call event một cách an toàn
     *
     * @param event Event cần call
     * @return Event sau khi được call
     */
    public static <T extends Event> T callEventSafely(T event) {
        if (event == null) {
            return null;
        }

        try {
            Bukkit.getPluginManager().callEvent(event);
            return event;
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể call event: " + e.getMessage());
            }
            return event;
        }
    }

    /**
     * Kiểm tra event có bị cancel không
     *
     * @param event Event cần kiểm tra
     * @return true nếu event bị cancel
     */
    public static boolean isCancelled(Event event) {
        if (event == null) {
            return false;
        }

        try {
            if (event instanceof org.bukkit.event.Cancellable) {
                return ((org.bukkit.event.Cancellable) event).isCancelled();
            }
            return false;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * Set cancel cho event một cách an toàn
     *
     * @param event     Event cần set cancel
     * @param cancelled Trạng thái cancel
     * @return true nếu set thành công
     */
    public static boolean setCancelled(Event event, boolean cancelled) {
        if (event == null) {
            return false;
        }

        try {
            if (event instanceof org.bukkit.event.Cancellable) {
                ((org.bukkit.event.Cancellable) event).setCancelled(cancelled);
                return true;
            }
            return false;
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể set cancel cho event: " + e.getMessage());
            }
            return false;
        }
    }

    /**
     * Lấy player từ event một cách an toàn
     *
     * @param event Event
     * @return Player hoặc null nếu không có
     */
    public static Player getPlayerFromEvent(Event event) {
        if (event == null) {
            return null;
        }

        try {
            // Kiểm tra các loại event phổ biến
            if (event instanceof PlayerJoinEvent) {
                return ((PlayerJoinEvent) event).getPlayer();
            } else if (event instanceof PlayerQuitEvent) {
                return ((PlayerQuitEvent) event).getPlayer();
            } else if (event instanceof PlayerInteractEvent) {
                return ((PlayerInteractEvent) event).getPlayer();
            } else if (event instanceof BlockBreakEvent) {
                return ((BlockBreakEvent) event).getPlayer();
            } else if (event instanceof BlockPlaceEvent) {
                return ((BlockPlaceEvent) event).getPlayer();
            } else if (event instanceof InventoryClickEvent) {
                if (((InventoryClickEvent) event).getWhoClicked() instanceof Player) {
                    return (Player) ((InventoryClickEvent) event).getWhoClicked();
                }
            } else if (event instanceof EntityDamageEvent) {
                if (((EntityDamageEvent) event).getEntity() instanceof Player) {
                    return (Player) ((EntityDamageEvent) event).getEntity();
                }
            }

            // Thử reflection cho các event khác
            try {
                java.lang.reflect.Method getPlayerMethod = event.getClass().getMethod("getPlayer");
                Object result = getPlayerMethod.invoke(event);
                if (result instanceof Player) {
                    return (Player) result;
                }
            } catch (Exception e) {
                // Bỏ qua lỗi reflection
            }

            return null;
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể lấy player từ event: " + e.getMessage());
            }
            return null;
        }
    }

    /**
     * Kiểm tra event có phải async không
     *
     * @param event Event cần kiểm tra
     * @return true nếu event async
     */
    public static boolean isAsyncEvent(Event event) {
        if (event == null) {
            return false;
        }

        try {
            return event.isAsynchronous();
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * Tạo custom event một cách an toàn
     *
     * @param eventClass Class của event
     * @param args       Arguments cho constructor
     * @return Event instance hoặc null nếu có lỗi
     */
    public static <T extends Event> T createCustomEvent(Class<T> eventClass, Object... args) {
        if (eventClass == null) {
            return null;
        }

        try {
            // Tìm constructor phù hợp
            Class<?>[] paramTypes = new Class[args.length];
            for (int i = 0; i < args.length; i++) {
                paramTypes[i] = args[i] != null ? args[i].getClass() : Object.class;
            }

            java.lang.reflect.Constructor<T> constructor = eventClass.getConstructor(paramTypes);
            return constructor.newInstance(args);
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể tạo custom event: " + e.getMessage());
            }
            return null;
        }
    }

    /**
     * Lấy HandlerList của event
     *
     * @param eventClass Class của event
     * @return HandlerList hoặc null nếu có lỗi
     */
    public static HandlerList getHandlerList(Class<? extends Event> eventClass) {
        if (eventClass == null) {
            return null;
        }

        try {
            java.lang.reflect.Method getHandlerListMethod = eventClass.getMethod("getHandlerList");
            return (HandlerList) getHandlerListMethod.invoke(null);
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể lấy HandlerList: " + e.getMessage());
            }
            return null;
        }
    }

    /**
     * Unregister tất cả listeners của plugin
     *
     * @return true nếu unregister thành công
     */
    public static boolean unregisterAllListeners() {
        try {
            HandlerList.unregisterAll(Storage.getStorage());
            return true;
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể unregister listeners: " + e.getMessage());
            }
            return false;
        }
    }

    /**
     * Register listener một cách an toàn
     *
     * @param listener Listener cần register
     * @return true nếu register thành công
     */
    public static boolean registerListener(org.bukkit.event.Listener listener) {
        if (listener == null) {
            return false;
        }

        try {
            Bukkit.getPluginManager().registerEvents(listener, Storage.getStorage());
            return true;
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể register listener: " + e.getMessage());
            }
            return false;
        }
    }

    /**
     * Kiểm tra event có được handle bởi plugin không
     *
     * @param event Event
     * @return true nếu event được handle
     */
    public static boolean isEventHandled(Event event) {
        if (event == null) {
            return false;
        }

        try {
            HandlerList handlers = event.getHandlers();
            return handlers != null && handlers.getRegisteredListeners().length > 0;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * Lấy event name
     *
     * @param event Event
     * @return Tên event
     */
    public static String getEventName(Event event) {
        if (event == null) {
            return "Unknown";
        }

        try {
            return event.getEventName();
        } catch (Exception e) {
            return event.getClass().getSimpleName();
        }
    }

    /**
     * Kiểm tra event có phải player event không
     *
     * @param event Event
     * @return true nếu là player event
     */
    public static boolean isPlayerEvent(Event event) {
        if (event == null) {
            return false;
        }

        return getPlayerFromEvent(event) != null;
    }

    /**
     * Delay call event
     *
     * @param event      Event cần call
     * @param delayTicks Delay tính bằng ticks
     * @return true nếu schedule thành công
     */
    public static boolean callEventDelayed(Event event, long delayTicks) {
        if (event == null || delayTicks < 0) {
            return false;
        }

        try {
            Bukkit.getScheduler().runTaskLater(Storage.getStorage(), () -> {
                callEventSafely(event);
            }, delayTicks);
            return true;
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể schedule delayed event: " + e.getMessage());
            }
            return false;
        }
    }

    /**
     * Call event async
     *
     * @param event Event cần call
     * @return true nếu call thành công
     */
    public static boolean callEventAsync(Event event) {
        if (event == null) {
            return false;
        }

        try {
            Bukkit.getScheduler().runTaskAsynchronously(Storage.getStorage(), () -> {
                callEventSafely(event);
            });
            return true;
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể call async event: " + e.getMessage());
            }
            return false;
        }
    }

    /**
     * Kiểm tra event có thể cancel không
     *
     * @param event Event
     * @return true nếu có thể cancel
     */
    public static boolean isCancellable(Event event) {
        if (event == null) {
            return false;
        }

        return event instanceof org.bukkit.event.Cancellable;
    }

    /**
     * Log event information
     *
     * @param event   Event
     * @param message Message thêm
     */
    public static void logEventInfo(Event event, String message) {
        if (event == null || !Storage.getStorage().isDebug()) {
            return;
        }

        try {
            String eventName = getEventName(event);
            Player player = getPlayerFromEvent(event);
            String playerName = player != null ? player.getName() : "N/A";
            boolean cancelled = isCancelled(event);

            Storage.getStorage().getLogger().info(String.format(
                    "[Event] %s - Player: %s - Cancelled: %s - %s",
                    eventName, playerName, cancelled, message
            ));
        } catch (Exception e) {
            // Bỏ qua lỗi logging
        }
    }
}
