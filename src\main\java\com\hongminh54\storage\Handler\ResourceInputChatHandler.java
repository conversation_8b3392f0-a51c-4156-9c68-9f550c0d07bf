package com.hongminh54.storage.Handler;

import com.hongminh54.storage.Manager.MineManager;
import com.hongminh54.storage.Manager.SoundManager;
import com.hongminh54.storage.Storage;
import com.hongminh54.storage.Utils.Chat;
import com.hongminh54.storage.Utils.File;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.HandlerList;
import org.bukkit.event.Listener;
import org.bukkit.event.player.AsyncPlayerChatEvent;
import org.bukkit.event.player.PlayerQuitEvent;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Consumer;
import java.util.stream.Collectors;

public class ResourceInputChatHandler {

    // Map lưu trữ các input đang hoạt động
    private static final Map<UUID, ResourceChatInputListener> activeInputs = new ConcurrentHashMap<>();

    // Map lưu trữ thời gian input cuối cùng để tránh spam
    private static final Map<UUID, Long> lastInputTime = new ConcurrentHashMap<>();

    // Thời gian timeout mặc định (giây)
    private static final int DEFAULT_TIMEOUT_SECONDS = 30;

    // Cooldown giữa các lần input (mili giây)
    private static final long INPUT_COOLDOWN = 1000L;

    /**
     * Bắt đầu quá trình nhập tên khoáng sản
     *
     * @param player   Người chơi
     * @param prompt   Tin nhắn hiển thị
     * @param callback Callback khi người chơi nhập
     */
    public static void startResourceInput(Player player, String prompt, Consumer<String> callback) {
        if (player == null || !player.isOnline()) return;

        UUID playerId = player.getUniqueId();
        long currentTime = System.currentTimeMillis();

        // Kiểm tra cooldown
        if (lastInputTime.containsKey(playerId)) {
            long timeDiff = currentTime - lastInputTime.get(playerId);
            if (timeDiff < INPUT_COOLDOWN) {
                return; // Bỏ qua nếu còn trong thời gian cooldown
            }
        }

        // Cập nhật thời gian input cuối cùng
        lastInputTime.put(playerId, currentTime);

        // Hủy tất cả input cũ trước khi đăng ký mới
        cancelInput(player);

        // Đăng ký input mới sau 3 tick để đảm bảo listener cũ đã bị hủy hoàn toàn
        Bukkit.getScheduler().runTaskLater(Storage.getStorage(), () -> {
            if (player.isOnline()) {
                registerNewResourceInput(player, prompt, callback);
            }
        }, 3L);
    }

    /**
     * Đăng ký input mới sau khi đã hủy input cũ
     *
     * @param player   Người chơi
     * @param prompt   Prompt hiển thị
     * @param callback Callback khi người chơi nhập
     */
    private static void registerNewResourceInput(Player player, String prompt, Consumer<String> callback) {
        if (!player.isOnline()) return;

        UUID playerId = player.getUniqueId();

        // Hiển thị prompt cho người chơi với hướng dẫn về khoáng sản
        player.sendMessage(Chat.colorize("&8&m------------------------------"));
        player.sendMessage(Chat.colorize("&d&l⛏ " + prompt));
        player.sendMessage(Chat.colorize("&8&l┃ &f• &eNhập tên khoáng sản (ví dụ: &aDiamond&e, &aIron&e, &aGold&e)"));
        player.sendMessage(Chat.colorize("&8&l┃ &f• &eChỉ cần gõ một phần tên cũng được (ví dụ: gõ &adia &ethay vì &aDiamond&e)"));
        player.sendMessage(Chat.colorize("&8&l┃ &f• &eHệ thống sẽ tự động gợi ý khoáng sản phù hợp"));
        player.sendMessage(Chat.colorize("&8&l┃ &f• &6Gõ &6'?' &6để xem danh sách khoáng sản"));
        player.sendMessage(Chat.colorize("&8&l┃ &f• &6Gõ &6'1', '2', '3'... &6để chọn nhanh từ gợi ý"));
        player.sendMessage(Chat.colorize("&8&l┃ &f• &6Gõ &6'next'/'tiep' &6(trang sau), &6'prev'/'lui' &6(trang trước)"));
        player.sendMessage(Chat.colorize("&8&l┃ &f• &6Gõ &6'trang 2' &6để chuyển trực tiếp đến trang 2"));
        player.sendMessage(Chat.colorize("&8&l┃ &f• &cGõ &c'huy' &choặc &c'cancel' &cđể quay lại"));
        player.sendMessage(Chat.colorize("&8&l┃ &f• &6Thời gian chờ: &e" + DEFAULT_TIMEOUT_SECONDS + " &6giây"));

        // Hiển thị một số tài nguyên phổ biến
        List<String> commonMinerals = MineManager.getPluginBlocks().stream()
                .limit(5)
                .map(material -> {
                    String displayName = File.getConfig().getString("items." + material, material.split(";")[0]);
                    return Chat.colorize(displayName);
                })
                .collect(Collectors.toList());

        if (!commonMinerals.isEmpty()) {
            player.sendMessage(Chat.colorize("&8&l┃ &f• &aKhoáng sản phổ biến: " + String.join("&7, ", commonMinerals)));
        }

        player.sendMessage(Chat.colorize("&8&m------------------------------"));

        // Phát hiệu ứng âm thanh
        SoundManager.playSound(player, "BLOCK_NOTE_BLOCK_PLING", 0.6f, 1.2f);

        // Tạo và đăng ký listener mới
        ResourceChatInputListener listener = new ResourceChatInputListener(player, callback);
        activeInputs.put(playerId, listener);

        // Đăng ký listener
        Bukkit.getPluginManager().registerEvents(listener, Storage.getStorage());

        // Lên lịch timeout tự động hủy input
        Bukkit.getScheduler().runTaskLater(Storage.getStorage(), () -> {
            if (activeInputs.containsKey(playerId) && player.isOnline()) {
                player.sendMessage(Chat.colorize(File.getMessage().getString("user.action.transfer.resource_timeout",
                        "&8[&6&l⚠&8] &6Hết thời gian chờ nhập khoáng sản. Đã tự động hủy.")));
                SoundManager.playSound(player, "ENTITY_VILLAGER_NO", 0.5f, 0.8f);
                cancelInput(player);
            }
        }, DEFAULT_TIMEOUT_SECONDS * 20L);

        // Debug log
        if (Storage.getStorage().isDebug()) {
            Storage.getStorage().getLogger().info("Đã đăng ký resource input listener cho: " + player.getName() +
                    " với timeout: " + DEFAULT_TIMEOUT_SECONDS + "s");
        }
    }

    /**
     * Hủy input của người chơi
     *
     * @param player Người chơi
     */
    public static void cancelInput(Player player) {
        if (player == null) return;

        UUID playerId = player.getUniqueId();
        ResourceChatInputListener listener = activeInputs.remove(playerId);

        if (listener != null) {
            try {
                HandlerList.unregisterAll(listener);
                if (Storage.getStorage().isDebug()) {
                    Storage.getStorage().getLogger().info("Đã hủy resource input listener cho: " + player.getName());
                }
            } catch (Exception e) {
                Storage.getStorage().getLogger().warning("Lỗi khi hủy resource input listener: " + e.getMessage());
            }
        }
    }

    /**
     * Kiểm tra xem người chơi có đang trong quá trình input không
     *
     * @param player Người chơi
     * @return true nếu đang input
     */
    public static boolean isInputting(Player player) {
        return player != null && activeInputs.containsKey(player.getUniqueId());
    }

    /**
     * Hủy tất cả input đang hoạt động
     */
    public static void cancelAllInputs() {
        for (UUID playerId : new HashSet<>(activeInputs.keySet())) {
            Player player = Bukkit.getPlayer(playerId);
            if (player != null) {
                cancelInput(player);
            }
        }
        activeInputs.clear();
        lastInputTime.clear();
    }

    /**
     * Tìm tài nguyên phù hợp nhất với input của người chơi
     *
     * @param input Input của người chơi
     * @return Tài nguyên phù hợp nhất hoặc null nếu không tìm thấy
     */
    private static String findBestMineralMatch(String input) {
        if (input == null || input.trim().isEmpty()) return null;

        String searchTerm = input.toLowerCase().trim();
        List<String> allMinerals = MineManager.getPluginBlocks();

        // Tìm kiếm chính xác trước
        for (String mineral : allMinerals) {
            String mineralKey = mineral.split(";")[0].toLowerCase();
            String displayName = Chat.stripColor(File.getConfig().getString("items." + mineral, mineralKey)).toLowerCase();

            if (mineralKey.equals(searchTerm) || displayName.equals(searchTerm)) {
                return mineral;
            }
        }

        // Tìm kiếm bắt đầu bằng
        for (String mineral : allMinerals) {
            String mineralKey = mineral.split(";")[0].toLowerCase();
            String displayName = Chat.stripColor(File.getConfig().getString("items." + mineral, mineralKey)).toLowerCase();

            if (mineralKey.startsWith(searchTerm) || displayName.startsWith(searchTerm)) {
                return mineral;
            }
        }

        // Tìm kiếm chứa
        for (String mineral : allMinerals) {
            String mineralKey = mineral.split(";")[0].toLowerCase();
            String displayName = Chat.stripColor(File.getConfig().getString("items." + mineral, mineralKey)).toLowerCase();

            if (mineralKey.contains(searchTerm) || displayName.contains(searchTerm)) {
                return mineral;
            }
        }

        return null;
    }

    /**
     * Lấy danh sách gợi ý khoáng sản
     *
     * @param input Input của người chơi
     * @param limit Số lượng gợi ý tối đa
     * @return Danh sách gợi ý display names
     */
    private static List<String> getMineralSuggestions(String input, int limit) {
        if (input == null || input.trim().isEmpty()) return Collections.emptyList();

        String searchTerm = input.toLowerCase().trim();
        List<String> allMinerals = MineManager.getPluginBlocks();
        List<String> suggestions = new ArrayList<>();

        for (String mineral : allMinerals) {
            if (suggestions.size() >= limit) break;

            String mineralKey = mineral.split(";")[0].toLowerCase();
            String displayName = File.getConfig().getString("items." + mineral, mineralKey);
            String cleanDisplayName = Chat.stripColor(displayName).toLowerCase();

            if (mineralKey.contains(searchTerm) || cleanDisplayName.contains(searchTerm)) {
                suggestions.add(Chat.colorize(displayName));
            }
        }

        return suggestions;
    }

    /**
     * Lấy danh sách mineral keys tương ứng với gợi ý
     *
     * @param input Input của người chơi
     * @param limit Số lượng gợi ý tối đa
     * @return Danh sách mineral keys
     */
    private static List<String> getMineralSuggestionKeys(String input, int limit) {
        if (input == null || input.trim().isEmpty()) return Collections.emptyList();

        String searchTerm = input.toLowerCase().trim();
        List<String> allMinerals = MineManager.getPluginBlocks();
        List<String> keys = new ArrayList<>();

        for (String mineral : allMinerals) {
            if (keys.size() >= limit) break;

            String mineralKey = mineral.split(";")[0].toLowerCase();
            String displayName = File.getConfig().getString("items." + mineral, mineralKey);
            String cleanDisplayName = Chat.stripColor(displayName).toLowerCase();

            if (mineralKey.contains(searchTerm) || cleanDisplayName.contains(searchTerm)) {
                keys.add(mineral);
            }
        }

        return keys;
    }

    /**
     * Listener cho việc nhập khoáng sản
     */
    private static class ResourceChatInputListener implements Listener {
        private final Player player;
        private final Consumer<String> callback;
        private boolean processed = false;
        private List<String> lastSuggestions = new ArrayList<>();
        private int currentPage = 1;
        private int totalPages = 1;

        public ResourceChatInputListener(Player player, Consumer<String> callback) {
            this.player = player;
            this.callback = callback;
        }

        @EventHandler(priority = EventPriority.LOWEST)
        public void onPlayerChat(AsyncPlayerChatEvent e) {
            if (processed || !e.getPlayer().equals(player)) return;

            String input = e.getMessage().trim();

            // Hủy event để không hiển thị tin nhắn trong chat
            e.setCancelled(true);

            // Kiểm tra lệnh hủy
            if (input.equalsIgnoreCase("huy") || input.equalsIgnoreCase("cancel") || input.equalsIgnoreCase("quit")) {
                Bukkit.getScheduler().runTask(Storage.getStorage(), () -> {
                    if (player.isOnline()) {
                        player.sendMessage(Chat.colorize("&8[&c&l✕&8] &cĐã hủy nhập khoáng sản."));
                        SoundManager.playSound(player, "ENTITY_VILLAGER_NO", 0.5f, 0.8f);
                    }
                    cancelInput(player);
                });
                return;
            }

            // Kiểm tra lệnh hiển thị danh sách khoáng sản
            if (input.equals("?") || input.equalsIgnoreCase("list") || input.equalsIgnoreCase("danh sach")) {
                Bukkit.getScheduler().runTask(Storage.getStorage(), () -> {
                    if (player.isOnline()) {
                        currentPage = 1; // Reset về trang 1
                        showMineralList(player, currentPage);
                    }
                });
                return;
            }

            // Kiểm tra lệnh chuyển trang
            if (input.equalsIgnoreCase("next") || input.equalsIgnoreCase("tiep") || input.equalsIgnoreCase("trang tiep")) {
                Bukkit.getScheduler().runTask(Storage.getStorage(), () -> {
                    if (player.isOnline()) {
                        if (currentPage < totalPages) {
                            currentPage++;
                            showMineralList(player, currentPage);
                        } else {
                            player.sendMessage(Chat.colorize("&8[&c&l✕&8] &cĐây là trang cuối cùng."));
                        }
                    }
                });
                return;
            }

            if (input.equalsIgnoreCase("prev") || input.equalsIgnoreCase("back") || input.equalsIgnoreCase("lui") || input.equalsIgnoreCase("trang truoc")) {
                Bukkit.getScheduler().runTask(Storage.getStorage(), () -> {
                    if (player.isOnline()) {
                        if (currentPage > 1) {
                            currentPage--;
                            showMineralList(player, currentPage);
                        } else {
                            player.sendMessage(Chat.colorize("&8[&c&l✕&8] &cĐây là trang đầu tiên."));
                        }
                    }
                });
                return;
            }

            // Kiểm tra lệnh chuyển trang trực tiếp (page 2, trang 2)
            if (input.toLowerCase().matches("^(page|trang)\\s+[1-9]\\d*$")) {
                String[] parts = input.toLowerCase().split("\\s+");
                int targetPage = Integer.parseInt(parts[1]);
                Bukkit.getScheduler().runTask(Storage.getStorage(), () -> {
                    if (player.isOnline()) {
                        if (targetPage >= 1 && targetPage <= totalPages) {
                            currentPage = targetPage;
                            showMineralList(player, currentPage);
                        } else {
                            player.sendMessage(Chat.colorize("&8[&c&l✕&8] &cTrang không hợp lệ. Chỉ có &e" + totalPages + " &ctrang."));
                        }
                    }
                });
                return;
            }

            // Tìm khoáng sản phù hợp trước
            String bestMatch = findBestMineralMatch(input);
            String finalInput = bestMatch != null ? bestMatch : input;

            // Nếu không tìm thấy khoáng sản và input là số, kiểm tra chọn nhanh
            if (bestMatch == null && input.matches("^[1-9]\\d*$")) {
                int index = Integer.parseInt(input) - 1;
                if (index >= 0 && index < lastSuggestions.size()) {
                    String selectedMineral = lastSuggestions.get(index);
                    Bukkit.getScheduler().runTask(Storage.getStorage(), () -> {
                        if (player.isOnline()) {
                            String displayName = File.getConfig().getString("items." + selectedMineral, selectedMineral.split(";")[0]);
                            player.sendMessage(Chat.colorize("&8[&a&l✓&8] &aĐã chọn: &e" + Chat.colorize(displayName)));
                            SoundManager.playSound(player, "BLOCK_NOTE_BLOCK_PLING", 0.5f, 1.5f);
                        }
                        processSelection(selectedMineral);
                    });
                    return;
                } else {
                    Bukkit.getScheduler().runTask(Storage.getStorage(), () -> {
                        if (player.isOnline()) {
                            player.sendMessage(Chat.colorize("&8[&c&l✕&8] &cSố thứ tự không hợp lệ. Gõ &e'?' &cđể xem danh sách."));
                        }
                    });
                    return;
                }
            }

            // Thông báo kết quả tìm kiếm
            if (bestMatch != null && !bestMatch.equals(input)) {
                String displayName = File.getConfig().getString("items." + bestMatch, bestMatch.split(";")[0]);
                Bukkit.getScheduler().runTask(Storage.getStorage(), () -> {
                    if (player.isOnline()) {
                        player.sendMessage(Chat.colorize("&8[&a&l✓&8] &aĐã tự động hoàn thành thành: &e" + Chat.colorize(displayName)));
                        SoundManager.playSound(player, "BLOCK_NOTE_BLOCK_PLING", 0.5f, 1.5f);
                    }
                });
            } else if (bestMatch == null) {
                // Hiển thị gợi ý nếu không tìm thấy và không phải là số
                if (!input.matches("^[1-9]\\d*$")) {
                    List<String> suggestions = getMineralSuggestions(input, 5);
                    lastSuggestions = getMineralSuggestionKeys(input, 5); // Lưu keys để chọn nhanh

                    Bukkit.getScheduler().runTask(Storage.getStorage(), () -> {
                        if (player.isOnline()) {
                            player.sendMessage(Chat.colorize(File.getMessage().getString("user.action.transfer.invalid_resource",
                                    "&8[&c&l✕&8] &cKhoáng sản không tồn tại: &f#resource#").replace("#resource#", input)));

                            if (!suggestions.isEmpty()) {
                                player.sendMessage(Chat.colorize("&8[&e&l💡&8] &eGợi ý khoáng sản:"));
                                for (int i = 0; i < suggestions.size(); i++) {
                                    player.sendMessage(Chat.colorize("&8&l┃ &f" + (i + 1) + ". &e" + suggestions.get(i)));
                                }
                                player.sendMessage(Chat.colorize("&8&l┃ &6Gõ số &e1-" + suggestions.size() + " &6để chọn nhanh"));
                            }
                        }
                    });
                }
            }

            // Hủy đăng ký listener và gọi callback
            Bukkit.getScheduler().runTask(Storage.getStorage(), () -> {
                processSelection(finalInput);
            });
        }

        @EventHandler
        public void onPlayerQuit(PlayerQuitEvent e) {
            if (e.getPlayer().equals(player)) {
                cancelInput(player);
            }
        }

        /**
         * Hiển thị danh sách khoáng sản cho người chơi
         */
        private void showMineralList(Player player, int page) {
            List<String> allMinerals = MineManager.getPluginBlocks();
            List<String> displayMinerals = new ArrayList<>();

            for (String mineral : allMinerals) {
                String displayName = File.getConfig().getString("items." + mineral, mineral.split(";")[0]);
                displayMinerals.add(Chat.colorize(displayName));
            }

            // Tính toán phân trang
            int pageSize = 10;
            totalPages = (int) Math.ceil((double) displayMinerals.size() / pageSize);

            if (page < 1) page = 1;
            if (page > totalPages) page = totalPages;

            int startIndex = (page - 1) * pageSize;
            int endIndex = Math.min(startIndex + pageSize, displayMinerals.size());

            player.sendMessage(Chat.colorize("&8&m------------------------------"));
            player.sendMessage(Chat.colorize("&d&l⛏ Danh sách khoáng sản (Trang " + page + "/" + totalPages + ")"));

            // Hiển thị khoáng sản của trang hiện tại
            for (int i = startIndex; i < endIndex; i++) {
                int displayNumber = i - startIndex + 1;
                player.sendMessage(Chat.colorize("&8&l┃ &f" + displayNumber + ". &e" + displayMinerals.get(i)));
            }

            // Hướng dẫn chuyển trang
            player.sendMessage(Chat.colorize("&8&l┃"));
            if (totalPages > 1) {
                StringBuilder navigation = new StringBuilder("&8&l┃ &6Điều hướng: ");
                if (page > 1) {
                    navigation.append("&e'prev' &6hoặc &e'lui' &6(trang trước) ");
                }
                if (page < totalPages) {
                    navigation.append("&e'next' &6hoặc &e'tiep' &6(trang sau) ");
                }
                navigation.append("&e'trang ").append(page == 1 ? 2 : 1).append("' &6(chuyển trực tiếp)");
                player.sendMessage(Chat.colorize(navigation.toString()));
            }

            player.sendMessage(Chat.colorize("&8&l┃ &6Gõ số &e1-" + (endIndex - startIndex) + " &6để chọn nhanh"));
            player.sendMessage(Chat.colorize("&8&l┃ &7Hoặc gõ tên khoáng sản để tìm kiếm"));
            player.sendMessage(Chat.colorize("&8&m------------------------------"));

            // Lưu gợi ý cho chọn nhanh - lấy từ trang hiện tại
            lastSuggestions = allMinerals.subList(startIndex, endIndex);
        }

        /**
         * Lấy danh sách mineral keys từ display names
         */
        private List<String> getMineralKeys(List<String> displayNames) {
            List<String> keys = new ArrayList<>();
            List<String> allMinerals = MineManager.getPluginBlocks();

            for (String displayName : displayNames) {
                String cleanDisplayName = Chat.stripColor(displayName).trim();
                for (String mineral : allMinerals) {
                    String mineralDisplayName = Chat.stripColor(File.getConfig().getString("items." + mineral, mineral.split(";")[0])).trim();
                    if (mineralDisplayName.equalsIgnoreCase(cleanDisplayName)) {
                        keys.add(mineral);
                        break;
                    }
                }
            }

            return keys;
        }

        /**
         * Xử lý khi người chơi chọn khoáng sản
         */
        private void processSelection(String mineral) {
            UUID playerId = player.getUniqueId();
            if (activeInputs.containsKey(playerId)) {
                HandlerList.unregisterAll(this);
                activeInputs.remove(playerId);
            }

            if (!player.isOnline()) return;

            processed = true;

            // Chờ 2 tick trước khi gọi callback để đảm bảo đồng bộ hóa
            Bukkit.getScheduler().runTaskLater(Storage.getStorage(), () -> {
                if (player.isOnline()) {
                    try {
                        callback.accept(mineral);
                    } catch (Exception ex) {
                        Storage.getStorage().getLogger().severe("Lỗi khi xử lý resource input: " + ex.getMessage());
                        player.sendMessage(Chat.colorize("&8[&4&l✕&8] &cCó lỗi xảy ra trong quá trình xử lý dữ liệu."));
                    }
                }
            }, 2L);
        }
    }
}
