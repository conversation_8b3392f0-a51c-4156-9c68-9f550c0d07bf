package com.hongminh54.storage.compatibility;

import com.hongminh54.storage.NMS.NMSAssistant;
import com.hongminh54.storage.Storage;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;

import java.util.Collection;

/**
 * Lớp hỗ trợ tương thích Potion API cho Minecraft 1.12.2 - 1.21.x
 * Xử lý PotionEffectType changes, potion effects, và potion manipulation
 */
public class PotionCompatibility {

    private static final NMSAssistant nmsAssistant = new NMSAssistant();
    private static final boolean IS_PRE_113 = nmsAssistant.isVersionLessThan(13);
    private static final boolean IS_PRE_116 = nmsAssistant.isVersionLessThan(16);
    private static final boolean IS_1_20_5_OR_HIGHER = nmsAssistant.isVersionGreaterThanOrEqualTo(20) &&
            nmsAssistant.getNMSVersion().getRevision() >= 5;

    /**
     * Lấy PotionEffectType tương thích đa phiên bản
     *
     * @param modernName Tên effect phiên bản mới (1.20.5+)
     * @param legacyName Tên effect phiên bản cũ (trước 1.20.5)
     * @return PotionEffectType tương thích
     */
    @SuppressWarnings("deprecation")
    public static PotionEffectType getCompatiblePotionEffectType(String modernName, String legacyName) {
        try {
            if (IS_1_20_5_OR_HIGHER) {
                // Thử với tên mới trước
                PotionEffectType effect = PotionEffectType.getByName(modernName);
                if (effect != null) {
                    return effect;
                }
            }

            // Thử với tên cũ
            PotionEffectType effect = PotionEffectType.getByName(legacyName);
            if (effect != null) {
                return effect;
            }

            // Thử với ID (cho phiên bản rất cũ)
            return getPotionEffectByLegacyId(legacyName);
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không tìm thấy PotionEffectType: " + modernName + "/" + legacyName);
            }
            return null;
        }
    }

    /**
     * Áp dụng potion effect một cách an toàn
     *
     * @param entity     Entity nhận effect
     * @param effectType Loại effect
     * @param duration   Thời gian (ticks)
     * @param amplifier  Cường độ (0 = level 1)
     * @param ambient    Có phải ambient effect không
     * @param particles  Hiển thị particles không
     * @return true nếu áp dụng thành công
     */
    public static boolean addPotionEffectSafely(LivingEntity entity, PotionEffectType effectType,
                                                int duration, int amplifier, boolean ambient, boolean particles) {
        if (entity == null || effectType == null) {
            return false;
        }

        try {
            PotionEffect effect = new PotionEffect(effectType, duration, amplifier, ambient, particles);
            return entity.addPotionEffect(effect);
        } catch (Exception e) {
            // Fallback không có particles parameter cho phiên bản cũ
            return addPotionEffectLegacy(entity, effectType, duration, amplifier, ambient);
        }
    }

    /**
     * Áp dụng potion effect với tham số đơn giản
     *
     * @param entity     Entity nhận effect
     * @param effectType Loại effect
     * @param duration   Thời gian (ticks)
     * @param amplifier  Cường độ (0 = level 1)
     * @return true nếu áp dụng thành công
     */
    public static boolean addPotionEffectSafely(LivingEntity entity, PotionEffectType effectType,
                                                int duration, int amplifier) {
        return addPotionEffectSafely(entity, effectType, duration, amplifier, false, true);
    }

    /**
     * Xóa potion effect một cách an toàn
     *
     * @param entity     Entity cần xóa effect
     * @param effectType Loại effect cần xóa
     * @return true nếu xóa thành công
     */
    public static boolean removePotionEffectSafely(LivingEntity entity, PotionEffectType effectType) {
        if (entity == null || effectType == null) {
            return false;
        }

        try {
            entity.removePotionEffect(effectType);
            return true;
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể xóa potion effect: " + e.getMessage());
            }
            return false;
        }
    }

    /**
     * Kiểm tra entity có effect không
     *
     * @param entity     Entity cần kiểm tra
     * @param effectType Loại effect
     * @return true nếu có effect
     */
    public static boolean hasEffect(LivingEntity entity, PotionEffectType effectType) {
        if (entity == null || effectType == null) {
            return false;
        }

        try {
            return entity.hasPotionEffect(effectType);
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * Lấy potion effect hiện tại
     *
     * @param entity     Entity
     * @param effectType Loại effect
     * @return PotionEffect hoặc null nếu không có
     */
    public static PotionEffect getEffect(LivingEntity entity, PotionEffectType effectType) {
        if (entity == null || effectType == null) {
            return null;
        }

        try {
            return entity.getPotionEffect(effectType);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * Lấy tất cả potion effects của entity
     *
     * @param entity Entity
     * @return Collection của PotionEffect
     */
    public static Collection<PotionEffect> getAllEffects(LivingEntity entity) {
        if (entity == null) {
            return java.util.Collections.emptyList();
        }

        try {
            return entity.getActivePotionEffects();
        } catch (Exception e) {
            return java.util.Collections.emptyList();
        }
    }

    /**
     * Xóa tất cả potion effects
     *
     * @param entity Entity
     * @return true nếu xóa thành công
     */
    public static boolean clearAllEffects(LivingEntity entity) {
        if (entity == null) {
            return false;
        }

        try {
            Collection<PotionEffect> effects = entity.getActivePotionEffects();
            for (PotionEffect effect : effects) {
                entity.removePotionEffect(effect.getType());
            }
            return true;
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể xóa tất cả effects: " + e.getMessage());
            }
            return false;
        }
    }

    /**
     * Áp dụng speed effect
     *
     * @param player    Player
     * @param duration  Thời gian (ticks)
     * @param amplifier Cường độ
     * @return true nếu thành công
     */
    public static boolean addSpeedEffect(Player player, int duration, int amplifier) {
        PotionEffectType speed = getCompatiblePotionEffectType("SPEED", "SPEED");
        return addPotionEffectSafely(player, speed, duration, amplifier);
    }

    /**
     * Áp dụng strength effect
     *
     * @param player    Player
     * @param duration  Thời gian (ticks)
     * @param amplifier Cường độ
     * @return true nếu thành công
     */
    public static boolean addStrengthEffect(Player player, int duration, int amplifier) {
        PotionEffectType strength = getCompatiblePotionEffectType("STRENGTH", "INCREASE_DAMAGE");
        return addPotionEffectSafely(player, strength, duration, amplifier);
    }

    /**
     * Áp dụng jump boost effect
     *
     * @param player    Player
     * @param duration  Thời gian (ticks)
     * @param amplifier Cường độ
     * @return true nếu thành công
     */
    public static boolean addJumpBoostEffect(Player player, int duration, int amplifier) {
        PotionEffectType jumpBoost = getCompatiblePotionEffectType("JUMP_BOOST", "JUMP");
        return addPotionEffectSafely(player, jumpBoost, duration, amplifier);
    }

    /**
     * Áp dụng regeneration effect
     *
     * @param player    Player
     * @param duration  Thời gian (ticks)
     * @param amplifier Cường độ
     * @return true nếu thành công
     */
    public static boolean addRegenerationEffect(Player player, int duration, int amplifier) {
        PotionEffectType regen = getCompatiblePotionEffectType("REGENERATION", "REGENERATION");
        return addPotionEffectSafely(player, regen, duration, amplifier);
    }

    /**
     * Áp dụng invisibility effect
     *
     * @param player   Player
     * @param duration Thời gian (ticks)
     * @return true nếu thành công
     */
    public static boolean addInvisibilityEffect(Player player, int duration) {
        PotionEffectType invisibility = getCompatiblePotionEffectType("INVISIBILITY", "INVISIBILITY");
        return addPotionEffectSafely(player, invisibility, duration, 0);
    }

    /**
     * Áp dụng night vision effect
     *
     * @param player   Player
     * @param duration Thời gian (ticks)
     * @return true nếu thành công
     */
    public static boolean addNightVisionEffect(Player player, int duration) {
        PotionEffectType nightVision = getCompatiblePotionEffectType("NIGHT_VISION", "NIGHT_VISION");
        return addPotionEffectSafely(player, nightVision, duration, 0);
    }

    /**
     * Áp dụng water breathing effect
     *
     * @param player   Player
     * @param duration Thời gian (ticks)
     * @return true nếu thành công
     */
    public static boolean addWaterBreathingEffect(Player player, int duration) {
        PotionEffectType waterBreathing = getCompatiblePotionEffectType("WATER_BREATHING", "WATER_BREATHING");
        return addPotionEffectSafely(player, waterBreathing, duration, 0);
    }

    /**
     * Áp dụng fire resistance effect
     *
     * @param player   Player
     * @param duration Thời gian (ticks)
     * @return true nếu thành công
     */
    public static boolean addFireResistanceEffect(Player player, int duration) {
        PotionEffectType fireResistance = getCompatiblePotionEffectType("FIRE_RESISTANCE", "FIRE_RESISTANCE");
        return addPotionEffectSafely(player, fireResistance, duration, 0);
    }

    /**
     * Lấy PotionEffectType bằng legacy ID cho phiên bản cũ
     */
    @SuppressWarnings("deprecation")
    private static PotionEffectType getPotionEffectByLegacyId(String name) {
        try {
            // Mapping tên sang ID cho phiên bản cũ
            switch (name.toUpperCase()) {
                case "SPEED":
                    return PotionEffectType.getById(1);
                case "SLOWNESS":
                    return PotionEffectType.getById(2);
                case "HASTE":
                    return PotionEffectType.getById(3);
                case "MINING_FATIGUE":
                    return PotionEffectType.getById(4);
                case "STRENGTH":
                case "INCREASE_DAMAGE":
                    return PotionEffectType.getById(5);
                case "INSTANT_HEALTH":
                    return PotionEffectType.getById(6);
                case "INSTANT_DAMAGE":
                    return PotionEffectType.getById(7);
                case "JUMP_BOOST":
                case "JUMP":
                    return PotionEffectType.getById(8);
                case "NAUSEA":
                case "CONFUSION":
                    return PotionEffectType.getById(9);
                case "REGENERATION":
                    return PotionEffectType.getById(10);
                case "RESISTANCE":
                case "DAMAGE_RESISTANCE":
                    return PotionEffectType.getById(11);
                case "FIRE_RESISTANCE":
                    return PotionEffectType.getById(12);
                case "WATER_BREATHING":
                    return PotionEffectType.getById(13);
                case "INVISIBILITY":
                    return PotionEffectType.getById(14);
                case "BLINDNESS":
                    return PotionEffectType.getById(15);
                case "NIGHT_VISION":
                    return PotionEffectType.getById(16);
                case "HUNGER":
                    return PotionEffectType.getById(17);
                case "WEAKNESS":
                    return PotionEffectType.getById(18);
                case "POISON":
                    return PotionEffectType.getById(19);
                case "WITHER":
                    return PotionEffectType.getById(20);
                default:
                    return null;
            }
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * Áp dụng potion effect cho phiên bản cũ (không có particles parameter)
     */
    private static boolean addPotionEffectLegacy(LivingEntity entity, PotionEffectType effectType,
                                                 int duration, int amplifier, boolean ambient) {
        try {
            PotionEffect effect = new PotionEffect(effectType, duration, amplifier, ambient);
            return entity.addPotionEffect(effect);
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể áp dụng potion effect legacy: " + e.getMessage());
            }
            return false;
        }
    }
}
