package com.hongminh54.storage.Manager;

import com.hongminh54.storage.Storage;
import com.hongminh54.storage.Utils.File;
import org.bukkit.ChatColor;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;

import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Quản lý độ trễ khi chuyển khoáng sản giữa các người chơi
 * để đảm bảo tính nhất quán dữ liệu và tránh các lỗi khi giao dịch liên tục
 */
public class TransferDelayManager {
    // Lưu trữ thông tin người chơi đang có giao dịch đang xử lý
    private static final Map<UUID, TransferData> pendingTransfers = new ConcurrentHashMap<>();

    // Thời gian chờ mặc định (đơn vị: tick - 20 tick = 1 giây)
    private static final int DEFAULT_DELAY_TICKS = 40; // 2 giây

    /**
     * Kiểm tra người chơi có đang trong quá trình chuyển khoáng sản không
     *
     * @param player Người chơi cần kiểm tra
     * @return true nếu người chơi đang trong quá trình chuyển khoáng sản
     */
    public static boolean isPlayerTransferring(Player player) {
        if (player == null) return false;
        return pendingTransfers.containsKey(player.getUniqueId());
    }

    /**
     * Đăng ký một giao dịch chuyển khoáng sản mới
     *
     * @param sender   Người gửi
     * @param receiver Người nhận
     * @param material Loại khoáng sản
     * @param amount   Số lượng
     * @param callback Hàm thực hiện khi hoàn thành thời gian chờ
     * @return true nếu đăng ký thành công, false nếu người chơi đã có giao dịch đang xử lý
     */
    public static boolean registerTransfer(Player sender, Player receiver, String material,
                                           int amount, TransferCallback callback) {
        if (sender == null || receiver == null || material == null) {
            return false;
        }

        // Kiểm tra xem người chơi đã có giao dịch đang xử lý không
        if (isPlayerTransferring(sender)) {
            return false;
        }

        // Đọc cấu hình thời gian chờ
        int delayTicks = getTransferDelayTicks();

        // Gửi thông báo bắt đầu xử lý
        sender.sendMessage(ChatColor.translateAlternateColorCodes('&',
                "&8[&e&l⏱&8] &aĐang xử lý chuyển khoáng sản... &e(" + (delayTicks / 20.0) + "s)"));

        // Hiển thị thanh tiến độ (nếu thời gian > 1 giây)
        if (delayTicks >= 20) {
            showProgressBar(sender, delayTicks);
        }

        // Tạo task chờ
        BukkitTask task = new BukkitRunnable() {
            @Override
            public void run() {
                // Xóa khỏi danh sách đang xử lý
                pendingTransfers.remove(sender.getUniqueId());

                // Kiểm tra người chơi còn online không
                if (!sender.isOnline() || !receiver.isOnline()) {
                    return;
                }

                // Gọi callback
                if (callback != null) {
                    callback.onTransferReady(sender, receiver, material, amount);
                }
            }
        }.runTaskLater(Storage.getStorage(), delayTicks);

        // Lưu thông tin giao dịch đang xử lý
        pendingTransfers.put(sender.getUniqueId(),
                new TransferData(sender, receiver, material, amount, task));

        return true;
    }

    /**
     * Hủy giao dịch đang xử lý của người chơi
     *
     * @param player Người chơi
     * @return true nếu hủy thành công
     */
    public static boolean cancelTransfer(Player player) {
        if (player == null) return false;

        TransferData data = pendingTransfers.remove(player.getUniqueId());
        if (data != null && data.getTask() != null) {
            data.getTask().cancel();
            player.sendMessage(ChatColor.translateAlternateColorCodes('&',
                    "&8[&c&l✕&8] &cĐã hủy quá trình chuyển khoáng sản."));
            return true;
        }

        return false;
    }

    /**
     * Lấy thời gian chờ từ cấu hình
     *
     * @return Thời gian chờ theo ticks
     */
    private static int getTransferDelayTicks() {
        // Đọc từ config, mặc định là 2 giây
        int delaySeconds = File.getConfig().getInt("transfer.processing_delay", 2);

        // Giới hạn từ 1-5 giây
        delaySeconds = Math.max(1, Math.min(5, delaySeconds));

        return delaySeconds * 20; // Chuyển đổi sang ticks
    }

    /**
     * Hiển thị thanh tiến độ cho người chơi
     *
     * @param player     Người chơi
     * @param totalTicks Tổng số ticks
     */
    private static void showProgressBar(Player player, int totalTicks) {
        int interval = Math.max(4, totalTicks / 5); // Chia thành 5 phần, tối thiểu 4 ticks/lần

        for (int i = interval; i < totalTicks; i += interval) {
            final int step = i;
            new BukkitRunnable() {
                @Override
                public void run() {
                    if (!player.isOnline() || !isPlayerTransferring(player)) {
                        this.cancel();
                        return;
                    }

                    int progress = (step * 100) / totalTicks;
                    int bars = progress / 10;

                    StringBuilder barBuilder = new StringBuilder("&8[");
                    for (int j = 0; j < 10; j++) {
                        if (j < bars) {
                            barBuilder.append("&a■");
                        } else {
                            barBuilder.append("&7■");
                        }
                    }
                    barBuilder.append("&8] &e").append(progress).append("%");

                    player.sendMessage(ChatColor.translateAlternateColorCodes('&', barBuilder.toString()));
                }
            }.runTaskLater(Storage.getStorage(), i);
        }
    }

    /**
     * Interface callback khi hoàn thành thời gian chờ
     */
    public interface TransferCallback {
        void onTransferReady(Player sender, Player receiver, String material, int amount);
    }

    /**
     * Lớp lưu trữ thông tin giao dịch đang xử lý
     */
    private static class TransferData {
        private final Player sender;
        private final Player receiver;
        private final String material;
        private final int amount;
        private final BukkitTask task;

        public TransferData(Player sender, Player receiver, String material, int amount, BukkitTask task) {
            this.sender = sender;
            this.receiver = receiver;
            this.material = material;
            this.amount = amount;
            this.task = task;
        }

        public Player getSender() {
            return sender;
        }

        public Player getReceiver() {
            return receiver;
        }

        public String getMaterial() {
            return material;
        }

        public int getAmount() {
            return amount;
        }

        public BukkitTask getTask() {
            return task;
        }
    }
} 