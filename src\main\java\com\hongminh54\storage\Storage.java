package com.hongminh54.storage;

import com.hongminh54.storage.API.StorageAPI;
import com.hongminh54.storage.CMD.*;
import com.hongminh54.storage.Database.Database;
import com.hongminh54.storage.Database.PlayerData;
import com.hongminh54.storage.Database.SQLite;
import com.hongminh54.storage.Events.BlockBreakEvent_;
import com.hongminh54.storage.Events.EventScheduler;
import com.hongminh54.storage.Events.MiningEvent;
import com.hongminh54.storage.GUI.GUI;
import com.hongminh54.storage.GUI.LeaderboardGUI;
import com.hongminh54.storage.GUI.PlayerSearchGUI;
import com.hongminh54.storage.Handler.PlayerSearchChatHandler;
import com.hongminh54.storage.Handler.ResourceInputChatHandler;
import com.hongminh54.storage.Listeners.*;
import com.hongminh54.storage.Manager.*;
import com.hongminh54.storage.NMS.NMSAssistant;
import com.hongminh54.storage.Placeholder.PAPI;
import com.hongminh54.storage.Utils.*;
import com.hongminh54.storage.nexo.NexoIntegration;
import net.xconfig.bukkit.model.SimpleConfigurationManager;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.event.Listener;
import org.bukkit.plugin.java.JavaPlugin;

import java.util.*;
import java.util.logging.Level;

public final class Storage extends JavaPlugin {

    private static final List<UpdateChecker> updateCheckers = new ArrayList<>();
    public static Database db;
    private static Storage storage;
    private static boolean WorldGuard;
    private NMSAssistant nmsAssistant;

    private boolean debug = false;

    public static Storage getStorage() {
        return storage;
    }

    public static List<UpdateChecker> getUpdateCheckers() {
        return updateCheckers;
    }

    public static boolean isWorldGuardInstalled() {
        return WorldGuard;
    }

    public boolean isDebug() {
        return debug;
    }

    public void setDebug(boolean debug) {
        this.debug = debug;
    }

    @Override
    public void onLoad() {
        storage = this;
        if (getServer().getPluginManager().getPlugin("WorldGuard") != null) {
            WorldGuard = true;
            com.hongminh54.storage.WorldGuard.WorldGuard.register(storage);
            getLogger().log(Level.INFO, "Hook with WorldGuard");
        }
    }

    @Override
    public void onEnable() {
        storage = this;
        nmsAssistant = new NMSAssistant();

        if (!nmsAssistant.isSupportedVersion()) {
            getLogger().severe("Your server version is not supported.");
            getLogger().severe("Plugin will now disable itself.");
            getLogger().severe("Please update your server to a supported version (1.12.2 - 1.21.x)");
            Bukkit.getPluginManager().disablePlugin(this);
            return;
        }

        if (!getDataFolder().exists()) {
            getDataFolder().mkdirs();
        }

        LibraryLoader.loadAllLibraries();

        if (!LibraryLoader.testPluginDatabase()) {
            getLogger().warning("Database test failed - plugin may not work correctly");
        }

        Bukkit.getConsoleSender().sendMessage(" §e§lStorage");
        Bukkit.getConsoleSender().sendMessage(" ");
        Bukkit.getConsoleSender().sendMessage(" §f§oVersion: §6v1.0.5");
        Bukkit.getConsoleSender().sendMessage(" ");
        Bukkit.getConsoleSender().sendMessage(" §d§oSupport Minecraft Version: §a1.12.2 - 1.21.x");
        Bukkit.getConsoleSender().sendMessage(" ");
        Bukkit.getConsoleSender().sendMessage(" §c§oAuthor: §dVoChiDanh");
        Bukkit.getConsoleSender().sendMessage(" §b§oContributor: §dTYBZI, hongminh54");

        this.debug = getConfig().getBoolean("debug", false);
        if (debug) {
            getLogger().info("Debug mode enabled");
        }

        GUI.register(storage);
        SimpleConfigurationManager.register(storage);
        File.loadFiles();
        File.loadGUI();
        File.updateConfig();
        File.updateMessage();

        MiningEvent.getInstance().loadEventConfig();

        EventScheduler.initializeInstance().loadSchedules();

        SpecialMaterialManager.initialize();

        if (Bukkit.getPluginManager().getPlugin("PlaceholderAPI") != null) {
            new PAPI().register();
            getLogger().info("Hooked PlaceholderAPI");
        }

        // Nexo Hook
        if (Bukkit.getPluginManager().getPlugin("Nexo") != null) {
            try {
                NexoIntegration.isNexoAvailable();
                getLogger().info("Hooked Nexo - Custom model data integration enabled");
            } catch (Exception e) {
                getLogger().warning("Cloud not hook Nexo: " + e.getMessage());
            }
        }

        // API
        StorageAPI.initialize();
        getLogger().info("API has been initialized!");

        UpdateChecker updateChecker = new UpdateChecker(storage);
        updateCheckers.add(updateChecker);

        BlockBreakEvent_ blockBreakEvent = new BlockBreakEvent_();
        blockBreakEvent.scheduleCacheCleanup();

        // Register events
        registerEvents(updateChecker, new JoinQuitListener(), new BlockBreakListener(), new ChatListener(), new BlockPlaceListener(), blockBreakEvent, new TNTEnchantListener(), new AxeEnchantListener(), new HoeEnchantListener(), new ItemSpawnListener(), new SpecialMaterialListener(), new ServerCommandListener(this));
        updateChecker.fetch();

        // Register commands
        new StorageCMD("storage");
        new TNTEnchantCommand();
        new ViewStorageCommand();
        new AxeEnchantCommand();
        new HoeEnchantCommand();
        new SpecialMaterialCMD("specialmaterial");
        new ConvertBlockCMD("doiblock");

        // Skull test command (only for debug)
        Objects.requireNonNull(getCommand("testskull")).setExecutor(new TestSkullCMD());

        // Nexo test command (only for debug)
        if (debug) {
            new NexoTestCommand();
            getLogger().info("§eNexo test command đã được kích hoạt (debug mode)");
        }

        try {
            db = new SQLite(this);
            db.load();
        } catch (Exception e) {
            getLogger().severe("Error when loading database: " + e.getMessage());
            e.printStackTrace();
            getPluginLoader().disablePlugin(this);
            return;
        }

        try {
            ErrorLogger.initialize();
            getLogger().info("Initialized error logger");

            TransferMonitor.initialize();

            // Initialize transfer history table
            if (TransferManager.createTransferHistoryTable()) {
                getLogger().info("Initialized transfer history table");
            } else {
                getLogger().warning("Could not initialize transfer history table");
            }
        } catch (Exception e) {
            getLogger().severe("Error when initializing transfer monitor: " + e.getMessage());
            e.printStackTrace();
        }

        // Initialize Leaderboard Manager
        try {
            getLogger().info("Initializing Leaderboard Manager...");
            // Update all leaderboards
            LeaderboardManager.updateAllLeaderboards();
            getLogger().info("Leaderboard system initialized!");
        } catch (Exception e) {
            getLogger().severe("Error when initializing Leaderboard Manager: " + e.getMessage());
            e.printStackTrace();
        }

        MineManager.loadBlocks();

        // Update leaderboard when server is ready
        try {
            getLogger().info("Updating leaderboards...");
            LeaderboardManager.updateAllLeaderboards();
            getLogger().info("Leaderboards updated!");
        } catch (Exception e) {
            getLogger().warning("Error when updating leaderboards: " + e.getMessage());
        }

        // Register PlayerListener
        registerEvents(new PlayerListener(this));

        // Cleanup old backups
        try {
            getLogger().info("Cleaning up old backups...");
            MineManager.cleanupAllBackups();
        } catch (Exception e) {
            getLogger().warning("Error when cleaning up old backups: " + e.getMessage());
        }

        DataManager.initialize();

        // Intialize GemEffectManager
        GemEffectManager.getInstance();
        getServer().getPluginManager().registerEvents(new GemEffectListener(), this);

        MineManager.loadBlocks();

        setupNMSHandler();
        registerPAPI();
        initUpdateChecker();

        SpecialMaterialManager.reload();
        // Đã đăng ký trong registerEvents() ở trên - không cần duplicate

        LeaderboardManager.initialize();
    }

    @Override
    public void onDisable() {
        // Cancel all tasks
        Bukkit.getScheduler().cancelTasks(this);

        // Set flag to indicate server is shutting down
        PlayerListener.isServerShuttingDown = true;

        getLogger().info("Storage is shutting down...");
        getLogger().info("Saving player data...");

        // Cancel all chat inputs
        PlayerSearchChatHandler.cancelAllInputs();
        ResourceInputChatHandler.cancelAllInputs();

        // Stop MiningEvent if it's running
        MiningEvent.getInstance().endEvent();

        try {
            EventScheduler eventSchedulerInstance = EventScheduler.getInstance();
            if (eventSchedulerInstance != null) {
                getLogger().info("Cleaning up EventScheduler...");
                eventSchedulerInstance.stopScheduler();
            }
        } catch (Exception e) {
            getLogger().warning("Error when stopping EventScheduler: " + e.getMessage());
        }

        int playerCount = 0;
        List<String> failedPlayers = new ArrayList<>();

        for (Player p : getServer().getOnlinePlayers()) {
            try {
                getLogger().info("Saving data for " + p.getName() + "...");

                // Count items before saving
                Map<String, Integer> playerItems = new HashMap<>();
                int totalItems = 0;

                for (String material : MineManager.getPluginBlocks()) {
                    if (MineManager.hasPlayerBlock(p, material)) {
                        int amount = MineManager.getPlayerBlock(p, material);
                        if (amount > 0) {
                            playerItems.put(material, amount);
                            totalItems += amount;
                        }
                    }
                }

                getLogger().info("Counted " + playerItems.size() + " items for " + p.getName() + ": " + totalItems);

                // Use synchronous save methods
                StatsManager.savePlayerStatsSynchronously(p);
                MineManager.savePlayerData(p);
                playerCount++;

                // Remove from cache
                StatsManager.removeFromCache(p.getName());
                LeaderboardManager.removePlayerFromCache(p.getName());

                // Check if data is saved correctly
                PlayerData savedData = Storage.db.getData(p.getName());

                if (savedData != null) {
                    getLogger().info("Saved data for " + p.getName());

                    // Check if data is empty
                    if (totalItems > 0 && savedData.getData().equals("{}")) {
                        getLogger().warning("WARNING: Data for " + p.getName() + " is empty after saving, but should have " + totalItems + " items!");

                        // Try saving again
                        MineManager.savePlayerData(p);

                        // Check again
                        PlayerData recheckData = Storage.db.getData(p.getName());
                        if (recheckData != null && !recheckData.getData().equals("{}")) {
                            getLogger().info("Data for " + p.getName() + " is now saved correctly after retrying.");
                        } else {
                            failedPlayers.add(p.getName());
                        }
                    }
                }
            } catch (Exception ex) {
                getLogger().warning("Error when saving data for " + p.getName() + ": " + ex.getMessage());
                failedPlayers.add(p.getName());
                ex.printStackTrace();
            }
        }

        getLogger().info("Saved data for " + playerCount + " players.");

        if (!failedPlayers.isEmpty()) {
            getLogger().severe("Failed to save data for " + failedPlayers.size() + " players: " + String.join(", ", failedPlayers));
        }

        // Stop any Event if it's running
        MiningEvent.getInstance().endEvent();

        try {
            if (db != null) {
                getLogger().info("Flushing database operations...");
                db.closeAllThreadLocalConnections(); // It will call synchronizer.flushAll()
                getLogger().info("Flushed all database operations");
            }
        } catch (Exception e) {
            getLogger().warning("Error when flushing database operations: " + e.getMessage());
        }

        File.saveFiles();

        // Cleanup backup before shutting down
        try {
            getLogger().info("Cleaning up old backups...");
            MineManager.cleanupAllBackups();
            getLogger().info("Cleaned up old backups.");
        } catch (Exception e) {
            getLogger().warning("Cleanup failed: " + e.getMessage());
        }

        SpecialMaterialManager.saveConfig();
        GemEffectManager.getInstance().cleanup();
        LeaderboardGUI.clearHeadCache();
        PlayerSearchGUI.clearAllHeadCache();
        LeaderboardManager.shutdown();

        try {
            if (db != null) {
                getLogger().info("Closing database connection pool...");
                db.closeConnection();
                getLogger().info("Closed database connection pool.");
            }
        } catch (Exception e) {
            getLogger().warning("Error when closing database connection pool: " + e.getMessage());
        }

        getLogger().info("Plugin has been disabled! Thank you for using Storage!");
    }

    private void setupNMSHandler() {
        // Implementation of setupNMSHandler method
    }

    private void registerPAPI() {
        // Implementation of registerPAPI method
    }

    private void initUpdateChecker() {
        // Implementation of initUpdateChecker method
    }
    
    public void registerEvents(Listener... listeners) {
        Arrays.asList(listeners).forEach(listener -> getServer().getPluginManager().registerEvents(listener, storage));
    }
}