package com.hongminh54.storage.Listeners;

import com.hongminh54.storage.Events.MiningEvent;
import com.hongminh54.storage.Manager.MineManager;
import com.hongminh54.storage.Manager.SpecialMaterialManager;
import com.hongminh54.storage.Manager.SpecialMaterialManager.SpecialMaterial;
import com.hongminh54.storage.Manager.SpecialMaterialManager.SpecialMaterialResult;
import com.hongminh54.storage.Storage;
import com.hongminh54.storage.Utils.Chat;
import org.bukkit.Bukkit;
import org.bukkit.block.Block;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.block.BlockBreakEvent;
import org.bukkit.inventory.ItemStack;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Lớp này xử lý sự kiện tạo tài nguyên đặc biệt khi người chơi đào tài nguyên
 */
public class SpecialMaterialListener implements Listener {

    private static final long RATE_MESSAGE_COOLDOWN = 180000; // 3 phút (mili giây)
    // Thêm cơ chế cooldown cho thông báo tỉ lệ
    private final Map<UUID, Long> rateMessageCooldowns = new HashMap<>();

    public SpecialMaterialListener() {
        // Không cần tải cấu hình ở đây nữa, SpecialMaterialManager đã xử lý
    }

    /**
     * Xử lý sự kiện khi người chơi đào block và có thể nhận được khoáng sản đặc biệt
     *
     * @param event Sự kiện BlockBreakEvent
     */
    @EventHandler(priority = EventPriority.LOW, ignoreCancelled = true)
    public void onBlockBreak(BlockBreakEvent event) {
        // Nếu tính năng không được bật, không xử lý
        if (!SpecialMaterialManager.isEnabled()) {
            return;
        }

        Player player = event.getPlayer();
        Block block = event.getBlock();

        // Kiểm tra xem block có phải là khoáng sản được đăng ký không
        if (!MineManager.checkBreak(block)) {
            return;
        }

        // Kiểm tra xem tài nguyên đặc biệt có thể được tạo ra không
        SpecialMaterialResult result = SpecialMaterialManager.tryCreateSpecialMaterial(player, block);

        // Nếu không có tài nguyên đặc biệt được tạo ra, thoát
        if (result == null) {
            return;
        }

        ItemStack specialMaterial = result.getItemStack();

        // Thêm tài nguyên đặc biệt vào túi đồ của người chơi hoặc thả xuống đất
        boolean success = false;
        if (SpecialMaterialManager.hasInventorySpace(player)) {
            player.getInventory().addItem(specialMaterial);
            success = true;
        } else {
            // Kiểm tra auto pickup - nếu bật thì KHÔNG drop
            if (MineManager.isAutoPickup(player)) {
                // Không drop khi auto pickup bật - có thể gửi thông báo túi đồ đầy
                success = false;
            } else {
                // Thả xuống đất nếu không còn chỗ trong túi đồ và auto pickup tắt
                player.getWorld().dropItemNaturally(block.getLocation(), specialMaterial);
                success = true;
            }
        }

        // Phát hiệu ứng nếu thành công
        if (success) {
            // Phát hiệu ứng hạt và âm thanh
            SpecialMaterialManager.playEffects(player, result.getSpecialMaterial(), specialMaterial);

            // Lấy tên hiển thị của vật phẩm để sử dụng trong các thông báo
            String displayName = specialMaterial.hasItemMeta() && specialMaterial.getItemMeta().hasDisplayName()
                    ? specialMaterial.getItemMeta().getDisplayName()
                    : specialMaterial.getType().name();

            // Không cần gửi thông báo ở đây vì đã được xử lý trong playEffects

            // Kiểm tra xem có đang có sự kiện tăng tỷ lệ rơi không để thông báo
            MiningEvent eventManager = MiningEvent.getInstance();
            if (eventManager != null && eventManager.isActive()) {
                double multiplier = SpecialMaterialManager.getEventMultiplier();
                if (multiplier > 1.0) {
                    // Kiểm tra cooldown trước khi hiển thị thông báo về tỉ lệ rơi
                    UUID playerUUID = player.getUniqueId();
                    long lastNotified = rateMessageCooldowns.getOrDefault(playerUUID, 0L);
                    long currentTime = System.currentTimeMillis();

                    if (currentTime - lastNotified > RATE_MESSAGE_COOLDOWN) {
                        String eventName = eventManager.getCurrentEventType().getDisplayName();
                        String rate = String.format("%.1f", (multiplier - 1.0) * 100);

                        String eventBonusMessage = SpecialMaterialManager.getMessage("obtain.event_bonus",
                                "&e&l⚡ &eTỷ lệ rơi khoáng sản đặc biệt tăng &f%rate%% &etrong sự kiện &f%event_name%&e!");

                        player.sendMessage(Chat.colorize(eventBonusMessage
                                .replace("%rate%", rate)
                                .replace("%event_name%", eventName)));

                        // Cập nhật thời gian thông báo cuối cùng
                        rateMessageCooldowns.put(playerUUID, currentTime);
                    }
                }

                // Xử lý thông báo toàn server nếu được bật và trong sự kiện
                broadcastRareFind(player, result.getSpecialMaterial(), specialMaterial, eventManager);
            }

            // Ghi log debug nếu cần
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().info("Người chơi " + player.getName() + " đã nhận được tài nguyên đặc biệt: " + displayName);
            }
        }
    }

    /**
     * Thông báo toàn server khi người chơi nhận được tài nguyên hiếm
     *
     * @param player          Người chơi
     * @param specialMaterial Tài nguyên đặc biệt
     * @param itemStack       ItemStack của tài nguyên đặc biệt
     * @param eventManager    Sự kiện đang diễn ra
     */
    private void broadcastRareFind(Player player, SpecialMaterial specialMaterial, ItemStack itemStack, MiningEvent eventManager) {
        // Lấy cấu hình thông báo
        boolean enableServerBroadcast = Boolean.parseBoolean(
                SpecialMaterialManager.getMessage("obtain.enable_server_broadcast", "true"));
        double broadcastRarityThreshold = Double.parseDouble(
                SpecialMaterialManager.getMessage("obtain.broadcast_rarity_threshold", "1.0"));

        // Chỉ thông báo nếu được bật và tài nguyên đủ hiếm
        if (!enableServerBroadcast || specialMaterial.getChance() > broadcastRarityThreshold) {
            return;
        }

        // Chỉ thông báo đối với tài nguyên sự kiện hoặc tài nguyên cực hiếm
        boolean isEventMaterial = specialMaterial.getEventOnly() != null;

        if (isEventMaterial || specialMaterial.getChance() <= broadcastRarityThreshold) {
            String displayName = itemStack.hasItemMeta() && itemStack.getItemMeta().hasDisplayName()
                    ? itemStack.getItemMeta().getDisplayName()
                    : itemStack.getType().name();

            // Lấy tên của sự kiện đang diễn ra
            String eventName = eventManager.getCurrentEventType().getDisplayName();

            // Lấy thông báo từ cấu hình
            String serverBroadcastMessage = SpecialMaterialManager.getMessage("obtain.server_broadcast",
                    "&d&l✧ &d&k!&r &f%player% &dđã tìm thấy &f%item_name% &dtrong sự kiện &5%event_name%&d! &d&k!&r");

            // Định dạng thông báo
            String message = Chat.colorize(serverBroadcastMessage
                    .replace("%player%", player.getName())
                    .replace("%item_name%", displayName)
                    .replace("%event_name%", eventName));

            // Gửi thông báo toàn server
            Bukkit.broadcastMessage(message);
        }
    }
} 