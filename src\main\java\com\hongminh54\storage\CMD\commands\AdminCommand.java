package com.hongminh54.storage.CMD.commands;

import com.hongminh54.storage.Database.PlayerData;
import com.hongminh54.storage.Manager.MineManager;
import com.hongminh54.storage.Storage;
import com.hongminh54.storage.Utils.Chat;
import com.hongminh54.storage.Utils.File;
import com.hongminh54.storage.Utils.Number;
import org.bukkit.Bukkit;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.bukkit.util.StringUtil;

import java.util.*;

public class AdminCommand implements IStorageCommand {

    @Override
    public boolean execute(CommandSender sender, String[] args) {
        if (args[0].equalsIgnoreCase("reload")) {
            return handleReloadCommand(sender);
        } else if (args[0].equalsIgnoreCase("max")) {
            return handleMaxCommand(sender, args);
        } else if (args[0].equalsIgnoreCase("add") || args[0].equalsIgnoreCase("remove") || args[0].equalsIgnoreCase("set")) {
            return handleResourceCommand(sender, args);
        }

        return false;
    }

    private boolean handleReloadCommand(CommandSender sender) {
        if (!sender.hasPermission("storage.admin") && !sender.hasPermission("storage.admin.reload")) {
            sender.sendMessage(Chat.colorize(File.getMessage().getString("admin.no_permission")));
            return true;
        }

        File.reloadFiles();
        MineManager.loadBlocks();


        for (Player p : Storage.getStorage().getServer().getOnlinePlayers()) {
            MineManager.convertOfflineData(p);
            MineManager.loadPlayerData(p);
        }
        sender.sendMessage(Chat.colorize(File.getMessage().getString("admin.reload")));

        return true;
    }

    private boolean handleMaxCommand(CommandSender sender, String[] args) {
        if (!sender.hasPermission("storage.admin") && !sender.hasPermission("storage.admin.max")) {
            sender.sendMessage(Chat.colorize(File.getMessage().getString("admin.no_permission")));
            return true;
        }

        if (args.length != 3) {
            sender.sendMessage(Chat.colorize("&cSử dụng: /kho max <người_chơi> <số_lượng>"));
            return true;
        }

        Player player = Bukkit.getPlayer(args[1]);
        if (player == null) {
            sender.sendMessage(Chat.colorize("&cNgười chơi không trực tuyến!"));
            return true;
        }

        int amount = Number.getInteger(args[2]);
        if (amount <= 0) {
            sender.sendMessage(Chat.colorize("&cSố lượng phải lớn hơn 0!"));
            return true;
        }

        MineManager.playermaxdata.put(player, amount);
        sender.sendMessage(Chat.colorize(Objects.requireNonNull(File.getMessage().getString("admin.set_max_storage"))
                .replace("#player#", player.getName())
                .replace("#amount#", String.valueOf(amount))));

        return true;
    }

    private boolean handleResourceCommand(CommandSender sender, String[] args) {
        if (args.length != 4) {
            sender.sendMessage(Chat.colorize("&cSử dụng: /kho " + args[0] + " <khoáng_sản> <người_chơi> <số_lượng>"));
            return true;
        }

        String command = args[0].toLowerCase();
        String material = args[1];
        String targetName = args[2];

        // Kiểm tra quyền
        if (!hasResourcePermission(sender, command)) {
            sender.sendMessage(Chat.colorize(File.getMessage().getString("admin.no_permission")));
            return true;
        }

        // Kiểm tra khoáng sản có tồn tại
        if (!MineManager.getPluginBlocks().contains(material)) {
            sender.sendMessage(Chat.colorize("&cKhoáng sản không tồn tại: " + material));
            return true;
        }

        int number = Number.getInteger(args[3]);
        if (number < 0) {
            sender.sendMessage(Chat.colorize("&cSố lượng không hợp lệ!"));
            return true;
        }

        Player targetPlayer = Bukkit.getPlayer(targetName);

        switch (command) {
            case "add":
                return handleAddCommand(sender, material, targetPlayer, targetName, number);
            case "remove":
                return handleRemoveCommand(sender, material, targetPlayer, targetName, number);
            case "set":
                return handleSetCommand(sender, material, targetPlayer, targetName, number);
        }

        return true;
    }

    private boolean hasResourcePermission(CommandSender sender, String command) {
        if (sender.hasPermission("storage.admin")) {
            return true;
        }

        switch (command) {
            case "add":
                return sender.hasPermission("storage.admin.add");
            case "remove":
                return sender.hasPermission("storage.admin.remove");
            case "set":
                return sender.hasPermission("storage.admin.set");
            default:
                return false;
        }
    }

    private boolean handleAddCommand(CommandSender sender, String material, Player targetPlayer, String targetName, int amount) {
        if (targetPlayer != null) {
            // Người chơi online
            Storage.getStorage().getLogger().info("Thực hiện lệnh /kho add: " + material + " cho " + targetPlayer.getName() + " số lượng " + amount);

            // Kiểm tra nếu tài nguyên không tồn tại trong kho của người chơi, thì khởi tạo
            if (!MineManager.hasPlayerBlock(targetPlayer, material)) {
                Storage.getStorage().getLogger().info("Khởi tạo tài nguyên " + material + " cho " + targetPlayer.getName());
                MineManager.setBlock(targetPlayer, material, 0);
            }

            // Thực hiện thêm tài nguyên
            if (MineManager.addBlockAmount(targetPlayer, material, amount)) {
                sender.sendMessage(Chat.colorize(File.getMessage().getString("admin.add_material_amount"))
                        .replace("#amount#", Integer.toString(amount))
                        .replace("#material#", material)
                        .replace("#player#", targetPlayer.getName()));
                targetPlayer.sendMessage(Chat.colorize(File.getMessage().getString("user.add_material_amount"))
                        .replace("#amount#", Integer.toString(amount))
                        .replace("#material#", material)
                        .replace("#player#", sender.getName()));

                // Ghi log xác nhận số lượng sau khi thêm
                int currentAmount = MineManager.getPlayerBlock(targetPlayer, material);
                Storage.getStorage().getLogger().info("Đã thêm thành công: " + material + " cho " + targetPlayer.getName() + ", số lượng hiện tại: " + currentAmount);

                // Lưu dữ liệu ngay lập tức
                MineManager.savePlayerDataAsync(targetPlayer);
            } else {
                sender.sendMessage(Chat.colorize("&c❌ Không thể thêm tài nguyên. Có thể do kho đã đầy hoặc lỗi dữ liệu."));
                Storage.getStorage().getLogger().warning("Không thể thêm tài nguyên " + material + " cho " + targetPlayer.getName() + ", số lượng: " + amount);
            }
        } else {
            // Người chơi offline
            handleOfflineAddCommand(sender, material, targetName, amount);
        }

        return true;
    }

    private boolean handleRemoveCommand(CommandSender sender, String material, Player targetPlayer, String targetName, int amount) {
        if (targetPlayer != null) {
            // Người chơi online
            if (MineManager.removeBlockAmount(targetPlayer, material, amount)) {
                sender.sendMessage(Chat.colorize(File.getMessage().getString("admin.remove_material_amount"))
                        .replace("#amount#", String.valueOf(amount))
                        .replace("#material#", material)
                        .replace("#player#", targetPlayer.getName()));
                targetPlayer.sendMessage(Chat.colorize(File.getMessage().getString("user.remove_material_amount"))
                        .replace("#amount#", String.valueOf(amount))
                        .replace("#material#", material)
                        .replace("#player#", sender.getName()));
            } else {
                sender.sendMessage(Chat.colorize("&cKhông thể xóa tài nguyên. Có thể do không đủ số lượng."));
            }
        } else {
            sender.sendMessage(Chat.colorize("&cNgười chơi không online. Tính năng xóa khoáng sản cho người chơi offline hiện không được hỗ trợ."));
        }

        return true;
    }

    private boolean handleSetCommand(CommandSender sender, String material, Player targetPlayer, String targetName, int amount) {
        if (targetPlayer != null) {
            // Người chơi online
            Storage.getStorage().getLogger().info("Thực hiện lệnh /kho set: " + material + " cho " + targetPlayer.getName() + " thành " + amount);

            // Thực hiện đặt tài nguyên
            MineManager.setBlock(targetPlayer, material, amount);
            sender.sendMessage(Chat.colorize(File.getMessage().getString("admin.set_material_amount"))
                    .replace("#amount#", Integer.toString(amount))
                    .replace("#material#", material)
                    .replace("#player#", targetPlayer.getName()));
            targetPlayer.sendMessage(Chat.colorize(File.getMessage().getString("user.set_material_amount"))
                    .replace("#amount#", Integer.toString(amount))
                    .replace("#material#", material)
                    .replace("#player#", sender.getName()));

            // Lưu dữ liệu ngay lập tức
            MineManager.savePlayerDataAsync(targetPlayer);
        } else {
            // Người chơi offline
            handleOfflineSetCommand(sender, material, targetName, amount);
        }

        return true;
    }

    private void handleOfflineAddCommand(CommandSender sender, String material, String targetName, int amount) {
        try {
            Storage.getStorage().getLogger().info("Thực hiện lệnh /kho add cho người chơi offline: " + material + " cho " + targetName + " số lượng " + amount);

            // Lấy dữ liệu từ database
            PlayerData playerData = Storage.db.getData(targetName);
            if (playerData == null) {
                sender.sendMessage(Chat.colorize("&c❌ Không tìm thấy dữ liệu người chơi: " + targetName));
                return;
            }

            // Parse dữ liệu blocks
            Map<String, Integer> blockMap = parseBlockData(playerData.getData());

            // Lấy giới hạn kho
            int maxStorage = playerData.getMax();
            if (maxStorage <= 0) {
                maxStorage = File.getConfig().getInt("settings.default_max_storage", 1000000);
            }

            // Cập nhật số lượng tài nguyên
            boolean foundResource = false;
            if (blockMap.containsKey(material)) {
                foundResource = true;
                int currentAmount = blockMap.get(material);
                int newAmount = currentAmount + amount;

                if (newAmount <= maxStorage) {
                    blockMap.put(material, newAmount);
                    sender.sendMessage(Chat.colorize("&a✓ Đã thêm &e" + amount + " " + material + "&a vào kho của &e" + targetName + "&a. Số lượng hiện tại: &e" + newAmount));
                } else {
                    blockMap.put(material, maxStorage);
                    sender.sendMessage(Chat.colorize("&e⚠ Kho đã đầy. Đã đặt " + material + " cho " + targetName + " thành giới hạn tối đa: " + maxStorage));
                }
            } else {
                // Tài nguyên chưa tồn tại, thêm mới
                int finalAmount = Math.min(amount, maxStorage);
                blockMap.put(material, finalAmount);
                foundResource = true;

                if (finalAmount < amount) {
                    sender.sendMessage(Chat.colorize("&e⚠ Vượt quá giới hạn kho. Đã thêm " + material + " cho " + targetName + " với số lượng tối đa: " + maxStorage));
                } else {
                    sender.sendMessage(Chat.colorize("&a✓ Đã thêm &e" + amount + " " + material + "&a vào kho của &e" + targetName));
                }
            }

            if (foundResource) {
                // Chuyển Map thành chuỗi JSON đúng định dạng
                String updatedBlocks = mapToString(blockMap);

                // Tạo PlayerData mới với dữ liệu đã cập nhật
                PlayerData updatedPlayerData = new PlayerData(
                        targetName,
                        updatedBlocks,
                        maxStorage,
                        playerData.getStatsData()
                );

                // Cập nhật vào database
                Storage.db.updateTable(updatedPlayerData);
                Storage.getStorage().getLogger().info("Đã cập nhật dữ liệu offline cho " + targetName + ": " + material + " = " + blockMap.get(material));
            }

        } catch (Exception e) {
            sender.sendMessage(Chat.colorize("&c❌ Lỗi khi thêm tài nguyên cho người chơi offline: " + e.getMessage()));
            Storage.getStorage().getLogger().severe("Lỗi khi thêm tài nguyên offline: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void handleOfflineSetCommand(CommandSender sender, String material, String targetName, int amount) {
        try {
            Storage.getStorage().getLogger().info("Thực hiện lệnh /kho set cho người chơi offline: " + material + " cho " + targetName + " thành " + amount);

            // Lấy dữ liệu từ database
            PlayerData playerData = Storage.db.getData(targetName);
            if (playerData == null) {
                sender.sendMessage(Chat.colorize("&c❌ Không tìm thấy dữ liệu người chơi: " + targetName));
                return;
            }

            // Parse dữ liệu blocks
            Map<String, Integer> blockMap = parseBlockData(playerData.getData());

            // Lấy giới hạn kho
            int maxStorage = playerData.getMax();
            if (maxStorage <= 0) {
                maxStorage = File.getConfig().getInt("settings.default_max_storage", 1000000);
            }

            // Giới hạn số lượng không vượt quá maxStorage
            int finalAmount = Math.min(amount, maxStorage);

            // Cập nhật số lượng hoặc thêm mới
            blockMap.put(material, finalAmount);

            if (finalAmount < amount) {
                sender.sendMessage(Chat.colorize("&e⚠ Vượt quá giới hạn kho. Đã đặt " + material + " cho " + targetName + " thành giá trị tối đa: " + maxStorage));
            } else {
                sender.sendMessage(Chat.colorize("&a✓ Đã đặt &e" + material + "&a cho &e" + targetName + "&a thành &e" + finalAmount));
            }

            // Chuyển Map thành chuỗi JSON đúng định dạng
            String updatedBlocks = mapToString(blockMap);

            // Tạo PlayerData mới với dữ liệu đã cập nhật
            PlayerData updatedPlayerData = new PlayerData(
                    targetName,
                    updatedBlocks,
                    maxStorage,
                    playerData.getStatsData()
            );

            // Cập nhật vào database
            Storage.db.updateTable(updatedPlayerData);
            Storage.getStorage().getLogger().info("Đã cập nhật dữ liệu offline cho " + targetName + ": " + material + " = " + finalAmount);

        } catch (Exception e) {
            sender.sendMessage(Chat.colorize("&c❌ Lỗi khi đặt tài nguyên cho người chơi offline: " + e.getMessage()));
            Storage.getStorage().getLogger().severe("Lỗi khi đặt tài nguyên offline: " + e.getMessage());
            e.printStackTrace();
        }
    }

    // Helper methods
    private Map<String, Integer> parseBlockData(String blocksData) {
        Map<String, Integer> blockMap = new HashMap<>();

        if (blocksData == null || blocksData.trim().isEmpty() || blocksData.equals("{}")) {
            return blockMap;
        }

        // Xóa dấu ngoặc nhọn
        String cleanData = blocksData.replace("{", "").replace("}", "").trim();

        if (cleanData.isEmpty()) {
            return blockMap;
        }

        // Tách các cặp key-value
        String[] pairs = cleanData.split(",");
        for (String pair : pairs) {
            String[] keyValue = pair.trim().split("=");
            if (keyValue.length == 2) {
                try {
                    String key = keyValue[0].trim();
                    int value = Integer.parseInt(keyValue[1].trim());
                    blockMap.put(key, value);
                } catch (NumberFormatException e) {
                    Storage.getStorage().getLogger().warning("Không thể parse giá trị: " + pair);
                }
            }
        }

        return blockMap;
    }

    private String mapToString(Map<String, Integer> blockMap) {
        StringBuilder mapAsString = new StringBuilder("{");
        for (Map.Entry<String, Integer> entry : blockMap.entrySet()) {
            mapAsString.append(entry.getKey()).append("=").append(entry.getValue()).append(", ");
        }

        // Xóa dấu phẩy và khoảng trắng cuối cùng
        if (mapAsString.length() > 1) {
            mapAsString.setLength(mapAsString.length() - 2);
        }
        mapAsString.append("}");

        return mapAsString.toString();
    }

    private List<String> getAllPlayerNames(String input) {
        List<String> names = new ArrayList<>();
        String lowerInput = input.toLowerCase();

        // Thêm người chơi online
        for (Player player : Bukkit.getOnlinePlayers()) {
            if (player.getName().toLowerCase().startsWith(lowerInput)) {
                names.add(player.getName());
            }
        }

        // TODO: Có thể thêm người chơi offline từ database nếu cần

        return names;
    }

    @Override
    public List<String> getTabComplete(CommandSender sender, String[] args) {
        List<String> completions = new ArrayList<>();

        if (args.length == 2) {
            // Tab completion cho materials (add/remove/set) hoặc players (max)
            if (args[0].equalsIgnoreCase("add") || args[0].equalsIgnoreCase("remove") || args[0].equalsIgnoreCase("set")) {
                List<String> materials = new ArrayList<>(MineManager.getPluginBlocks());
                StringUtil.copyPartialMatches(args[1], materials, completions);
            } else if (args[0].equalsIgnoreCase("max")) {
                List<String> playerNames = new ArrayList<>();
                for (Player player : Bukkit.getOnlinePlayers()) {
                    playerNames.add(player.getName());
                }
                StringUtil.copyPartialMatches(args[1], playerNames, completions);
            }
        } else if (args.length == 3) {
            // Tab completion cho players (add/remove/set) hoặc amount (max)
            if (args[0].equalsIgnoreCase("add") || args[0].equalsIgnoreCase("remove") || args[0].equalsIgnoreCase("set")) {
                if (MineManager.getPluginBlocks().contains(args[1])) {
                    // Sử dụng getAllPlayerNames để hiển thị người chơi online và offline cho lệnh add và set
                    if (args[0].equalsIgnoreCase("add") || args[0].equalsIgnoreCase("set")) {
                        StringUtil.copyPartialMatches(args[2], getAllPlayerNames(args[2]), completions);
                    } else {
                        // Đối với lệnh remove, chỉ hiển thị người chơi online
                        List<String> playerNames = new ArrayList<>();
                        for (Player player : Bukkit.getOnlinePlayers()) {
                            playerNames.add(player.getName());
                        }
                        StringUtil.copyPartialMatches(args[2], playerNames, completions);
                    }
                }
            } else if (args[0].equalsIgnoreCase("max")) {
                completions.add("<number>");
            }
        } else if (args.length == 4) {
            // Tab completion cho amount (add/remove/set)
            if (args[0].equalsIgnoreCase("add") || args[0].equalsIgnoreCase("remove") || args[0].equalsIgnoreCase("set")) {
                if (MineManager.getPluginBlocks().contains(args[1])) {
                    completions.add("<number>");
                }
            }
        }

        return completions;
    }

    @Override
    public String getCommandName() {
        return "admin";
    }

    @Override
    public List<String> getAliases() {
        return Arrays.asList("add", "remove", "set", "max", "reload");
    }

    @Override
    public boolean hasPermission(CommandSender sender) {
        return sender.hasPermission("storage.admin") ||
                sender.hasPermission("storage.admin.add") ||
                sender.hasPermission("storage.admin.remove") ||
                sender.hasPermission("storage.admin.set") ||
                sender.hasPermission("storage.admin.max") ||
                sender.hasPermission("storage.admin.reload");
    }
}
