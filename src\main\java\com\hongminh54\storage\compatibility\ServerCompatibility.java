package com.hongminh54.storage.compatibility;

import com.hongminh54.storage.NMS.NMSAssistant;
import com.hongminh54.storage.Storage;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;

import java.lang.reflect.Method;
import java.util.Collection;
import java.util.Collections;

/**
 * Lớp hỗ trợ tương thích Server API cho Minecraft 1.12.2 - 1.21.x
 * X<PERSON> lý các vấn đề tương thích với server methods, scheduler, v.v.
 */
public class ServerCompatibility {

    private static final NMSAssistant nmsAssistant = new NMSAssistant();
    private static final boolean IS_PRE_113 = nmsAssistant.isVersionLessThan(13);
    private static final boolean IS_PRE_116 = nmsAssistant.isVersionLessThan(16);
    private static final boolean IS_1_20_OR_HIGHER = nmsAssistant.isVersionGreaterThanOrEqualTo(20);
    private static final boolean IS_1_20_5_OR_HIGHER = nmsAssistant.is1_20_5OrHigher();
    private static final boolean IS_1_21_4_OR_HIGHER = nmsAssistant.is1_21_4OrHigher();

    /**
     * Lấy danh sách người chơi online một cách an toàn
     *
     * @return Collection của Player
     */
    public static Collection<? extends Player> getOnlinePlayers() {
        try {
            return Bukkit.getOnlinePlayers();
        } catch (Exception e) {
            // Fallback với reflection cho phiên bản rất cũ
            return getOnlinePlayersReflection();
        }
    }

    /**
     * Lấy số lượng người chơi online một cách an toàn
     *
     * @return Số lượng người chơi online
     */
    public static int getOnlinePlayerCount() {
        try {
            return Bukkit.getOnlinePlayers().size();
        } catch (Exception e) {
            return getOnlinePlayersReflection().size();
        }
    }

    /**
     * Lấy TPS của server một cách an toàn
     *
     * @return TPS hiện tại hoặc -1 nếu không thể lấy
     */
    public static double getCurrentTPS() {
        try {
            if (IS_1_20_OR_HIGHER) {
                // Minecraft 1.20+: Sử dụng Bukkit API
                // Minecraft 1.20+ có method getTPS()
                try {
                    java.lang.reflect.Method getTpsMethod = Bukkit.class.getMethod("getTPS");
                    double[] tps = (double[]) getTpsMethod.invoke(null);
                    return tps[0]; // TPS của 1 phút gần nhất
                } catch (Exception e) {
                    return getTpsReflection();
                }
            } else {
                // Phiên bản cũ: Sử dụng reflection
                return getTpsReflection();
            }
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể lấy TPS: " + e.getMessage());
            }
            return -1.0;
        }
    }

    /**
     * Chạy task async một cách an toàn
     *
     * @param runnable Task cần chạy
     * @return BukkitTask hoặc null nếu có lỗi
     */
    public static BukkitTask runTaskAsync(Runnable runnable) {
        if (runnable == null) {
            return null;
        }

        try {
            return Bukkit.getScheduler().runTaskAsynchronously(Storage.getStorage(), runnable);
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể chạy async task: " + e.getMessage());
            }
            return null;
        }
    }

    /**
     * Chạy task sync một cách an toàn
     *
     * @param runnable Task cần chạy
     * @return BukkitTask hoặc null nếu có lỗi
     */
    public static BukkitTask runTaskSync(Runnable runnable) {
        if (runnable == null) {
            return null;
        }

        try {
            return Bukkit.getScheduler().runTask(Storage.getStorage(), runnable);
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể chạy sync task: " + e.getMessage());
            }
            return null;
        }
    }

    /**
     * Chạy task delayed một cách an toàn
     *
     * @param runnable Task cần chạy
     * @param delay    Độ trễ (ticks)
     * @return BukkitTask hoặc null nếu có lỗi
     */
    public static BukkitTask runTaskLater(Runnable runnable, long delay) {
        if (runnable == null || delay < 0) {
            return null;
        }

        try {
            return Bukkit.getScheduler().runTaskLater(Storage.getStorage(), runnable, delay);
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể chạy delayed task: " + e.getMessage());
            }
            return null;
        }
    }

    /**
     * Chạy task timer một cách an toàn
     *
     * @param runnable Task cần chạy
     * @param delay    Độ trễ ban đầu (ticks)
     * @param period   Chu kỳ lặp lại (ticks)
     * @return BukkitTask hoặc null nếu có lỗi
     */
    public static BukkitTask runTaskTimer(Runnable runnable, long delay, long period) {
        if (runnable == null || delay < 0 || period <= 0) {
            return null;
        }

        try {
            return Bukkit.getScheduler().runTaskTimer(Storage.getStorage(), runnable, delay, period);
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể chạy timer task: " + e.getMessage());
            }
            return null;
        }
    }

    /**
     * Hủy task một cách an toàn
     *
     * @param task Task cần hủy
     * @return true nếu hủy thành công
     */
    public static boolean cancelTask(BukkitTask task) {
        if (task == null) {
            return false;
        }

        try {
            task.cancel();
            return true;
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể hủy task: " + e.getMessage());
            }
            return false;
        }
    }

    /**
     * Kiểm tra xem có đang chạy trên main thread không
     *
     * @return true nếu đang ở main thread
     */
    public static boolean isPrimaryThread() {
        try {
            return Bukkit.isPrimaryThread();
        } catch (Exception e) {
            // Fallback: kiểm tra thread name
            return Thread.currentThread().getName().equals("Server thread");
        }
    }

    /**
     * Lấy max players của server
     *
     * @return Số lượng player tối đa
     */
    public static int getMaxPlayers() {
        try {
            return Bukkit.getMaxPlayers();
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể lấy max players: " + e.getMessage());
            }
            return 20; // Default fallback
        }
    }

    /**
     * Lấy server name
     *
     * @return Tên server
     */
    public static String getServerName() {
        try {
            try {
                java.lang.reflect.Method getServerNameMethod = Bukkit.class.getMethod("getServerName");
                return (String) getServerNameMethod.invoke(null);
            } catch (Exception e) {
                return "Unknown";
            }
        } catch (Exception e) {
            return "Unknown";
        }
    }

    /**
     * Lấy server version
     *
     * @return Phiên bản server
     */
    public static String getServerVersion() {
        try {
            return Bukkit.getVersion();
        } catch (Exception e) {
            return "Unknown";
        }
    }

    /**
     * Lấy Bukkit version
     *
     * @return Phiên bản Bukkit
     */
    public static String getBukkitVersion() {
        try {
            return Bukkit.getBukkitVersion();
        } catch (Exception e) {
            return "Unknown";
        }
    }

    /**
     * Kiểm tra xem server có đang shutdown không
     *
     * @return true nếu đang shutdown
     */
    public static boolean isShuttingDown() {
        try {
            // Phương pháp gián tiếp: kiểm tra scheduler
            return Bukkit.getScheduler() == null;
        } catch (Exception e) {
            return true; // Assume shutting down if error
        }
    }

    /**
     * Lấy danh sách player online sử dụng reflection (cho phiên bản rất cũ)
     */
    @SuppressWarnings("unchecked")
    private static Collection<? extends Player> getOnlinePlayersReflection() {
        try {
            Method getOnlinePlayersMethod = Bukkit.class.getMethod("getOnlinePlayers");
            Object result = getOnlinePlayersMethod.invoke(null);

            if (result instanceof Collection) {
                return (Collection<? extends Player>) result;
            } else if (result instanceof Player[]) {
                // Phiên bản rất cũ trả về array
                Player[] players = (Player[]) result;
                return java.util.Arrays.asList(players);
            }
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Lỗi reflection getOnlinePlayers: " + e.getMessage());
            }
        }

        return Collections.emptyList();
    }

    /**
     * Lấy TPS sử dụng reflection cho phiên bản cũ
     */
    private static double getTpsReflection() {
        try {
            String version = Bukkit.getServer().getClass().getPackage().getName().split("\\.")[3];
            Class<?> minecraftServerClass = Class.forName("net.minecraft.server." + version + ".MinecraftServer");

            Method getServerMethod = minecraftServerClass.getMethod("getServer");
            Object minecraftServer = getServerMethod.invoke(null);

            // Lấy recentTps field
            java.lang.reflect.Field recentTpsField = minecraftServerClass.getField("recentTps");
            double[] recentTps = (double[]) recentTpsField.get(minecraftServer);

            return recentTps[0]; // TPS của 1 phút gần nhất
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Lỗi reflection TPS: " + e.getMessage());
            }
            return 20.0; // Default TPS
        }
    }

    /**
     * Chạy BukkitRunnable một cách an toàn
     *
     * @param bukkitRunnable BukkitRunnable cần chạy
     * @return BukkitTask hoặc null nếu có lỗi
     */
    public static BukkitTask runBukkitRunnable(BukkitRunnable bukkitRunnable) {
        if (bukkitRunnable == null) {
            return null;
        }

        try {
            return bukkitRunnable.runTask(Storage.getStorage());
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể chạy BukkitRunnable: " + e.getMessage());
            }
            return null;
        }
    }

    /**
     * Chạy BukkitRunnable async một cách an toàn
     *
     * @param bukkitRunnable BukkitRunnable cần chạy
     * @return BukkitTask hoặc null nếu có lỗi
     */
    public static BukkitTask runBukkitRunnableAsync(BukkitRunnable bukkitRunnable) {
        if (bukkitRunnable == null) {
            return null;
        }

        try {
            return bukkitRunnable.runTaskAsynchronously(Storage.getStorage());
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể chạy BukkitRunnable async: " + e.getMessage());
            }
            return null;
        }
    }

    /**
     * Lấy TPS của server (alias cho getCurrentTPS)
     *
     * @return TPS hiện tại hoặc -1 nếu không thể lấy
     */
    public static double getServerTPS() {
        return getCurrentTPS();
    }
}
