package com.hongminh54.storage.CMD.commands;

import com.hongminh54.storage.Events.EventScheduler;
import com.hongminh54.storage.Events.MiningEvent;
import com.hongminh54.storage.Utils.Chat;
import com.hongminh54.storage.Utils.File;
import org.bukkit.command.CommandSender;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.util.StringUtil;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class EventCommand implements IStorageCommand {

    @Override
    public boolean execute(CommandSender sender, String[] args) {
        if (args.length < 2) {
            sender.sendMessage(Chat.colorize(File.getEvents().getString("commands.messages.usage")));
            return true;
        }

        MiningEvent miningEvent = MiningEvent.getInstance();

        if (args[1].equalsIgnoreCase("start")) {
            return handleStartCommand(sender, args, miningEvent);
        } else if (args[1].equalsIgnoreCase("stop")) {
            return handleStopCommand(sender, miningEvent);
        } else if (args[1].equalsIgnoreCase("info")) {
            return handleInfoCommand(sender, miningEvent);
        } else if (args[1].equalsIgnoreCase("reload")) {
            return handleReloadCommand(sender);
        } else if (args[1].equalsIgnoreCase("schedule")) {
            return handleScheduleCommand(sender);
        } else {
            sender.sendMessage(Chat.colorize(File.getEvents().getString("commands.messages.usage")));
        }

        return true;
    }

    private boolean handleStartCommand(CommandSender sender, String[] args, MiningEvent miningEvent) {
        if (miningEvent.isActive()) {
            sender.sendMessage(Chat.colorize("&cĐã có sự kiện đang diễn ra! Hãy dừng sự kiện hiện tại trước khi bắt đầu sự kiện mới."));
            return true;
        }

        int duration = File.getEvents().getInt("event.default_duration", 1800);
        MiningEvent.EventType eventType = MiningEvent.EventType.DOUBLE_DROP;

        if (args.length >= 3) {
            try {
                eventType = MiningEvent.EventType.valueOf(args[2].toUpperCase());
            } catch (IllegalArgumentException e) {
                sender.sendMessage(Chat.colorize("&cLoại sự kiện không hợp lệ! Các loại sự kiện: DOUBLE_DROP, FORTUNE_BOOST, RARE_MATERIALS, COMMUNITY_GOAL"));
                return true;
            }
        }

        if (args.length >= 4) {
            try {
                duration = Integer.parseInt(args[3]);
            } catch (NumberFormatException e) {
                sender.sendMessage(Chat.colorize("&cThời gian không hợp lệ! Vui lòng nhập một số nguyên dương."));
                return true;
            }
        }

        miningEvent.startEvent(eventType, duration);
        sender.sendMessage(Chat.colorize(File.getEvents().getString("commands.messages.start")
                .replace("%event_type%", eventType.getDisplayName())
                .replace("%duration%", String.valueOf(duration))));

        return true;
    }

    private boolean handleStopCommand(CommandSender sender, MiningEvent miningEvent) {
        if (!miningEvent.isActive()) {
            sender.sendMessage(Chat.colorize(File.getEvents().getString("commands.messages.no_event")));
            return true;
        }

        miningEvent.endEvent();
        sender.sendMessage(Chat.colorize(File.getEvents().getString("commands.messages.stop")));

        return true;
    }

    private boolean handleInfoCommand(CommandSender sender, MiningEvent miningEvent) {
        if (!miningEvent.isActive()) {
            sender.sendMessage(Chat.colorize(File.getEvents().getString("commands.messages.no_event")));
            return true;
        }

        sender.sendMessage(Chat.colorize("&a&lThông tin sự kiện:"));
        sender.sendMessage(Chat.colorize("&eLoại: &f" + miningEvent.getCurrentEventType().getDisplayName()));
        sender.sendMessage(Chat.colorize("&eThời gian còn lại: &f" + formatTime(miningEvent.getRemainingTime())));
        sender.sendMessage(Chat.colorize("&eThời gian tổng: &f" + formatTime(miningEvent.getEventDuration())));

        // Hiển thị thông tin đặc biệt cho từng loại sự kiện
        switch (miningEvent.getCurrentEventType()) {
            case DOUBLE_DROP:
                sender.sendMessage(Chat.colorize("&eHiệu ứng: &fTất cả tài nguyên khai thác được nhân đôi"));
                break;
            case FORTUNE_BOOST:
                sender.sendMessage(Chat.colorize("&eHiệu ứng: &fTài nguyên khai thác được tăng 1.5x - 3x ngẫu nhiên"));
                break;
            case RARE_MATERIALS:
                sender.sendMessage(Chat.colorize("&eHiệu ứng: &fTỷ lệ rơi khoáng sản đặc biệt tăng cao"));
                break;
            case COMMUNITY_GOAL:
                sender.sendMessage(Chat.colorize("&eHiệu ứng: &fCộng đồng cùng nhau đạt mục tiêu khai thác"));
                // Hiển thị tiến độ cộng đồng nếu có
                break;
        }

        return true;
    }

    private boolean handleReloadCommand(CommandSender sender) {
        // Tải lại cấu hình sự kiện và lịch trình
        File.loadEvents();
        MiningEvent.getInstance().loadEventConfig();
        EventScheduler.getInstance().loadSchedules();
        sender.sendMessage(Chat.colorize("&aĐã tải lại cấu hình sự kiện và lịch trình!"));

        return true;
    }

    private boolean handleScheduleCommand(CommandSender sender) {
        // Hiển thị thông tin lịch trình sự kiện
        sender.sendMessage(Chat.colorize("&a&lLịch trình sự kiện tự động:"));
        sender.sendMessage(Chat.colorize("&eTrạng thái: " + (File.getEvents().getBoolean("scheduler.enable", true) ? "&aBật" : "&cTắt")));
        sender.sendMessage(Chat.colorize("&eKiểm tra: &fMỗi " + File.getEvents().getInt("scheduler.check_interval", 5) + " phút"));
        sender.sendMessage(Chat.colorize("&eCác sự kiện theo lịch:"));

        ConfigurationSection schedules = File.getEvents().getConfigurationSection("scheduler.schedules");
        if (schedules != null) {
            for (String eventId : schedules.getKeys(false)) {
                String type = schedules.getString(eventId + ".type", "UNKNOWN");
                int duration = schedules.getInt(eventId + ".duration", 0);
                int minutes = duration / 60;
                String days = schedules.getString(eventId + ".days", "ALL");

                // Hiển thị thông tin sự kiện
                sender.sendMessage(Chat.colorize("  &6" + eventId + ": &f" + type + " &7(Kéo dài " + minutes + " phút)"));

                // Hiển thị thời gian
                StringBuilder timeInfo = new StringBuilder("    &eThời gian: &f");
                if (schedules.contains(eventId + ".time")) {
                    // Định dạng cũ
                    timeInfo.append(schedules.getString(eventId + ".time", "00:00"));
                } else if (schedules.contains(eventId + ".times")) {
                    // Định dạng mới
                    List<String> times = schedules.getStringList(eventId + ".times");
                    if (!times.isEmpty()) {
                        for (int i = 0; i < times.size(); i++) {
                            timeInfo.append(times.get(i));
                            if (i < times.size() - 1) {
                                timeInfo.append(", ");
                            }
                        }
                    }
                }
                sender.sendMessage(Chat.colorize(timeInfo.toString()));

                // Hiển thị thông tin ngày
                sender.sendMessage(Chat.colorize("    &eNgày: &f" + days));
            }
        } else {
            sender.sendMessage(Chat.colorize("&cKhông có sự kiện nào được cấu hình."));
        }

        return true;
    }

    private String formatTime(int seconds) {
        int hours = seconds / 3600;
        int minutes = (seconds % 3600) / 60;
        int secs = seconds % 60;

        if (hours > 0) {
            return String.format("%d giờ %d phút %d giây", hours, minutes, secs);
        } else if (minutes > 0) {
            return String.format("%d phút %d giây", minutes, secs);
        } else {
            return String.format("%d giây", secs);
        }
    }

    @Override
    public List<String> getTabComplete(CommandSender sender, String[] args) {
        List<String> completions = new ArrayList<>();

        if (args.length == 2) {
            List<String> subCommands = Arrays.asList("start", "stop", "info", "reload", "schedule");
            StringUtil.copyPartialMatches(args[1], subCommands, completions);
        } else if (args.length == 3 && args[1].equalsIgnoreCase("start")) {
            // Tab completion cho loại sự kiện
            List<String> eventTypes = new ArrayList<>();
            for (MiningEvent.EventType type : MiningEvent.EventType.values()) {
                if (type != MiningEvent.EventType.NONE) {
                    eventTypes.add(type.name());
                }
            }
            StringUtil.copyPartialMatches(args[2], eventTypes, completions);
        } else if (args.length == 4 && args[1].equalsIgnoreCase("start")) {
            // Tab completion cho thời gian
            completions.add("<thời_gian>");
        }

        return completions;
    }

    @Override
    public String getCommandName() {
        return "event";
    }

    @Override
    public List<String> getAliases() {
        return new ArrayList<>();
    }

    @Override
    public boolean hasPermission(CommandSender sender) {
        return sender.hasPermission("storage.event");
    }
}
