package com.hongminh54.storage.API;

import com.hongminh54.storage.Manager.MineManager;
import com.hongminh54.storage.Manager.StatsManager;
import com.hongminh54.storage.NMS.NMSAssistant;
import org.bukkit.block.Block;
import org.bukkit.entity.Player;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * API chính thức của plugin KhoKhoangSanPlus.
 * Cung cấp các phương thức để tương tác với kho khoáng sản của người chơi.
 */
public final class StorageAPI {

    private static final NMSAssistant nmsAssistant = new NMSAssistant();
    private static boolean initialized = false;

    /**
     * Khởi tạo API. Phương thức này được gọi tự động khi plugin được enable.
     */
    public static void initialize() {
        initialized = true;
    }

    /**
     * Lấy phiên bản API hiện tại
     *
     * @return Phiên bản API
     */
    public static String getAPIVersion() {
        return "1.0.5";
    }

    /**
     * Kiểm tra tương thích phiên bản Minecraft
     *
     * @return true nếu phiên bản được hỗ trợ
     */
    public static boolean isMinecraftVersionSupported() {
        return nmsAssistant.isSupportedVersion();
    }

    /**
     * Lấy thông tin phiên bản Minecraft hiện tại
     *
     * @return Chuỗi thông tin phiên bản
     */
    public static String getMinecraftVersionInfo() {
        return nmsAssistant.getNMSVersion().toString();
    }

    /**
     * Kiểm tra xem API đã được khởi tạo chưa.
     *
     * @return true nếu API đã được khởi tạo
     */
    public static boolean isInitialized() {
        return initialized;
    }

    /**
     * Lấy số lượng khoáng sản của một người chơi
     *
     * @param player   Người chơi
     * @param material Loại khoáng sản
     * @return Số lượng khoáng sản
     */
    public static int getPlayerResourceAmount(Player player, String material) {
        checkInitialized();
        return MineManager.getPlayerBlock(player, material);
    }

    /**
     * Thêm khoáng sản vào kho của người chơi
     *
     * @param player   Người chơi
     * @param material Loại khoáng sản
     * @param amount   Số lượng
     * @return true nếu thêm thành công
     */
    public static boolean addPlayerResource(Player player, String material, int amount) {
        checkInitialized();
        return MineManager.addBlockAmount(player, material, amount);
    }

    /**
     * Trừ khoáng sản từ kho của người chơi
     *
     * @param player   Người chơi
     * @param material Loại khoáng sản
     * @param amount   Số lượng
     * @return true nếu trừ thành công
     */
    public static boolean removePlayerResource(Player player, String material, int amount) {
        checkInitialized();
        return MineManager.removeBlockAmount(player, material, amount);
    }

    /**
     * Kiểm tra xem kho của người chơi đã đầy chưa
     *
     * @param player Người chơi
     * @return true nếu kho đã đầy
     */
    public static boolean isStorageFull(Player player) {
        checkInitialized();
        for (String material : MineManager.getPluginBlocks()) {
            int currentAmount = MineManager.getPlayerBlock(player, material);
            int maxStorage = MineManager.getMaxBlock(player);
            if (currentAmount < maxStorage) {
                return false;
            }
        }
        return true;
    }

    /**
     * Kiểm tra xem kho của người chơi cho một loại khoáng sản cụ thể đã đầy chưa
     *
     * @param player   Người chơi
     * @param material Loại khoáng sản
     * @return true nếu kho cho loại khoáng sản đó đã đầy
     */
    public static boolean isResourceFull(Player player, String material) {
        checkInitialized();
        int currentAmount = MineManager.getPlayerBlock(player, material);
        int maxStorage = MineManager.getMaxBlock(player);
        return currentAmount >= maxStorage;
    }

    /**
     * Lấy giới hạn kho của người chơi
     *
     * @param player Người chơi
     * @return Giới hạn kho
     */
    public static int getStorageLimit(Player player) {
        checkInitialized();
        return MineManager.getMaxBlock(player);
    }

    /**
     * Kiểm tra xem người chơi có bật tự động nhặt không
     *
     * @param player Người chơi
     * @return true nếu đã bật tự động nhặt
     */
    public static boolean isAutoPickupEnabled(Player player) {
        checkInitialized();
        return MineManager.isAutoPickup(player);
    }

    /**
     * Đặt trạng thái tự động nhặt cho người chơi
     *
     * @param player  Người chơi
     * @param enabled true để bật, false để tắt
     */
    public static void setAutoPickup(Player player, boolean enabled) {
        checkInitialized();
        MineManager.toggle.put(player, enabled);
    }

    /**
     * Lấy danh sách tất cả các loại khoáng sản được hỗ trợ
     *
     * @return Danh sách các loại khoáng sản
     */
    public static List<String> getSupportedMaterials() {
        checkInitialized();
        return MineManager.getPluginBlocks();
    }

    /**
     * Lấy tất cả khoáng sản của người chơi
     *
     * @param player Người chơi
     * @return Map chứa loại khoáng sản và số lượng
     */
    public static Map<String, Integer> getAllPlayerResources(Player player) {
        checkInitialized();
        Map<String, Integer> resources = new HashMap<>();
        for (String material : MineManager.getPluginBlocks()) {
            int amount = MineManager.getPlayerBlock(player, material);
            if (amount > 0) {
                resources.put(material, amount);
            }
        }
        return resources;
    }

    /**
     * Chuyển khoáng sản từ người chơi này sang người chơi khác
     *
     * @param sender   Người gửi
     * @param receiver Người nhận
     * @param material Loại khoáng sản
     * @param amount   Số lượng
     * @return true nếu chuyển thành công
     */
    public static boolean transferResource(Player sender, Player receiver, String material, int amount) {
        checkInitialized();
        Map<String, Integer> materials = new HashMap<>();
        materials.put(material, amount);
        Map<String, Integer> result = MineManager.transferResources(sender, receiver, materials);
        return result.containsKey(material) && result.get(material) == amount;
    }

    /**
     * Lưu dữ liệu kho của người chơi vào cơ sở dữ liệu
     *
     * @param player Người chơi
     */
    public static void savePlayerStorage(Player player) {
        checkInitialized();
        MineManager.savePlayerDataAsync(player);
    }

    /**
     * Tải lại dữ liệu kho của người chơi từ cơ sở dữ liệu
     *
     * @param player Người chơi
     */
    public static void reloadPlayerStorage(Player player) {
        checkInitialized();
        MineManager.loadPlayerData(player);
    }

    /**
     * Lấy tổng số khoáng sản đã khai thác của người chơi
     *
     * @param player Người chơi
     * @return Tổng số khoáng sản đã khai thác
     */
    public static int getTotalMined(Player player) {
        checkInitialized();
        return StatsManager.getTotalMined(player);
    }

    /**
     * Lấy tổng số khoáng sản đã gửi vào kho của người chơi
     *
     * @param player Người chơi
     * @return Tổng số khoáng sản đã gửi vào kho
     */
    public static int getTotalDeposited(Player player) {
        checkInitialized();
        return StatsManager.getTotalDeposited(player);
    }

    /**
     * Lấy tổng số khoáng sản đã rút ra từ kho của người chơi
     *
     * @param player Người chơi
     * @return Tổng số khoáng sản đã rút ra
     */
    public static int getTotalWithdrawn(Player player) {
        checkInitialized();
        return StatsManager.getTotalWithdrawn(player);
    }

    /**
     * Lấy tổng số khoáng sản đã bán của người chơi
     *
     * @param player Người chơi
     * @return Tổng số khoáng sản đã bán
     */
    public static int getTotalSold(Player player) {
        checkInitialized();
        return StatsManager.getTotalSold(player);
    }

    /**
     * Lấy danh sách thông tin thống kê của người chơi
     *
     * @param player Người chơi
     * @return Danh sách các thông tin thống kê
     */
    public static List<String> getStatsInfo(Player player) {
        checkInitialized();
        return StatsManager.getStatsInfo(player);
    }

    /**
     * Kiểm tra xem một khối có phải là khối khoáng sản hợp lệ không
     *
     * @param block Khối cần kiểm tra
     * @return true nếu là khối khoáng sản hợp lệ
     */
    public static boolean isValidOreBlock(Block block) {
        checkInitialized();
        return MineManager.checkBreak(block);
    }

    /**
     * Kiểm tra xem một loại vật liệu có thể chuyển đổi thành khối không
     *
     * @param material Tên vật liệu cần kiểm tra (ví dụ: IRON_INGOT, DIAMOND)
     * @return true nếu vật liệu có thể chuyển đổi thành khối
     */
    public static boolean canConvertToBlock(String material) {
        checkInitialized();
        if (material == null || material.isEmpty()) {
            return false;
        }

        return getBlockMaterial(material) != null;
    }

    /**
     * Lấy tên khối tương ứng với một loại vật liệu
     *
     * @param material Tên vật liệu (ví dụ: IRON_INGOT, DIAMOND)
     * @return Tên khối tương ứng hoặc null nếu không tìm thấy
     */
    public static String getBlockMaterial(String material) {
        checkInitialized();
        if (material == null || material.isEmpty()) {
            return null;
        }

        String materialData = material.contains(";") ? material : material + ";0";
        String mat = materialData.split(";")[0].toUpperCase();
        String data = materialData.split(";")[1];

        // Map các loại phôi với block tương ứng
        switch (mat) {
            case "IRON_INGOT":
                return "IRON_BLOCK;" + data;
            case "GOLD_INGOT":
                return "GOLD_BLOCK;" + data;
            case "DIAMOND":
                return "DIAMOND_BLOCK;" + data;
            case "EMERALD":
                return "EMERALD_BLOCK;" + data;
            case "COAL":
                return "COAL_BLOCK;" + data;
            case "REDSTONE":
                return "REDSTONE_BLOCK;" + data;
            case "LAPIS_LAZULI":
                return "LAPIS_BLOCK;" + data;
            // Hỗ trợ cho các loại phôi mới (1.16+)
            case "COPPER_INGOT":
                return "COPPER_BLOCK;" + data;
            case "NETHERITE_INGOT":
                return "NETHERITE_BLOCK;" + data;
            // Các vật liệu khác có thể đổi thành block
            case "QUARTZ":
                return "QUARTZ_BLOCK;" + data;
            case "AMETHYST_SHARD":
                return "AMETHYST_BLOCK;" + data;
            // Hỗ trợ thêm cho Raw Ores (1.17+)
            case "RAW_IRON":
                return "RAW_IRON_BLOCK;" + data;
            case "RAW_GOLD":
                return "RAW_GOLD_BLOCK;" + data;
            case "RAW_COPPER":
                return "RAW_COPPER_BLOCK;" + data;
            default:
                return null;
        }
    }

    /**
     * Chuyển đổi vật liệu từ phôi sang khối
     *
     * @param player   Người chơi thực hiện chuyển đổi
     * @param material Tên vật liệu cần chuyển đổi (ví dụ: IRON_INGOT, DIAMOND)
     * @param amount   Số lượng phôi muốn chuyển đổi (-1 để chuyển đổi nhiều nhất có thể)
     * @return ConversionResult chứa thông tin kết quả chuyển đổi
     */
    public static ConversionResult convertToBlock(Player player, String material, int amount) {
        checkInitialized();

        // Validate input
        if (player == null || material == null || material.isEmpty()) {
            return new ConversionResult(false, "Invalid input parameters", 0, 0);
        }

        if (!canConvertToBlock(material)) {
            return new ConversionResult(false, "Material cannot be converted to block", 0, 0);
        }

        String materialData = material.contains(";") ? material : material + ";0";
        String blockMaterial = getBlockMaterial(materialData);
        if (blockMaterial == null) {
            return new ConversionResult(false, "Block material not found", 0, 0);
        }

        // Số lượng phôi cần để tạo 1 block
        final int INGOTS_PER_BLOCK = getIngotsPerBlock(materialData);
        int currentAmount = MineManager.getPlayerBlock(player, materialData);

        if (currentAmount <= 0) {
            return new ConversionResult(false, "No materials available", 0, 0);
        }

        // Tính toán số lượng phôi sẽ sử dụng
        int ingotsToUse = calculateIngotsToUse(currentAmount, amount, INGOTS_PER_BLOCK);

        if (ingotsToUse < INGOTS_PER_BLOCK) {
            return new ConversionResult(false, "Not enough materials to create a block", 0, 0);
        }

        int blocksToCreate = ingotsToUse / INGOTS_PER_BLOCK;

        // Kiểm tra không gian trống trong kho
        if (!hasEnoughSpace(player, blockMaterial, blocksToCreate)) {
            return new ConversionResult(false, "Not enough storage space", 0, 0);
        }

        // Thực hiện chuyển đổi
        return performConversion(player, materialData, blockMaterial, ingotsToUse, blocksToCreate, true);
    }

    /**
     * Lấy số lượng khối có thể tạo ra từ số lượng phôi hiện có của người chơi
     *
     * @param player   Người chơi
     * @param material Tên vật liệu cần kiểm tra (ví dụ: IRON_INGOT, DIAMOND)
     * @return Số lượng khối có thể tạo ra
     */
    public static int getPossibleBlocksToCreate(Player player, String material) {
        checkInitialized();
        if (!canConvertToBlock(material)) {
            return 0;
        }

        String materialData = material.contains(";") ? material : material + ";0";
        int currentAmount = MineManager.getPlayerBlock(player, materialData);

        // Số lượng phôi cần để tạo 1 block
        final int INGOTS_PER_BLOCK = getIngotsPerBlock(materialData);

        return currentAmount / INGOTS_PER_BLOCK;
    }

    /**
     * Lấy số lượng phôi có thể tạo ra từ số lượng khối hiện có của người chơi
     *
     * @param player        Người chơi
     * @param blockMaterial Tên khối cần kiểm tra (ví dụ: IRON_BLOCK, DIAMOND_BLOCK)
     * @return Số lượng phôi có thể tạo ra
     */
    public static int getPossibleIngotsToCreate(Player player, String blockMaterial) {
        checkInitialized();
        if (!canConvertToIngot(blockMaterial)) {
            return 0;
        }

        String blockData = blockMaterial.contains(";") ? blockMaterial : blockMaterial + ";0";
        int currentAmount = MineManager.getPlayerBlock(player, blockData);

        // Số lượng phôi nhận được từ 1 block
        final int INGOTS_PER_BLOCK = getIngotsPerBlock(blockData);

        return currentAmount * INGOTS_PER_BLOCK;
    }

    /**
     * Kiểm tra xem có thể thực hiện chuyển đổi không (kiểm tra đầy đủ điều kiện)
     *
     * @param player         Người chơi
     * @param inputMaterial  Vật liệu đầu vào
     * @param outputMaterial Vật liệu đầu ra
     * @param inputAmount    Số lượng đầu vào
     * @param outputAmount   Số lượng đầu ra
     * @return ConversionResult với thông tin kiểm tra (success = false nếu không thể thực hiện)
     */
    public static ConversionResult canPerformConversion(Player player, String inputMaterial, String outputMaterial,
                                                        int inputAmount, int outputAmount) {
        checkInitialized();

        if (player == null || inputMaterial == null || outputMaterial == null) {
            return new ConversionResult(false, "Invalid parameters", 0, 0);
        }

        // Kiểm tra số lượng hiện có
        int currentInput = MineManager.getPlayerBlock(player, inputMaterial);
        if (currentInput < inputAmount) {
            return new ConversionResult(false, "Not enough input materials", 0, 0);
        }

        // Kiểm tra không gian trống
        if (!hasEnoughSpace(player, outputMaterial, outputAmount)) {
            return new ConversionResult(false, "Not enough storage space", 0, 0);
        }

        return new ConversionResult(true, "Conversion is possible", inputAmount, outputAmount);
    }

    /**
     * Lấy tỷ lệ chuyển đổi giữa phôi và khối
     *
     * @return Số lượng phôi cần để tạo 1 khối
     */
    public static int getConversionRatio() {
        return 9; // Minecraft vanilla ratio
    }

    /**
     * Lấy vật liệu phôi tương ứng với một loại khối
     *
     * @param blockMaterial Tên khối (ví dụ: IRON_BLOCK, DIAMOND_BLOCK)
     * @return Tên phôi tương ứng hoặc null nếu không tìm thấy
     */
    public static String getIngotMaterial(String blockMaterial) {
        checkInitialized();
        if (blockMaterial == null || blockMaterial.isEmpty()) {
            return null;
        }

        String blockData = blockMaterial.contains(";") ? blockMaterial : blockMaterial + ";0";
        String block = blockData.split(";")[0].toUpperCase();
        String data = blockData.split(";")[1];

        // Map các loại block với phôi tương ứng
        switch (block) {
            case "IRON_BLOCK":
                return "IRON_INGOT;" + data;
            case "GOLD_BLOCK":
                return "GOLD_INGOT;" + data;
            case "DIAMOND_BLOCK":
                return "DIAMOND;" + data;
            case "EMERALD_BLOCK":
                return "EMERALD;" + data;
            case "COAL_BLOCK":
                return "COAL;" + data;
            case "REDSTONE_BLOCK":
                return "REDSTONE;" + data;
            case "LAPIS_BLOCK":
                return "LAPIS_LAZULI;" + data;
            // Hỗ trợ cho các loại block mới (1.16+)
            case "COPPER_BLOCK":
                return "COPPER_INGOT;" + data;
            case "NETHERITE_BLOCK":
                return "NETHERITE_INGOT;" + data;
            // Các khối khác có thể chuyển thành vật liệu
            case "QUARTZ_BLOCK":
                return "QUARTZ;" + data;
            case "AMETHYST_BLOCK":
                return "AMETHYST_SHARD;" + data;
            // Hỗ trợ thêm cho Raw Ore Blocks (1.17+)
            case "RAW_IRON_BLOCK":
                return "RAW_IRON;" + data;
            case "RAW_GOLD_BLOCK":
                return "RAW_GOLD;" + data;
            case "RAW_COPPER_BLOCK":
                return "RAW_COPPER;" + data;
            default:
                return null;
        }
    }

    /**
     * Kiểm tra xem một loại khối có thể chuyển đổi thành phôi không
     *
     * @param blockMaterial Tên khối cần kiểm tra (ví dụ: IRON_BLOCK, DIAMOND_BLOCK)
     * @return true nếu khối có thể chuyển đổi thành phôi
     */
    public static boolean canConvertToIngot(String blockMaterial) {
        checkInitialized();
        return getIngotMaterial(blockMaterial) != null;
    }

    /**
     * Chuyển đổi khối thành phôi
     *
     * @param player        Người chơi thực hiện chuyển đổi
     * @param blockMaterial Tên khối cần chuyển đổi (ví dụ: IRON_BLOCK, DIAMOND_BLOCK)
     * @param amount        Số lượng khối muốn chuyển đổi (-1 để chuyển đổi nhiều nhất có thể)
     * @return ConversionResult chứa thông tin kết quả chuyển đổi
     */
    public static ConversionResult convertToIngot(Player player, String blockMaterial, int amount) {
        checkInitialized();

        // Validate input
        if (player == null || blockMaterial == null || blockMaterial.isEmpty()) {
            return new ConversionResult(false, "Invalid input parameters", 0, 0);
        }

        if (!canConvertToIngot(blockMaterial)) {
            return new ConversionResult(false, "Block cannot be converted to ingots", 0, 0);
        }

        String blockData = blockMaterial.contains(";") ? blockMaterial : blockMaterial + ";0";
        String ingotMaterial = getIngotMaterial(blockData);
        if (ingotMaterial == null) {
            return new ConversionResult(false, "Ingot material not found", 0, 0);
        }

        // Số lượng phôi nhận được từ 1 block
        final int INGOTS_PER_BLOCK = getIngotsPerBlock(blockData);
        int currentBlockAmount = MineManager.getPlayerBlock(player, blockData);

        if (currentBlockAmount <= 0) {
            return new ConversionResult(false, "No blocks available", 0, 0);
        }

        // Tính toán số lượng khối sẽ sử dụng
        int blocksToUse = (amount == -1) ? currentBlockAmount : Math.min(amount, currentBlockAmount);

        if (blocksToUse <= 0) {
            return new ConversionResult(false, "No blocks to convert", 0, 0);
        }

        int ingotsToCreate = blocksToUse * INGOTS_PER_BLOCK;

        // Kiểm tra không gian trống trong kho
        if (!hasEnoughSpace(player, ingotMaterial, ingotsToCreate)) {
            return new ConversionResult(false, "Not enough storage space", 0, 0);
        }

        // Thực hiện chuyển đổi
        return performConversion(player, blockData, ingotMaterial, blocksToUse, ingotsToCreate, true);
    }

    /**
     * Lấy số lượng phôi cần để tạo 1 block
     *
     * @param material Tên vật liệu
     * @return Số lượng phôi cần thiết
     */
    private static int getIngotsPerBlock(String material) {
        // Mặc định là 9 phôi = 1 block (theo Minecraft vanilla)
        // Có thể tùy chỉnh cho từng loại vật liệu nếu cần
        return 9;
    }

    /**
     * Tính toán số lượng phôi sẽ sử dụng
     *
     * @param currentAmount   Số lượng hiện có
     * @param requestedAmount Số lượng yêu cầu (-1 để dùng tất cả)
     * @param ingotsPerBlock  Số phôi cần cho 1 block
     * @return Số lượng phôi sẽ sử dụng
     */
    private static int calculateIngotsToUse(int currentAmount, int requestedAmount, int ingotsPerBlock) {
        if (requestedAmount == -1) {
            // Chuyển đổi nhiều nhất có thể (làm tròn xuống)
            return (currentAmount / ingotsPerBlock) * ingotsPerBlock;
        } else {
            // Sử dụng số lượng yêu cầu nếu đủ
            return Math.min(requestedAmount, currentAmount);
        }
    }

    /**
     * Kiểm tra xem có đủ không gian trong kho không
     *
     * @param player   Người chơi
     * @param material Loại vật liệu
     * @param amount   Số lượng cần thêm
     * @return true nếu có đủ không gian
     */
    private static boolean hasEnoughSpace(Player player, String material, int amount) {
        int maxStorage = MineManager.getMaxBlock(player);
        int currentAmount = MineManager.getPlayerBlock(player, material);
        return (maxStorage - currentAmount) >= amount;
    }

    /**
     * Thực hiện quá trình chuyển đổi
     *
     * @param player         Người chơi
     * @param inputMaterial  Vật liệu đầu vào
     * @param outputMaterial Vật liệu đầu ra
     * @param inputAmount    Số lượng đầu vào
     * @param outputAmount   Số lượng đầu ra
     * @param saveData       Có lưu dữ liệu ngay lập tức không
     * @return Kết quả chuyển đổi
     */
    private static ConversionResult performConversion(Player player, String inputMaterial, String outputMaterial,
                                                      int inputAmount, int outputAmount, boolean saveData) {
        // Thực hiện trừ vật liệu đầu vào
        boolean removeSuccess = MineManager.removeBlockAmount(player, inputMaterial, inputAmount);
        if (!removeSuccess) {
            return new ConversionResult(false, "Failed to remove input materials", 0, 0);
        }

        // Thực hiện thêm vật liệu đầu ra
        boolean addSuccess = MineManager.addBlockAmount(player, outputMaterial, outputAmount);
        if (!addSuccess) {
            // Nếu thêm thất bại, hoàn trả lại vật liệu đầu vào
            MineManager.addBlockAmount(player, inputMaterial, inputAmount);
            return new ConversionResult(false, "Failed to add output materials", 0, 0);
        }

        // Lưu dữ liệu nếu được yêu cầu
        if (saveData) {
            MineManager.savePlayerDataAsync(player);
        }

        return new ConversionResult(true, "Conversion successful", inputAmount, outputAmount);
    }

    /**
     * Kiểm tra xem API đã được khởi tạo chưa, nếu chưa thì ném exception
     */
    private static void checkInitialized() {
        if (!initialized) {
            throw new IllegalStateException("StorageAPI has not been initialized yet!");
        }
    }

    /**
     * Class chứa kết quả của quá trình chuyển đổi
     */
    public static class ConversionResult {
        private final boolean success;
        private final String message;
        private final int inputUsed;
        private final int outputCreated;

        public ConversionResult(boolean success, String message, int inputUsed, int outputCreated) {
            this.success = success;
            this.message = message;
            this.inputUsed = inputUsed;
            this.outputCreated = outputCreated;
        }

        /**
         * @return true nếu chuyển đổi thành công
         */
        public boolean isSuccess() {
            return success;
        }

        /**
         * @return Thông báo kết quả
         */
        public String getMessage() {
            return message;
        }

        /**
         * @return Số lượng vật liệu đầu vào đã sử dụng
         */
        public int getInputUsed() {
            return inputUsed;
        }

        /**
         * @return Số lượng vật liệu đầu ra đã tạo
         */
        public int getOutputCreated() {
            return outputCreated;
        }

        @Override
        public String toString() {
            return String.format("ConversionResult{success=%s, message='%s', inputUsed=%d, outputCreated=%d}",
                    success, message, inputUsed, outputCreated);
        }
    }
} 