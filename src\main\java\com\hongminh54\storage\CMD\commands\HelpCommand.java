package com.hongminh54.storage.CMD.commands;

import com.hongminh54.storage.Utils.Chat;
import com.hongminh54.storage.Utils.File;
import org.bukkit.command.CommandSender;

import java.util.ArrayList;
import java.util.List;

public class HelpCommand implements IStorageCommand {

    @Override
    public boolean execute(CommandSender sender, String[] args) {
        if (sender.hasPermission("storage.admin")) {
            File.getMessage().getStringList("admin.help").forEach(s -> sender.sendMessage(Chat.colorize(s)));
        }
        File.getMessage().getStringList("user.help").forEach(s -> sender.sendMessage(Chat.colorize(s)));
        return true;
    }

    @Override
    public List<String> getTabComplete(CommandSender sender, String[] args) {
        return new ArrayList<>();
    }

    @Override
    public String getCommandName() {
        return "help";
    }

    @Override
    public List<String> getAliases() {
        return new ArrayList<>();
    }

    @Override
    public boolean hasPermission(CommandSender sender) {
        return true; // Help command có thể được sử dụng bởi tất cả mọi người
    }
}
