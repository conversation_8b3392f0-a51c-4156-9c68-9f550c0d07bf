package com.hongminh54.storage.CMD.commands;

import com.hongminh54.storage.GUI.PlayerSearchGUI;
import com.hongminh54.storage.Manager.SoundManager;
import com.hongminh54.storage.Utils.Chat;
import com.hongminh54.storage.Utils.File;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

public class SearchCommand implements IStorageCommand {

    @Override
    public boolean execute(CommandSender sender, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(Chat.colorize("&8[&4&l✕&8] &cLệnh này chỉ có thể được sử dụng bởi người chơi!"));
            return true;
        }

        Player player = (Player) sender;

        try {
            // Mở GUI tìm kiếm người chơi
            player.openInventory(new PlayerSearchGUI(player).getInventory());

            // Phát âm thanh thành công
            String successSoundConfig = File.getConfig().getString("effects.search_open.sound", "UI_BUTTON_CLICK:0.5:1.0");
            SoundManager.playSoundFromConfig(player, successSoundConfig);

        } catch (Exception e) {
            player.sendMessage(Chat.colorize("&8[&4&l✕&8] &cLỗi khi mở giao diện tìm kiếm: " + e.getMessage()));

            // Phát âm thanh thất bại
            String failSoundConfig = File.getConfig().getString("effects.search_fail.sound", "ENTITY_VILLAGER_NO:1.0:1.0");
            SoundManager.playSoundFromConfig(player, failSoundConfig);

            e.printStackTrace();
        }

        return true;
    }

    @Override
    public List<String> getTabComplete(CommandSender sender, String[] args) {
        return new ArrayList<>();
    }

    @Override
    public String getCommandName() {
        return "search";
    }

    @Override
    public List<String> getAliases() {
        return Collections.singletonList("timkiem");
    }

    @Override
    public boolean hasPermission(CommandSender sender) {
        return sender.hasPermission("storage.transfer");
    }
}
