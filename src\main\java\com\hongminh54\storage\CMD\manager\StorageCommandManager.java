package com.hongminh54.storage.CMD.manager;

import com.hongminh54.storage.CMD.commands.*;
import com.hongminh54.storage.GUI.PersonalStorage;
import com.hongminh54.storage.Utils.Chat;
import com.hongminh54.storage.Utils.File;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class StorageCommandManager {

    private final Map<String, IStorageCommand> commands = new HashMap<>();
    private final Map<String, String> aliases = new HashMap<>();

    public StorageCommandManager() {
        registerCommands();
    }

    private void registerCommands() {
        // Đăng ký các command
        registerCommand(new HelpCommand());
        registerCommand(new ToggleCommand());
        registerCommand(new StatsCommand());
        registerCommand(new LeaderboardCommand());
        registerCommand(new TransferCommand());
        registerCommand(new LogCommand());
        registerCommand(new SearchCommand());
        registerCommand(new EventCommand());
        registerCommand(new AdminCommand());
        registerCommand(new ResetCommand());
        registerCommand(new UpdateCommand());

        // DoiBlockCommand được xử lý bởi ConvertBlockCMD riêng biệt
    }

    private void registerCommand(IStorageCommand command) {
        commands.put(command.getCommandName().toLowerCase(), command);

        // Đăng ký aliases
        for (String alias : command.getAliases()) {
            aliases.put(alias.toLowerCase(), command.getCommandName().toLowerCase());
        }
    }

    public boolean executeCommand(CommandSender sender, String[] args) {
        // Nếu không có args, mở GUI storage
        if (args.length == 0) {
            if (sender instanceof Player) {
                try {
                    ((Player) sender).openInventory(new PersonalStorage((Player) sender).getInventory());
                } catch (IndexOutOfBoundsException e) {
                    sender.sendMessage(Chat.colorize(File.getMessage().getString("admin.not_enough_slot")));
                }
            }
            return true;
        }

        String commandName = args[0].toLowerCase();

        // Kiểm tra alias
        if (aliases.containsKey(commandName)) {
            commandName = aliases.get(commandName);
        }

        IStorageCommand command = commands.get(commandName);
        if (command != null) {
            if (command.hasPermission(sender)) {
                return command.execute(sender, args);
            } else {
                sender.sendMessage(Chat.colorize(File.getMessage().getString("admin.no_permission")));
                return true;
            }
        }

        return false;
    }

    public List<String> getTabComplete(CommandSender sender, String[] args) {
        List<String> completions = new ArrayList<>();

        if (args.length == 1) {
            // Tab completion cho command chính
            for (IStorageCommand command : commands.values()) {
                if (command.hasPermission(sender)) {
                    completions.add(command.getCommandName());
                    completions.addAll(command.getAliases());
                }
            }
            return filterCompletions(args[0], completions);
        } else if (args.length > 1) {
            // Tab completion cho sub-commands
            String commandName = args[0].toLowerCase();

            if (aliases.containsKey(commandName)) {
                commandName = aliases.get(commandName);
            }

            IStorageCommand command = commands.get(commandName);
            if (command != null && command.hasPermission(sender)) {
                return command.getTabComplete(sender, args);
            }
        }

        return completions;
    }

    private List<String> filterCompletions(String input, List<String> completions) {
        List<String> filtered = new ArrayList<>();
        String lowerInput = input.toLowerCase();

        for (String completion : completions) {
            if (completion.toLowerCase().startsWith(lowerInput)) {
                filtered.add(completion);
            }
        }

        return filtered;
    }
}
