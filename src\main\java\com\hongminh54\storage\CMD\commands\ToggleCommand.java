package com.hongminh54.storage.CMD.commands;

import com.hongminh54.storage.Manager.MineManager;
import com.hongminh54.storage.Manager.SoundManager;
import com.hongminh54.storage.Storage;
import com.hongminh54.storage.Utils.Chat;
import com.hongminh54.storage.Utils.File;
import org.bukkit.Bukkit;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

import java.util.ArrayList;
import java.util.List;

public class ToggleCommand implements IStorageCommand {

    @Override
    public boolean execute(CommandSender sender, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(Chat.colorize("&cLệnh này chỉ có thể được sử dụng bởi người chơi."));
            return true;
        }

        Player player = (Player) sender;

        // Lấy trạng thái hiện tại và đảo ngược
        boolean newToggleState = !MineManager.toggle.getOrDefault(player, true);
        MineManager.toggle.put(player, newToggleState);

        // G<PERSON>i thông báo cho người chơi
        String statusText = newToggleState ?
                File.getMessage().getString("status.status_on", "&a✓ Bật") :
                File.getMessage().getString("status.status_off", "&c✕ Tắt");
        String message = File.getMessage().getString("status.toggle", "&8[&b&l❖&8] &fĐã #status# &ftính năng tự động nhặt tài nguyên");
        if (message != null && statusText != null) {
            message = message.replace("#status#", statusText);
        } else {
            // Fallback message nếu không tìm thấy trong config
            message = newToggleState ?
                    "&a✓ Đã bật tính năng tự động nhặt tài nguyên" :
                    "&c✕ Đã tắt tính năng tự động nhặt tài nguyên";
        }
        player.sendMessage(Chat.colorize(message));

        // Phát âm thanh
        String soundConfig = newToggleState ?
                File.getConfig().getString("effects.toggle_on.sound", "ENTITY_EXPERIENCE_ORB_PICKUP:1.0:1.0") :
                File.getConfig().getString("effects.toggle_off.sound", "BLOCK_LEVER_CLICK:1.0:1.0");
        SoundManager.playSoundFromConfig(player, soundConfig);

        // Lưu dữ liệu bất đồng bộ
        Bukkit.getScheduler().runTaskAsynchronously(Storage.getStorage(), () -> {
            try {
                MineManager.savePlayerData(player);
            } catch (Exception e) {
                Storage.getStorage().getLogger().warning("Lỗi khi lưu dữ liệu toggle cho " + player.getName() + ": " + e.getMessage());
                e.printStackTrace();
            }
        });

        return true;
    }

    @Override
    public List<String> getTabComplete(CommandSender sender, String[] args) {
        return new ArrayList<>();
    }

    @Override
    public String getCommandName() {
        return "toggle";
    }

    @Override
    public List<String> getAliases() {
        return new ArrayList<>();
    }

    @Override
    public boolean hasPermission(CommandSender sender) {
        return sender.hasPermission("storage.toggle");
    }
}
