package com.hongminh54.storage.GUI;

import com.cryptomorin.xseries.XEnchantment;
import com.cryptomorin.xseries.XMaterial;
import com.hongminh54.storage.GUI.manager.IGUI;
import com.hongminh54.storage.GUI.manager.InteractiveItem;
import com.hongminh54.storage.Manager.SoundManager;
import com.hongminh54.storage.Manager.StatsManager;
import com.hongminh54.storage.Storage;
import com.hongminh54.storage.Utils.File;
import com.hongminh54.storage.Utils.GUIText;
import com.hongminh54.storage.Utils.Number;
import com.hongminh54.storage.compatibility.AdvancedCompatibility;
import com.hongminh54.storage.compatibility.MaterialCompatibility;
import org.bukkit.Material;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemFlag;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.scheduler.BukkitRunnable;
import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class StatsGUI implements IGUI {
    private final Player p;
    private final FileConfiguration fileConfig;
    private Inventory inventory;

    public StatsGUI(Player p) {
        this.p = p;
        this.fileConfig = File.getGUIConfig("stats");

        // Validate config
        if (!validateConfig()) {
            Storage.getStorage().getLogger().warning("Config stats.yml có vấn đề cho StatsGUI");
        }

        createInventory();

        // Phát âm thanh mở GUI
        playOpenSound();
    }

    private void createInventory() {
        String title = GUIText.format(Objects.requireNonNull(fileConfig.getString("title")));
        int size = fileConfig.getInt("size") * 9;
        inventory = GUI.createInventory(p, size, title);

        // Thêm item trang trí trước
        addDecorativeItems();

        // Sau đó thêm các item chức năng
        fillItems();
    }

    /**
     * Thêm các item trang trí vào GUI
     */
    private void addDecorativeItems() {
        ConfigurationSection decoratesSection = fileConfig.getConfigurationSection("items.decorates");
        if (decoratesSection == null) return;

        // Lấy danh sách slot từ config
        String slotsString = decoratesSection.getString("slot", "");
        if (slotsString.isEmpty()) return;

        String[] slots = slotsString.split(",");

        // Tạo item trang trí với hỗ trợ đa phiên bản
        ItemStack decorateItem = createDecorativeItem(decoratesSection);

        // Đặt item vào các slot và đăng ký với InteractiveItem để không thể lấy ra
        for (String slotStr : slots) {
            try {
                int slot = Integer.parseInt(slotStr.trim());
                if (slot >= 0 && slot < inventory.getSize()) {
                    // Tạo bản sao để tránh xung đột
                    ItemStack safeDecorateItem = decorateItem.clone();

                    // Tạo InteractiveItem cho item trang trí (không có action)
                    InteractiveItem decorativeInteractiveItem = new InteractiveItem(
                            safeDecorateItem, slot, false, new ArrayList<>(), "decorates");

                    // Đặt item vào inventory
                    inventory.setItem(slot, decorativeInteractiveItem.getItem());

                    // Đăng ký với GUI manager để không thể lấy ra
                    GUI.getInteractiveItems().put(slot, decorativeInteractiveItem);
                }
            } catch (NumberFormatException e) {
                // Bỏ qua slot không hợp lệ
            }
        }
    }

    /**
     * Tạo item trang trí với hỗ trợ đa phiên bản
     *
     * @param decoratesSection ConfigurationSection chứa thông tin item trang trí
     * @return ItemStack đã được tạo
     */
    private ItemStack createDecorativeItem(ConfigurationSection decoratesSection) {
        // Lấy material với hỗ trợ đa phiên bản
        String materialName = decoratesSection.getString("material", "BLACK_STAINED_GLASS_PANE");
        ItemStack itemStack = createCompatibleItemStack(materialName);

        // Thiết lập số lượng
        itemStack.setAmount(decoratesSection.getInt("amount", 1));

        ItemMeta meta = itemStack.getItemMeta();
        if (meta != null) {
            // Thiết lập tên
            String name = decoratesSection.getString("name", "&7 ");
            meta.setDisplayName(GUIText.format(name));

            // Thiết lập lore
            List<String> lore = new ArrayList<>();
            for (String loreLine : decoratesSection.getStringList("lore")) {
                lore.add(GUIText.format(loreLine));
            }
            meta.setLore(lore);

            // Thêm custom model data nếu được cấu hình và phiên bản hỗ trợ
            if (decoratesSection.contains("custom-model-data")) {
                int modelData = decoratesSection.getInt("custom-model-data", -1);
                if (modelData > 0) {
                    // Sử dụng AdvancedCompatibility để set custom model data an toàn
                    AdvancedCompatibility.setCustomModelData(meta, modelData);
                }
            }

            // Thiết lập unbreakable nếu không phải 1.12.2
            if (!MaterialCompatibility.isPre113()) {
                meta.setUnbreakable(decoratesSection.getBoolean("unbreakable", false));
            }

            // Xử lý flags
            if (decoratesSection.contains("flags")) {
                ConfigurationSection flagsSection = decoratesSection.getConfigurationSection("flags");
                if (flagsSection != null) {
                    for (String flag_name : flagsSection.getKeys(false)) {
                        boolean apply = flagsSection.getBoolean(flag_name);
                        if (flag_name.equalsIgnoreCase("ALL") && apply) {
                            try {
                                // Sử dụng tương thích đa phiên bản cho ItemFlag
                                ItemFlag potionFlag = com.hongminh54.storage.compatibility.MaterialCompatibility.getCompatibleItemFlag("HIDE_ADDITIONAL_TOOLTIP", "HIDE_POTION_EFFECTS");
                                meta.addItemFlags(ItemFlag.HIDE_ATTRIBUTES,
                                        ItemFlag.HIDE_DESTROYS, ItemFlag.HIDE_ENCHANTS,
                                        ItemFlag.HIDE_PLACED_ON, potionFlag);

                                // HIDE_UNBREAKABLE chỉ có trong 1.13+
                                if (!MaterialCompatibility.isPre113()) {
                                    meta.addItemFlags(ItemFlag.HIDE_UNBREAKABLE);
                                }
                            } catch (Exception e) {
                                // Bỏ qua lỗi với phiên bản cũ
                            }
                            break;
                        } else if (apply) {
                            try {
                                meta.addItemFlags(ItemFlag.valueOf(flag_name));
                            } catch (IllegalArgumentException ignored) {
                                // Bỏ qua flag không hợp lệ
                            }
                        }
                    }
                }
            }

            itemStack.setItemMeta(meta);
        }

        // Thêm enchantment nếu được cấu hình
        if (decoratesSection.getBoolean("enchanted", false) ||
                decoratesSection.contains("enchants")) {

            if (decoratesSection.contains("enchants")) {
                ConfigurationSection enchantsSection = decoratesSection.getConfigurationSection("enchants");
                if (enchantsSection != null) {
                    for (String enchantName : enchantsSection.getKeys(false)) {
                        int level = enchantsSection.getInt(enchantName);
                        try {
                            XEnchantment xEnchant = XEnchantment.matchXEnchantment(enchantName).orElse(null);
                            if (xEnchant != null && xEnchant.getEnchant() != null) {
                                itemStack.addUnsafeEnchantment(xEnchant.getEnchant(), level);
                            }
                        } catch (Exception e) {
                            // Bỏ qua enchantment không hợp lệ
                        }
                    }
                }
            }
        }

        return itemStack;
    }

    /**
     * Tạo ItemStack tương thích với phiên bản hiện tại
     *
     * @param materialName Tên material
     * @return ItemStack tương thích
     */
    private ItemStack createCompatibleItemStack(String materialName) {
        if (materialName == null || materialName.isEmpty()) {
            materialName = "BLACK_STAINED_GLASS_PANE";
        }

        // Sử dụng function mới từ MaterialCompatibility
        return MaterialCompatibility.createCompatibleItemStack(materialName);
    }

    private void fillItems() {
        ConfigurationSection items = fileConfig.getConfigurationSection("items");
        if (items == null) return;

        for (String key : items.getKeys(false)) {
            // Bỏ qua item trang trí vì đã được xử lý riêng
            if (key.equals("decorates")) continue;

            ConfigurationSection item = items.getConfigurationSection(key);
            if (item == null) continue;

            // Sử dụng MaterialCompatibility để tương thích với 1.12.2
            String materialName = item.getString("material");
            ItemStack itemStack;

            // Kiểm tra nếu chuỗi chứa dấu hai chấm (định dạng legacy cho material và data)
            if (materialName != null && materialName.contains(":")) {
                String[] parts = materialName.split(":");
                String material = parts[0];
                byte data = Byte.parseByte(parts[1]);

                // Sử dụng function mới từ MaterialCompatibility
                itemStack = createCompatibleItemStack(materialName);
                itemStack.setAmount(item.getInt("amount", 1));
            } else {
                // Trường hợp thông thường không có data
                try {
                    XMaterial xMat = XMaterial.matchXMaterial(materialName).orElse(XMaterial.STONE);
                    itemStack = xMat.parseItem();
                    if (itemStack != null) {
                        itemStack.setAmount(item.getInt("amount", 1));
                    } else {
                        itemStack = new ItemStack(Material.STONE, item.getInt("amount", 1));
                    }
                } catch (Exception e) {
                    itemStack = new ItemStack(Material.STONE, item.getInt("amount", 1));
                }
            }

            int slot = item.getInt("slot");

            // Sử dụng GUIText để định dạng tên và mô tả
            String name = GUIText.format(item.getString("name", ""));
            List<String> lore = new ArrayList<>();

            for (String loreLine : item.getStringList("lore")) {
                // Thay thế placeholder với giá trị thống kê đã được định dạng
                loreLine = loreLine.replace("#total_mined#",
                                Number.formatCompact(StatsManager.getTotalMined(p)))
                        .replace("#total_deposited#",
                                Number.formatCompact(StatsManager.getTotalDeposited(p)))
                        .replace("#total_withdrawn#",
                                Number.formatCompact(StatsManager.getTotalWithdrawn(p)))
                        .replace("#total_sold#",
                                Number.formatCompact(StatsManager.getTotalSold(p)));

                lore.add(GUIText.format(loreLine));
            }

            boolean enchanted = item.getBoolean("enchanted", false);
            List<String> actions = item.getStringList("action");

            ItemMeta meta = itemStack.getItemMeta();
            if (meta != null) {
                meta.setDisplayName(name);
                meta.setLore(lore);

                // Thêm custom model data nếu được cấu hình và phiên bản hỗ trợ
                if (item.contains("custom-model-data")) {
                    int modelData = item.getInt("custom-model-data", -1);
                    if (modelData > 0) {
                        // Sử dụng AdvancedCompatibility để set custom model data an toàn
                        AdvancedCompatibility.setCustomModelData(meta, modelData);
                    }
                }

                // Xử lý flags nếu có
                if (item.contains("flags")) {
                    ConfigurationSection flagsSection = item.getConfigurationSection("flags");
                    if (flagsSection != null) {
                        for (String flag_name : flagsSection.getKeys(false)) {
                            boolean apply = flagsSection.getBoolean(flag_name);
                            if (flag_name.equalsIgnoreCase("ALL") && apply) {
                                try {
                                    // Sử dụng tương thích đa phiên bản cho ItemFlag
                                    ItemFlag potionFlag = com.hongminh54.storage.compatibility.MaterialCompatibility.getCompatibleItemFlag("HIDE_ADDITIONAL_TOOLTIP", "HIDE_POTION_EFFECTS");
                                    meta.addItemFlags(ItemFlag.HIDE_ATTRIBUTES,
                                            ItemFlag.HIDE_DESTROYS, ItemFlag.HIDE_ENCHANTS,
                                            ItemFlag.HIDE_PLACED_ON, potionFlag);

                                    // HIDE_UNBREAKABLE chỉ có trong 1.13+
                                    if (!MaterialCompatibility.isPre113()) {
                                        meta.addItemFlags(ItemFlag.HIDE_UNBREAKABLE);
                                    }
                                } catch (Exception e) {
                                    // Bỏ qua lỗi với phiên bản cũ
                                }
                                break;
                            } else if (apply) {
                                try {
                                    meta.addItemFlags(ItemFlag.valueOf(flag_name));
                                } catch (IllegalArgumentException ignored) {
                                    // Bỏ qua flag không hợp lệ
                                }
                            }
                        }
                    }
                }

                itemStack.setItemMeta(meta);
            }

            InteractiveItem interactiveItem = new InteractiveItem(itemStack, slot, enchanted, actions, key);
            if (enchanted) {
                interactiveItem.enchant();
            }

            // Xử lý các hành động khi click vào item
            interactiveItem.onClick((player, clickType) -> {
                for (String action : actions) {
                    if (action.startsWith("[PLAYER_COMMAND]")) {
                        String command = action.substring("[PLAYER_COMMAND]".length()).trim();
                        player.closeInventory();
                        new BukkitRunnable() {
                            @Override
                            public void run() {
                                player.performCommand(command);
                            }
                        }.runTask(Storage.getStorage());
                    }
                }
            });

            inventory.setItem(slot, interactiveItem.getItem());
            GUI.getInteractiveItems().put(slot, interactiveItem);
        }
    }

    @Override
    public @NotNull Inventory getInventory() {
        return inventory;
    }

    /**
     * Validate config file
     *
     * @return true nếu config hợp lệ
     */
    private boolean validateConfig() {
        try {
            if (fileConfig == null) {
                return false;
            }

            // Kiểm tra các trường bắt buộc
            if (!fileConfig.contains("title") || !fileConfig.contains("size") || !fileConfig.contains("items")) {
                return false;
            }

            // Kiểm tra size hợp lệ
            int size = fileConfig.getInt("size", 6);
            return size >= 1 && size <= 6;
        } catch (Exception e) {
            handleError("Error validating config: " + e.getMessage(), false);
            return false;
        }
    }

    /**
     * Phát âm thanh mở GUI từ config
     */
    private void playOpenSound() {
        try {
            if (File.getConfig().getBoolean("effects.enabled", true)) {
                String openSound = fileConfig.getString("sounds.open", "BLOCK_CHEST_OPEN:0.5:1.0");
                SoundManager.playSoundFromConfig(p, openSound);
            }
        } catch (Exception e) {
            // Bỏ qua lỗi âm thanh
        }
    }

    /**
     * Phát âm thanh click từ config
     */
    private void playClickSound() {
        try {
            if (File.getConfig().getBoolean("effects.enabled", true)) {
                String clickSound = fileConfig.getString("sounds.click", "UI_BUTTON_CLICK:0.5:1.0");
                SoundManager.playSoundFromConfig(p, clickSound);
            }
        } catch (Exception e) {
            // Bỏ qua lỗi âm thanh
        }
    }

    /**
     * Xử lý lỗi và hiển thị thông báo
     *
     * @param error        Thông điệp lỗi
     * @param showToPlayer Có hiển thị cho người chơi không
     */
    private void handleError(String error, boolean showToPlayer) {
        com.hongminh54.storage.Storage.getStorage().getLogger().warning("StatsGUI Error: " + error);
        if (showToPlayer && p != null && p.isOnline()) {
            try {
                String failSound = fileConfig.getString("sounds.error", "ENTITY_VILLAGER_NO:0.5:1.0");
                SoundManager.playSoundFromConfig(p, failSound);
            } catch (Exception e) {
                // Bỏ qua lỗi âm thanh
            }
        }
    }

    /**
     * Lấy player instance
     *
     * @return Player
     */
    public Player getPlayer() {
        return p;
    }

    /**
     * Lấy config instance
     *
     * @return FileConfiguration
     */
    public FileConfiguration getConfig() {
        return fileConfig;
    }
}