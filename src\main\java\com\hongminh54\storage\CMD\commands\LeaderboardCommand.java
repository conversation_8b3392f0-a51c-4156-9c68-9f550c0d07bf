package com.hongminh54.storage.CMD.commands;

import com.hongminh54.storage.GUI.LeaderboardGUI;
import com.hongminh54.storage.Manager.LeaderboardManager;
import com.hongminh54.storage.Manager.StatsManager;
import com.hongminh54.storage.Storage;
import com.hongminh54.storage.Utils.Chat;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.bukkit.util.StringUtil;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class LeaderboardCommand implements IStorageCommand {

    // Biến để theo dõi thời gian sử dụng lệnh
    private static final Map<String, Long> playerCooldowns = new ConcurrentHashMap<>();

    @Override
    public boolean execute(CommandSender sender, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(Chat.colorize("&c<PERSON><PERSON><PERSON> này chỉ có thể được sử dụng bởi người chơi."));
            return true;
        }

        Player player = (Player) sender;

        try {
            // Kiểm tra thời gian giữa các lần sử dụng bảng xếp hạng
            String playerKey = "leaderboard_cooldown_" + player.getName();
            Long lastUse = playerCooldowns.get(playerKey);
            long currentTime = System.currentTimeMillis();

            if (lastUse != null && (currentTime - lastUse) < 2000) { // 2 giây cooldown
                player.sendMessage(Chat.colorizewp("&eVui lòng đợi giây lát trước khi mở lại bảng xếp hạng."));
                return true;
            }

            // Cập nhật thời gian sử dụng lệnh
            playerCooldowns.put(playerKey, currentTime);

            // Xử lý các tham số đặc biệt
            if (args.length > 1) {
                // Chế độ làm mới bảng xếp hạng bắt buộc
                if (args[1].equalsIgnoreCase("refresh") && player.hasPermission("storage.leaderboard.refresh")) {
                    player.sendMessage(Chat.colorizewp("&e&lĐang làm mới bảng xếp hạng..."));
                    // Xóa cache và cập nhật lại toàn bộ bảng xếp hạng
                    LeaderboardManager.clearCache();
                    LeaderboardManager.updateAllLeaderboards();
                    player.sendMessage(Chat.colorizewp("&a&lĐã làm mới bảng xếp hạng thành công!"));

                    // Sau đó mở bảng xếp hạng với dữ liệu mới
                    LeaderboardGUI gui = new LeaderboardGUI(player);
                    player.openInventory(gui.getInventory());
                    return true;
                }

                // Chế độ debug cho admin - CẢI THIỆN VỚI FOCUS VÀO MINED
                if (args[1].equalsIgnoreCase("debug") && player.hasPermission("storage.admin")) {
                    // Kiểm tra các loại bảng xếp hạng với focus vào mined
                    player.sendMessage(Chat.colorizewp("&e&lDebug bảng xếp hạng - Xem console để biết chi tiết"));
                    player.sendMessage(Chat.colorizewp("&c&lFocus: Debugging MINED leaderboard issues"));

                    // Clear cache trước khi debug
                    LeaderboardManager.clearCache();

                    // Debug mined trước
                    LeaderboardManager.debugLeaderboard(LeaderboardManager.TYPE_MINED);

                    // Debug các loại khác
                    LeaderboardManager.debugLeaderboard(LeaderboardManager.TYPE_DEPOSITED);
                    LeaderboardManager.debugLeaderboard(LeaderboardManager.TYPE_WITHDRAWN);
                    LeaderboardManager.debugLeaderboard(LeaderboardManager.TYPE_SOLD);

                    // Debug stats của chính player
                    player.sendMessage(Chat.colorizewp("&a&lYour stats:"));
                    List<String> playerStats = StatsManager.getStatsInfo(player);
                    for (String stat : playerStats) {
                        player.sendMessage(Chat.colorizewp(stat));
                    }

                    return true;
                }

                // Chọn loại bảng xếp hạng cụ thể
                if (args[1].equalsIgnoreCase("mined") ||
                        args[1].equalsIgnoreCase("deposited") ||
                        args[1].equalsIgnoreCase("withdrawn") ||
                        args[1].equalsIgnoreCase("sold")) {

                    String type;
                    switch (args[1].toLowerCase()) {
                        case "mined":
                            type = LeaderboardManager.TYPE_MINED;
                            break;
                        case "deposited":
                            type = LeaderboardManager.TYPE_DEPOSITED;
                            break;
                        case "withdrawn":
                            type = LeaderboardManager.TYPE_WITHDRAWN;
                            break;
                        case "sold":
                            type = LeaderboardManager.TYPE_SOLD;
                            break;
                        default:
                            type = LeaderboardManager.TYPE_MINED;
                    }

                    // Kiểm tra bảng xếp hạng chỉ định có dữ liệu
                    if (LeaderboardManager.getCachedLeaderboard(type, 1).isEmpty()) {
                        player.sendMessage(Chat.colorizewp("&eBảng xếp hạng trống hoặc chưa được tạo. Đang tạo mới..."));
                        LeaderboardManager.requestUpdate(type, true);
                    }

                    LeaderboardGUI gui = new LeaderboardGUI(player, type);
                    player.openInventory(gui.getInventory());
                    return true;
                }
            }

            // Mở GUI bảng xếp hạng với loại mặc định
            LeaderboardGUI gui = new LeaderboardGUI(player);
            player.openInventory(gui.getInventory());

        } catch (Exception e) {
            Storage.getStorage().getLogger().severe("Lỗi khi mở bảng xếp hạng: " + e.getMessage());
            e.printStackTrace();
            player.sendMessage(Chat.colorizewp("&cKhông thể mở bảng xếp hạng. Lỗi: " + e.getMessage()));
            player.sendMessage(Chat.colorizewp("&eBạn có thể thử: &f/kho leaderboard refresh &eđể khắc phục lỗi"));
        }

        return true;
    }

    @Override
    public List<String> getTabComplete(CommandSender sender, String[] args) {
        if (args.length == 2) {
            List<String> leaderboardOptions = new ArrayList<>();
            // Các loại bảng xếp hạng
            leaderboardOptions.add("mined");
            leaderboardOptions.add("deposited");
            leaderboardOptions.add("withdrawn");
            leaderboardOptions.add("sold");

            // Tùy chọn làm mới và debug
            if (sender.hasPermission("storage.leaderboard.refresh")) {
                leaderboardOptions.add("refresh");
            }
            if (sender.hasPermission("storage.admin")) {
                leaderboardOptions.add("debug");
            }

            List<String> completions = new ArrayList<>();
            StringUtil.copyPartialMatches(args[1], leaderboardOptions, completions);
            return completions;
        }
        return new ArrayList<>();
    }

    @Override
    public String getCommandName() {
        return "leaderboard";
    }

    @Override
    public List<String> getAliases() {
        return Arrays.asList("bangxephang", "bxh", "top");
    }

    @Override
    public boolean hasPermission(CommandSender sender) {
        return sender.hasPermission("storage.leaderboard");
    }
}
