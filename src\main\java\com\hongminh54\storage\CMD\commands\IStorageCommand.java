package com.hongminh54.storage.CMD.commands;

import org.bukkit.command.CommandSender;

import java.util.List;

public interface IStorageCommand {

    /**
     * Thực thi command
     *
     * @param sender Ng<PERSON><PERSON><PERSON> gửi command
     * @param args   <PERSON><PERSON><PERSON> tham số command
     * @return true nếu command đư<PERSON>c x<PERSON> lý thành công
     */
    boolean execute(CommandSender sender, String[] args);

    /**
     * L<PERSON>y danh sách tab completion
     *
     * @param sender Ngư<PERSON>i gửi command
     * @param args   Các tham số command
     * @return Danh sách tab completion
     */
    List<String> getTabComplete(CommandSender sender, String[] args);

    /**
     * L<PERSON>y tên command
     *
     * @return Tên command
     */
    String getCommandName();

    /**
     * <PERSON><PERSON><PERSON> c<PERSON><PERSON> alias của command
     *
     * @return Danh sách alias
     */
    List<String> getAliases();

    /**
     * Kiểm tra quyền sử dụng command
     *
     * @param sender Ngư<PERSON>i gửi command
     * @return true nếu có quyền
     */
    boolean hasPermission(CommandSender sender);
}
