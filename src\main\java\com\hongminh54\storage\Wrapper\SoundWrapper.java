package com.hongminh54.storage.Wrapper;

import com.cryptomorin.xseries.XSound;
import com.hongminh54.storage.NMS.NMSAssistant;
import com.hongminh54.storage.Storage;
import org.bukkit.entity.Player;

import java.util.HashMap;
import java.util.Map;

public class SoundWrapper {

    private static final NMSAssistant nmsAssistant = new NMSAssistant();
    private static final boolean IS_PRE_113 = nmsAssistant.isVersionLessThan(13);

    // Cache XSound instances để tối ưu performance
    private static final Map<String, XSound> xSoundCache = new HashMap<>();

    // Cache sound mapping để tối ưu performance (fallback)
    private static final Map<String, String> soundMappingCache = new HashMap<>();

    // Sound mapping cho 1.12.2 -> 1.13+
    private static final Map<String, String> LEGACY_TO_MODERN = new HashMap<>();
    private static final Map<String, String> MODERN_TO_LEGACY = new HashMap<>();

    static {
        initializeSoundMappings();
    }

    /**
     * Khởi tạo sound mappings
     */
    private static void initializeSoundMappings() {
        // UI Sounds
        addMapping("CLICK", "UI_BUTTON_CLICK");
        addMapping("WOOD_CLICK", "UI_BUTTON_CLICK");
        addMapping("STONE_BUTTON_CLICK_ON", "UI_BUTTON_CLICK");

        // Chest Sounds
        addMapping("CHEST_OPEN", "BLOCK_CHEST_OPEN");
        addMapping("CHEST_CLOSE", "BLOCK_CHEST_CLOSE");

        // Note Sounds
        addMapping("NOTE_PLING", "BLOCK_NOTE_BLOCK_PLING");
        addMapping("NOTE_BASS", "BLOCK_NOTE_BLOCK_BASS");

        // Entity Sounds
        addMapping("ORB_PICKUP", "ENTITY_EXPERIENCE_ORB_PICKUP");
        addMapping("ITEM_PICKUP", "ENTITY_ITEM_PICKUP");
        addMapping("ITEM_BREAK", "ENTITY_ITEM_BREAK");
        addMapping("VILLAGER_NO", "ENTITY_VILLAGER_NO");
        addMapping("VILLAGER_YES", "ENTITY_VILLAGER_YES");
        addMapping("LEVEL_UP", "ENTITY_PLAYER_LEVELUP");
        addMapping("PLAYER_LEVELUP", "ENTITY_PLAYER_LEVELUP");

        // Block Sounds
        addMapping("ANVIL_USE", "BLOCK_ANVIL_USE");
        addMapping("ANVIL_BREAK", "BLOCK_ANVIL_BREAK");
        addMapping("ANVIL_LAND", "BLOCK_ANVIL_LAND");

        // Generic Sounds
        addMapping("HURT_FLESH", "ENTITY_GENERIC_HURT");
        addMapping("EXPLODE", "ENTITY_GENERIC_EXPLODE");
        addMapping("SPLASH", "ENTITY_GENERIC_SPLASH");
        addMapping("EAT", "ENTITY_GENERIC_EAT");
        addMapping("DRINK", "ENTITY_GENERIC_DRINK");

        // Teleport & Magic
        addMapping("ENDERMAN_TELEPORT", "ENTITY_ENDERMAN_TELEPORT");
        addMapping("FIREWORK_BLAST", "ENTITY_FIREWORK_ROCKET_BLAST");
        addMapping("FIREWORK_LAUNCH", "ENTITY_FIREWORK_ROCKET_LAUNCH");
    }

    /**
     * Thêm mapping giữa legacy và modern sound
     */
    private static void addMapping(String legacy, String modern) {
        LEGACY_TO_MODERN.put(legacy, modern);
        MODERN_TO_LEGACY.put(modern, legacy);
    }

    /**
     * Phát sound an toàn cho tất cả phiên bản sử dụng XSound
     */
    public static boolean playSound(Player player, String soundName, float volume, float pitch) {
        if (player == null || soundName == null || soundName.isEmpty()) {
            return false;
        }

        // Đảm bảo volume và pitch trong giới hạn hợp lý
        volume = Math.max(0.0f, Math.min(2.0f, volume));
        pitch = Math.max(0.5f, Math.min(2.0f, pitch));

        if (Storage.getStorage().isDebug()) {
            Storage.getStorage().getLogger().info("§e[SoundWrapper] Playing sound: " + soundName);
        }

        // Ưu tiên sử dụng XSound cho cross-version compatibility
        if (playSoundWithXSound(player, soundName, volume, pitch)) {
            return true;
        }

        // Fallback với legacy method
        String compatibleSound = getCompatibleSoundName(soundName);
        return playSoundDirect(player, compatibleSound, volume, pitch);
    }

    /**
     * Phát sound sử dụng XSound (method chính cho cross-version compatibility)
     */
    private static boolean playSoundWithXSound(Player player, String soundName, float volume, float pitch) {
        try {
            // Thử lấy XSound từ cache trước
            XSound xSound = xSoundCache.get(soundName.toUpperCase());

            if (xSound == null) {
                // Thử parse XSound từ tên sử dụng matchXSound
                xSound = XSound.matchXSound(soundName.toUpperCase()).orElse(null);
                if (xSound == null) {
                    // Thử với các tên sound tương thích
                    xSound = findCompatibleXSound(soundName);
                }
                if (xSound != null) {
                    xSoundCache.put(soundName.toUpperCase(), xSound);
                }
            }

            if (xSound != null) {
                xSound.play(player, volume, pitch);
                if (Storage.getStorage().isDebug()) {
                    Storage.getStorage().getLogger().info("§a[SoundWrapper] XSound success: " + soundName + " -> " + xSound.name());
                }
                return true;
            }

        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("§c[SoundWrapper] XSound failed: " + soundName + " - " + e.getMessage());
            }
        }

        return false;
    }

    /**
     * Tìm XSound tương thích cho sound name
     */
    private static XSound findCompatibleXSound(String soundName) {
        String upperName = soundName.toUpperCase();

        // Mapping cho các sound phổ biến
        switch (upperName) {
            case "CLICK":
            case "WOOD_CLICK":
            case "STONE_BUTTON_CLICK_ON":
                return XSound.UI_BUTTON_CLICK;
            case "CHEST_OPEN":
                return XSound.BLOCK_CHEST_OPEN;
            case "CHEST_CLOSE":
                return XSound.BLOCK_CHEST_CLOSE;
            case "NOTE_PLING":
                return XSound.BLOCK_NOTE_BLOCK_PLING;
            case "ORB_PICKUP":
                return XSound.ENTITY_EXPERIENCE_ORB_PICKUP;
            case "ITEM_PICKUP":
                return XSound.ENTITY_ITEM_PICKUP;
            case "VILLAGER_NO":
                return XSound.ENTITY_VILLAGER_NO;
            case "VILLAGER_YES":
                return XSound.ENTITY_VILLAGER_YES;
            case "LEVEL_UP":
            case "PLAYER_LEVELUP":
                return XSound.ENTITY_PLAYER_LEVELUP;
            case "ANVIL_USE":
                return XSound.BLOCK_ANVIL_USE;
            default:
                return null;
        }
    }

    /**
     * Lấy tên sound tương thích với phiên bản hiện tại
     */
    public static String getCompatibleSoundName(String soundName) {
        if (soundName == null || soundName.isEmpty()) {
            return getDefaultSoundName();
        }

        String upperName = soundName.toUpperCase();

        // Kiểm tra cache trước
        String cached = soundMappingCache.get(upperName);
        if (cached != null) {
            return cached;
        }

        String result;
        if (IS_PRE_113) {
            // Chuyển từ modern sang legacy cho 1.12.2
            result = MODERN_TO_LEGACY.getOrDefault(upperName, upperName);
        } else {
            // Chuyển từ legacy sang modern cho 1.13+
            result = LEGACY_TO_MODERN.getOrDefault(upperName, upperName);
        }

        // Lưu vào cache
        soundMappingCache.put(upperName, result);
        return result;
    }

    /**
     * Phát sound trực tiếp với fallback
     */
    private static boolean playSoundDirect(Player player, String soundName, float volume, float pitch) {
        // Thử phát sound chính
        try {
            player.playSound(player.getLocation(), soundName, volume, pitch);
            return true;
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("§c[SoundWrapper] Primary sound failed: " + soundName + " - " + e.getMessage());
            }
        }

        // Thử với fallback sounds
        String[] fallbackSounds = getFallbackSounds();
        for (String fallback : fallbackSounds) {
            try {
                player.playSound(player.getLocation(), fallback, volume, pitch);
                if (Storage.getStorage().isDebug()) {
                    Storage.getStorage().getLogger().info("§a[SoundWrapper] Used fallback sound: " + fallback);
                }
                return true;
            } catch (Exception ignored) {
                // Thử sound tiếp theo
            }
        }

        return false;
    }

    /**
     * Lấy danh sách fallback sounds theo phiên bản
     */
    private static String[] getFallbackSounds() {
        if (IS_PRE_113) {
            return new String[]{"CLICK", "NOTE_PLING", "CHEST_OPEN", "ORB_PICKUP"};
        } else {
            return new String[]{"UI_BUTTON_CLICK", "BLOCK_NOTE_BLOCK_PLING", "BLOCK_CHEST_OPEN", "ENTITY_EXPERIENCE_ORB_PICKUP"};
        }
    }

    /**
     * Lấy tên sound mặc định theo phiên bản
     */
    public static String getDefaultSoundName() {
        return IS_PRE_113 ? "CLICK" : "UI_BUTTON_CLICK";
    }

    /**
     * Lấy XSound mặc định an toàn cho tất cả phiên bản
     */
    public static XSound getDefaultXSound() {
        return XSound.UI_BUTTON_CLICK; // XSound tự động handle cross-version
    }

    /**
     * Phát sound mặc định an toàn
     */
    public static boolean playDefaultSound(Player player, float volume, float pitch) {
        try {
            getDefaultXSound().play(player, volume, pitch);
            return true;
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("§c[SoundWrapper] Default XSound failed: " + e.getMessage());
            }
            // Fallback với string-based method
            return playSoundDirect(player, getDefaultSoundName(), volume, pitch);
        }
    }

    /**
     * Phát sound từ config string
     */
    public static boolean playSoundFromConfig(Player player, String configString) {
        if (player == null || configString == null || configString.isEmpty()) {
            return false;
        }

        try {
            String[] parts = configString.split(":");
            String soundName = parts[0].trim();
            float volume = parts.length > 1 ? Float.parseFloat(parts[1].trim()) : 1.0f;
            float pitch = parts.length > 2 ? Float.parseFloat(parts[2].trim()) : 1.0f;

            return playSound(player, soundName, volume, pitch);
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("§c[SoundWrapper] Failed to parse config: " + configString + " - " + e.getMessage());
            }
            return playSound(player, getDefaultSoundName(), 0.5f, 1.0f);
        }
    }

}
