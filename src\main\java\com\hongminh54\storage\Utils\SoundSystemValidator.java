package com.hongminh54.storage.Utils;

import com.hongminh54.storage.Manager.SoundManager;
import com.hongminh54.storage.NMS.NMSAssistant;
import com.hongminh54.storage.Storage;
import com.hongminh54.storage.compatibility.SoundCompatibility;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.entity.Player;

/**
 * Utility class để validate và test sound system
 */
public class SoundSystemValidator {

    /**
     * Validate toàn bộ sound system
     *
     * @param player Player để test (có thể null)
     * @return true nếu sound system hoạt động tốt
     */
    public static boolean validateSoundSystem(Player player) {
        boolean isValid = true;

        try {
            // 1. Kiểm tra config sounds
            if (!validateConfigSounds()) {
                Storage.getStorage().getLogger().warning("§c[SoundValidator] Config sounds validation failed");
                isValid = false;
            }

            // 2. Kiểm tra sound compatibility
            if (!validateSoundCompatibility()) {
                Storage.getStorage().getLogger().warning("§c[SoundValidator] Sound compatibility validation failed");
                isValid = false;
            }

            // 3. Test sound playback (nếu có player)
            if (player != null && !testSoundPlayback(player)) {
                Storage.getStorage().getLogger().warning("§c[SoundValidator] Sound playback test failed");
                isValid = false;
            }

            if (isValid) {
                Storage.getStorage().getLogger().info("§a[SoundValidator] Sound system validation passed");
            }

        } catch (Exception e) {
            Storage.getStorage().getLogger().severe("§c[SoundValidator] Exception during validation: " + e.getMessage());
            isValid = false;
        }

        return isValid;
    }

    /**
     * Validate các sound configs trong config.yml
     */
    private static boolean validateConfigSounds() {
        try {
            FileConfiguration config = File.getConfig();

            if (!config.getBoolean("effects.enabled", true)) {
                Storage.getStorage().getLogger().info("§e[SoundValidator] Effects are disabled in config");
                return true; // Không phải lỗi nếu effects bị tắt
            }

            // Danh sách các effect sounds cần kiểm tra
            String[] requiredEffects = {"collect", "transfer_success", "transfer_fail", "search_success", "search_fail", "history_view"};

            boolean allValid = true;

            for (String effect : requiredEffects) {
                String soundConfig = config.getString("effects." + effect + ".sound");
                if (soundConfig == null || soundConfig.isEmpty()) {
                    Storage.getStorage().getLogger().warning("§c[SoundValidator] Missing sound config for: " + effect);
                    allValid = false;
                } else {
                    // Validate format
                    if (!isValidSoundConfig(soundConfig)) {
                        Storage.getStorage().getLogger().warning("§c[SoundValidator] Invalid sound config for " + effect + ": " + soundConfig);
                        allValid = false;
                    }
                }
            }

            return allValid;

        } catch (Exception e) {
            Storage.getStorage().getLogger().warning("§c[SoundValidator] Error validating config sounds: " + e.getMessage());
            return false;
        }
    }

    /**
     * Kiểm tra format của sound config
     */
    private static boolean isValidSoundConfig(String soundConfig) {
        try {
            String[] parts = soundConfig.split(":");

            // Phải có ít nhất tên sound
            if (parts.length < 1 || parts[0].trim().isEmpty()) {
                return false;
            }

            // Kiểm tra volume nếu có
            if (parts.length > 1) {
                float volume = Float.parseFloat(parts[1].trim());
                if (volume < 0.0f || volume > 2.0f) {
                    return false;
                }
            }

            // Kiểm tra pitch nếu có
            if (parts.length > 2) {
                float pitch = Float.parseFloat(parts[2].trim());
                return !(pitch < 0.5f) && !(pitch > 2.0f);
            }

            return true;

        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * Validate sound compatibility system
     */
    private static boolean validateSoundCompatibility() {
        try {
            // Test các sound mapping cơ bản
            String[][] testPairs = {{"UI_BUTTON_CLICK", "CLICK"}, {"BLOCK_CHEST_OPEN", "CHEST_OPEN"}, {"ENTITY_VILLAGER_NO", "VILLAGER_NO"}, {"BLOCK_NOTE_BLOCK_PLING", "NOTE_PLING"}};

            for (String[] pair : testPairs) {
                org.bukkit.Sound sound = SoundCompatibility.getCompatibleSound(pair[0], pair[1]);
                if (sound == null) {
                    Storage.getStorage().getLogger().warning("§c[SoundValidator] No compatible sound found for: " + pair[0] + "/" + pair[1]);
                    return false;
                }
            }

            // Test default sound
            org.bukkit.Sound defaultSound = SoundCompatibility.getDefaultSound();
            if (defaultSound == null) {
                Storage.getStorage().getLogger().warning("§c[SoundValidator] No default sound available");
                return false;
            }

            return true;

        } catch (Exception e) {
            Storage.getStorage().getLogger().warning("§c[SoundValidator] Error validating sound compatibility: " + e.getMessage());
            return false;
        }
    }

    /**
     * Test sound playback với player
     */
    private static boolean testSoundPlayback(Player player) {
        try {
            // Test basic sound
            SoundCompatibility.playClickSound(player);

            // Test config sound
            SoundManager.playSoundFromConfig(player, "UI_BUTTON_CLICK:0.1:1.0");

            // Test effect sound
            SoundManager.playEffectSound(player, "collect");

            return true;

        } catch (Exception e) {
            Storage.getStorage().getLogger().warning("§c[SoundValidator] Error testing sound playback: " + e.getMessage());
            return false;
        }
    }

    /**
     * Lấy thông tin chi tiết về sound system
     */
    public static String getSoundSystemInfo() {
        StringBuilder info = new StringBuilder();

        try {
            info.append("§e=== Sound System Information ===\n");

            // Version info
            NMSAssistant nms = new NMSAssistant();
            info.append("§fMinecraft Version: ").append(nms.getNMSVersion().toString()).append("\n");
            info.append("§fIs Pre-1.13: ").append(nms.isVersionLessThan(13)).append("\n");

            // Config info
            FileConfiguration config = File.getConfig();
            info.append("§fEffects Enabled: ").append(config.getBoolean("effects.enabled", true)).append("\n");
            info.append("§fDebug Mode: ").append(Storage.getStorage().isDebug()).append("\n");

            // Sound cache info
            info.append("§fSound Cache Size: ").append(SoundCompatibility.getCacheSize()).append("\n");

            // Available sounds count
            String[] allSounds = SoundCompatibility.getAllSounds();
            info.append("§fAvailable Sounds: ").append(allSounds.length).append("\n");

        } catch (Exception e) {
            info.append("§cError getting sound system info: ").append(e.getMessage());
        }

        return info.toString();
    }

    /**
     * Clear sound cache và reload
     */
    public static void resetSoundSystem() {
        try {
            SoundCompatibility.clearSoundCache();
            Storage.getStorage().getLogger().info("§a[SoundValidator] Sound system reset completed");
        } catch (Exception e) {
            Storage.getStorage().getLogger().warning("§c[SoundValidator] Error resetting sound system: " + e.getMessage());
        }
    }
}
