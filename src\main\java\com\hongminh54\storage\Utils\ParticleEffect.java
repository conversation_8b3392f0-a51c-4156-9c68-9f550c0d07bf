package com.hongminh54.storage.Utils;

import com.hongminh54.storage.NMS.NMSAssistant;
import org.bukkit.Location;
import org.bukkit.Particle;
import org.bukkit.entity.Player;

/**
 * Lớp quản lý hiệu ứng hạt cho Minecraft 1.12.2 - 1.21.x
 */
public class ParticleEffect {

    private static final boolean IS_PRE_113 = new NMSAssistant().isVersionLessThan(13);

    /**
     * Tính năng tạo hiệu ứng hạt tương thích cho cả phiên bản 1.12.2 - 1.21.x
     * sử dụng reflection để tránh lỗi biên dịch với các phiên bản cũ
     *
     * @param location     Vị trí hiển thị hiệu ứng
     * @param particleName Tên particle (dùng enum Particle từ 1.13+)
     * @param count        Số lượng hạt
     * @param offsetX      Độ lệch X
     * @param offsetY      Độ lệch Y
     * @param offsetZ      Độ lệch Z
     * @param speed        Tốc độ hạt
     */
    public static void spawnParticle(Location location, String particleName, int count, double offsetX, double offsetY, double offsetZ, double speed) {
        if (location == null || location.getWorld() == null || particleName == null) {
            return;
        }

        try {
            if (IS_PRE_113) {
                // Phiên bản 1.12.2 sử dụng tên particle khác biệt
                String legacyName = convertParticleName(particleName);

                // Sử dụng reflection để tránh lỗi biên dịch với enum Effect
                try {
                    // Tìm enum Effect và gọi phương thức play
                    Class<?> effectClass = Class.forName("org.bukkit.Effect");
                    Object effect = null;

                    for (Object enumConstant : effectClass.getEnumConstants()) {
                        if (enumConstant.toString().equals(legacyName)) {
                            effect = enumConstant;
                            break;
                        }
                    }

                    if (effect != null) {
                        // Gọi world.playEffect()
                        java.lang.reflect.Method playEffect = location.getWorld().getClass().getMethod("playEffect",
                                Location.class, effectClass, int.class, int.class);
                        playEffect.invoke(location.getWorld(), location, effect, count, 0);
                    } else {
                        // Fallback cho các phiên bản cũ
                        location.getWorld().spawnParticle(
                                org.bukkit.Particle.valueOf("FLAME"),
                                location,
                                count,
                                offsetX,
                                offsetY,
                                offsetZ,
                                speed);
                    }
                } catch (Exception e) {
                    // Bỏ qua lỗi do enum không khớp
                }
            } else {
                // Phiên bản 1.13+ sử dụng enum Particle
                try {
                    Particle particle = Particle.valueOf(particleName);
                    location.getWorld().spawnParticle(
                            particle,
                            location,
                            count,
                            offsetX,
                            offsetY,
                            offsetZ,
                            speed);
                } catch (IllegalArgumentException e) {
                    // Fallback nếu không tìm thấy particle
                    location.getWorld().spawnParticle(
                            Particle.FLAME,
                            location,
                            count,
                            offsetX,
                            offsetY,
                            offsetZ,
                            speed);
                }
            }
        } catch (Exception e) {
            // Bỏ qua lỗi, không gây crash plugin
            System.out.println("Lỗi khi tạo hiệu ứng hạt " + particleName + ": " + e.getMessage());
        }
    }

    /**
     * Chuyển đổi tên particle từ phiên bản mới sang phiên bản cũ (1.12.2)
     *
     * @param newName Tên particle từ phiên bản 1.13+
     * @return Tên particle tương thích với 1.12.2
     */
    private static String convertParticleName(String newName) {
        if (newName == null) {
            return "FLAME"; // Mặc định
        }

        // Chuyển đổi tên particle từ phiên bản mới sang cũ
        switch (newName.toUpperCase()) {
            case "HEART":
                return "HEART";
            case "FLAME":
                return "FLAME";
            case "SMOKE":
                return "SMOKE";
            case "FIREWORKS_SPARK":
                return "FIREWORKS_SPARK";
            case "CRIT":
                return "CRIT";
            case "SPELL":
                return "SPELL";
            case "NOTE":
                return "NOTE";
            case "PORTAL":
                return "PORTAL";
            case "ENCHANTMENT_TABLE":
                return "ENCHANTMENT_TABLE";
            case "VILLAGER_HAPPY":
                return "VILLAGER_HAPPY";
            case "VILLAGER_ANGRY":
                return "VILLAGER_ANGRY";
            case "REDSTONE":
                return "REDSTONE";
            case "EXPLOSION_NORMAL":
            case "EXPLOSION":
                return "EXPLOSION_NORMAL";
            case "EXPLOSION_LARGE":
                return "EXPLOSION_LARGE";
            case "LAVA":
                return "LAVA";
            case "CLOUD":
                return "CLOUD";
            case "SNOWBALL":
                return "SNOWBALL";
            case "WATER_DROP":
                return "WATER_DROP";
            case "SLIME":
                return "SLIME";
            default:
                return "FLAME"; // Mặc định nếu không tìm thấy
        }
    }

    /**
     * Hiển thị hiệu ứng hạt cho một người chơi cụ thể
     *
     * @param player       Người chơi sẽ thấy hiệu ứng
     * @param particleName Tên particle
     * @param location     Vị trí hiển thị
     * @param count        Số lượng hạt
     * @param offsetX      Độ lệch X
     * @param offsetY      Độ lệch Y
     * @param offsetZ      Độ lệch Z
     * @param speed        Tốc độ hạt
     */
    public static void spawnParticleForPlayer(Player player, String particleName, Location location,
                                              int count, double offsetX, double offsetY, double offsetZ, double speed) {
        if (player == null || !player.isOnline() || location == null) {
            return;
        }

        try {
            if (IS_PRE_113) {
                // Phiên bản 1.12.2 không có phương thức spawnParticle riêng cho player
                // Tạo hiệu ứng ở vị trí của người chơi thay thế
                spawnParticle(location, particleName, count, offsetX, offsetY, offsetZ, speed);
            } else {
                try {
                    Particle particle = Particle.valueOf(particleName);
                    player.spawnParticle(particle, location, count, offsetX, offsetY, offsetZ, speed);
                } catch (IllegalArgumentException e) {
                    // Fallback nếu không tìm thấy particle
                    player.spawnParticle(Particle.FLAME, location, count, offsetX, offsetY, offsetZ, speed);
                }
            }
        } catch (Exception e) {
            // Bỏ qua lỗi
            System.out.println("Lỗi khi tạo hiệu ứng hạt cho người chơi: " + e.getMessage());
        }
    }

    /**
     * Tạo hiệu ứng hạt từ chuỗi cấu hình
     * Định dạng: "TÊN_PARTICLE:OFFSET_X:OFFSET_Y:OFFSET_Z:SPEED:COUNT"
     * Ví dụ: "VILLAGER_HAPPY:0.5:0.5:0.5:0.1:10"
     *
     * @param location       Vị trí hiển thị
     * @param particleConfig Chuỗi cấu hình
     */
    public static void spawnParticleFromConfig(Location location, String particleConfig) {
        if (location == null || particleConfig == null || particleConfig.isEmpty()) {
            return;
        }

        try {
            String[] parts = particleConfig.split(":");
            String particleName = parts[0];
            double offsetX = parts.length > 1 ? Double.parseDouble(parts[1]) : 0.5;
            double offsetY = parts.length > 2 ? Double.parseDouble(parts[2]) : 0.5;
            double offsetZ = parts.length > 3 ? Double.parseDouble(parts[3]) : 0.5;
            double speed = parts.length > 4 ? Double.parseDouble(parts[4]) : 0.1;
            int count = parts.length > 5 ? Integer.parseInt(parts[5]) : 10;

            // Giới hạn số lượng hạt để đảm bảo hiệu suất
            count = Math.min(count, 50);

            spawnParticle(location, particleName, count, offsetX, offsetY, offsetZ, speed);
        } catch (Exception e) {
            // Bỏ qua lỗi
            System.out.println("Lỗi khi tạo hiệu ứng hạt từ cấu hình: " + e.getMessage());
        }
    }
} 