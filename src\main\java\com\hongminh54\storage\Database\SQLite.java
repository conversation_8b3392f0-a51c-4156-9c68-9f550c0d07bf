package com.hongminh54.storage.Database;

import com.hongminh54.storage.Storage;

import java.io.File;
import java.io.IOException;
import java.sql.*;
import java.util.logging.Level;

public class SQLite extends Database {

    // Đơn giản hóa - chỉ giữ biến cần thiết
    private static final Object connectionLock = new Object();
    public String SQLiteCreateTokensTable = "CREATE TABLE IF NOT EXISTS PlayerData (" +
            "`player` VARCHAR(36) NOT NULL," +
            "`data` TEXT DEFAULT '{}'," +
            "`max` BIGINT NOT NULL," +
            "`statsData` TEXT DEFAULT '{}'," +
            "`auto_pickup` BOOLEAN DEFAULT 1," +
            "PRIMARY KEY (`player`)" +
            ");";
    String dbname;

    public SQLite(Storage instance) {
        super(instance);
        dbname = "PlayerData";
    }

    /**
     * Thiết lập PRAGMA ban đầu khi tạo connection mới (an toàn)
     *
     * @param connection Kết nối mới tạo
     */
    private void setupInitialPragmas(Connection connection) {
        if (connection == null) return;

        try (Statement stmt = connection.createStatement()) {
            // Thiết lập timeout cho statement
            stmt.setQueryTimeout(10);

            // Đọc cấu hình từ config.yml
            boolean walMode = com.hongminh54.storage.Utils.File.getConfig().getBoolean("database.sqlite.wal_mode", true);
            int cacheSize = com.hongminh54.storage.Utils.File.getConfig().getInt("database.sqlite.cache_size", 2000);
            int busyTimeout = com.hongminh54.storage.Utils.File.getConfig().getInt("database.sqlite.busy_timeout", 60000);

            // Thiết lập journal mode trước tiên
            stmt.execute("PRAGMA journal_mode = " + (walMode ? "WAL" : "DELETE"));

            // Thiết lập các PRAGMA cơ bản từ config
            stmt.execute("PRAGMA synchronous = 1"); // 1 = NORMAL
            stmt.execute("PRAGMA cache_size = " + cacheSize);
            stmt.execute("PRAGMA temp_store = 2"); // 2 = MEMORY
            stmt.execute("PRAGMA busy_timeout = " + busyTimeout);
            stmt.execute("PRAGMA foreign_keys = 1"); // 1 = ON

            // Thiết lập các PRAGMA quan trọng cho thread safety
            stmt.execute("PRAGMA locking_mode = NORMAL"); // Normal locking
            stmt.execute("PRAGMA read_uncommitted = 0"); // 0 = false, đảm bảo data consistency
            stmt.execute("PRAGMA recursive_triggers = 0"); // 0 = false, tắt recursive triggers
            stmt.execute("PRAGMA secure_delete = 0"); // 0 = OFF, tăng hiệu suất
            stmt.execute("PRAGMA auto_vacuum = 2"); // 2 = INCREMENTAL, tự động dọn dẹp

            // Thiết lập memory và performance - conservative settings
            stmt.execute("PRAGMA threads = 1"); // Chỉ 1 thread để tránh hoàn toàn race conditions
            stmt.execute("PRAGMA mmap_size = 67108864"); // 64MB memory mapping (giảm xuống)

            // Thêm các PRAGMA để tăng cường thread safety
            stmt.execute("PRAGMA case_sensitive_like = 1"); // Tăng tính nhất quán
            stmt.execute("PRAGMA defer_foreign_keys = 0"); // Không defer foreign key checks

            // Kiểm tra journal mode đã được thiết lập
            try (ResultSet rs = stmt.executeQuery("PRAGMA journal_mode")) {
                if (rs.next()) {
                    String mode = rs.getString(1);
                    Storage.getStorage().getLogger().info("SQLite journal mode: " + mode);
                }
            }

            Storage.getStorage().getLogger().info("SQLite PRAGMA settings applied successfully");
        } catch (SQLException e) {
            // Log warning nhưng không crash
            Storage.getStorage().getLogger().warning("Không thể thiết lập PRAGMA SQLite: " + e.getMessage());
        }
    }

    /**
     * Thiết lập PRAGMA cơ bản và an toàn cho SQLite (trong transaction)
     *
     * @param connection Kết nối cần thiết lập
     */
    private void setupBasicPragmas(Connection connection) {
        if (connection == null) return;

        try (Statement stmt = connection.createStatement()) {
            // Đảm bảo không có transaction đang hoạt động
            if (!connection.getAutoCommit()) {
                Storage.getStorage().getLogger().fine("Bỏ qua thiết lập PRAGMA vì đang trong transaction");
                return;
            }

            // Chỉ thiết lập busy_timeout vì nó an toàn trong mọi trường hợp
            stmt.execute("PRAGMA busy_timeout = 30000");
        } catch (SQLException e) {
            // Bỏ qua lỗi PRAGMA để tránh gây lỗi transaction
            Storage.getStorage().getLogger().fine("Bỏ qua thiết lập PRAGMA: " + e.getMessage());
        }
    }

    @Override
    public Connection getSQLConnection() {
        synchronized (connectionLock) {
            try {
                // Kiểm tra kết nối hiện tại
                if (connection != null && !connection.isClosed()) {
                    // Test kết nối đơn giản
                    try (Statement testStmt = connection.createStatement()) {
                        testStmt.setQueryTimeout(5);
                        testStmt.executeQuery("SELECT 1").close();
                        return connection;
                    } catch (SQLException testEx) {
                        // Kết nối không hoạt động, tạo mới
                        Storage.getStorage().getLogger().fine("Kết nối cũ không hoạt động, tạo mới: " + testEx.getMessage());
                    }
                }
            } catch (SQLException e) {
                // Kết nối bị lỗi, tạo mới
                Storage.getStorage().getLogger().fine("Lỗi kết nối: " + e.getMessage());
            }

            // Đóng kết nối cũ nếu có
            try {
                if (connection != null) {
                    connection.close();
                }
            } catch (SQLException closeEx) {
                // Bỏ qua lỗi đóng
            }
            connection = null;

            try {
                // Dọn dẹp các file database cũ với tên sai (do lỗi URL parameters)
                cleanupOldDatabaseFiles();

                File dataFolder = new File(Storage.getStorage().getDataFolder(), dbname + ".db");
                if (!dataFolder.exists()) {
                    dataFolder.createNewFile();
                }

                // Load SQLite driver với package gốc
                Class.forName("org.sqlite.JDBC");

                // Tạo connection URL đơn giản - không dùng parameters trong URL để tránh lỗi filename
                String connectionUrl = "jdbc:sqlite:" + dataFolder.getAbsolutePath();

                // Log connection URL for debugging
                Storage.getStorage().getLogger().info("Connecting to SQLite database: " + dataFolder.getAbsolutePath());

                connection = DriverManager.getConnection(connectionUrl);

                // Thiết lập PRAGMA một lần khi tạo connection mới
                setupInitialPragmas(connection);

                // Thiết lập timeout cho statement từ config
                int queryTimeout = com.hongminh54.storage.Utils.File.getConfig().getInt("database.sqlite.query_timeout", 30);
                try (Statement stmt = connection.createStatement()) {
                    stmt.setQueryTimeout(queryTimeout);
                } catch (SQLException timeoutEx) {
                    Storage.getStorage().getLogger().fine("Không thể thiết lập timeout: " + timeoutEx.getMessage());
                }

                return connection;
            } catch (SQLException | ClassNotFoundException | IOException ex) {
                Storage.getStorage().getLogger().log(Level.SEVERE, "SQLite connection error", ex);
                return null;
            }
        }
    }

    @Override
    public void load() {
        Connection conn = null;
        Statement s = null;

        try {
            conn = getSQLConnection();

            if (conn == null) {
                Storage.getStorage().getLogger().severe("Không thể kết nối cơ sở dữ liệu để khởi tạo bảng!");
                return;
            }

            s = conn.createStatement();

            // Thiết lập timeout lớn hơn để tránh lỗi database locked
            s.setQueryTimeout(60); // 60 giây timeout

            // Tạo bảng chính nếu chưa tồn tại
            s.executeUpdate(SQLiteCreateTokensTable);

            // Kiểm tra và thêm cột statsData nếu cần
            try {
                s.executeUpdate("ALTER TABLE PlayerData ADD COLUMN statsData TEXT DEFAULT '{}'");
                Storage.getStorage().getLogger().info("Added statsData column to the database");
            } catch (SQLException e) {
                // Cột đã tồn tại, bỏ qua lỗi
                if (!e.getMessage().contains("duplicate column name")) {
                    Storage.getStorage().getLogger().warning("Lỗi khi thêm cột statsData: " + e.getMessage());
                }
            }

            // Kiểm tra và thêm cột auto_pickup nếu cần
            try {
                s.executeUpdate("ALTER TABLE PlayerData ADD COLUMN auto_pickup BOOLEAN DEFAULT 1");
                Storage.getStorage().getLogger().info("Added auto_pickup column to the database");
            } catch (SQLException e) {
                // Cột đã tồn tại, bỏ qua lỗi
                if (!e.getMessage().contains("duplicate column name")) {
                    Storage.getStorage().getLogger().warning("Lỗi khi thêm cột auto_pickup: " + e.getMessage());
                }
            }

        } catch (SQLException e) {
            Storage.getStorage().getLogger().log(Level.SEVERE, "Lỗi khi khởi tạo cơ sở dữ liệu", e);
        } finally {
            try {
                if (s != null) {
                    s.close();
                }
            } catch (SQLException e) {
                Storage.getStorage().getLogger().warning("Không thể đóng statement: " + e.getMessage());
            }
        }

        // Kiểm tra và repair database nếu cần
        try {
            checkAndRepairDatabase();
        } catch (Exception e) {
            Storage.getStorage().getLogger().warning("Database check failed: " + e.getMessage());
        }

        // Khởi tạo connection pool
        initialize();
        Storage.getStorage().getLogger().info("Loaded SQLite Data");
    }

    /**
     * Thực hiện thao tác SQL với retry đơn giản
     *
     * @param operation  Thao tác cần thực hiện
     * @param maxRetries Số lần thử tối đa
     * @return true nếu thành công, false nếu thất bại
     */
    public boolean executeWithSimpleRetry(Runnable operation, int maxRetries) {
        for (int i = 0; i <= maxRetries; i++) {
            try {
                operation.run();
                return true;
            } catch (Exception e) {
                String errorMsg = e.getMessage();
                if (errorMsg != null && (errorMsg.contains("locked") || errorMsg.contains("busy"))) {
                    if (i < maxRetries) {
                        Storage.getStorage().getLogger().fine("Database busy, retry " + (i + 1) + "/" + maxRetries);
                        try {
                            Thread.sleep(100L * (i + 1)); // Tăng dần thời gian chờ
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt();
                            break;
                        }
                        continue;
                    }
                }
                Storage.getStorage().getLogger().warning("SQL operation failed: " + e.getMessage());
                break;
            }
        }
        return false;
    }

    /**
     * Kiểm tra và sửa chữa database nếu cần - Enhanced version để tránh crash
     */
    public void checkAndRepairDatabase() {
        try (Connection conn = getSQLConnection()) {
            if (conn == null) {
                Storage.getStorage().getLogger().warning("Không thể kết nối database để kiểm tra");
                return;
            }

            // Kiểm tra integrity với timeout
            try (Statement stmt = conn.createStatement()) {
                stmt.setQueryTimeout(30); // 30 giây timeout

                // Quick check trước
                ResultSet quickCheck = stmt.executeQuery("PRAGMA quick_check(1)");
                if (quickCheck.next()) {
                    String quickResult = quickCheck.getString(1);
                    if (!"ok".equalsIgnoreCase(quickResult)) {
                        Storage.getStorage().getLogger().warning("Database quick check failed: " + quickResult);

                        // Thử repair với VACUUM
                        try {
                            Storage.getStorage().getLogger().info("Attempting database repair with VACUUM...");
                            stmt.execute("VACUUM");
                            Storage.getStorage().getLogger().info("Database VACUUM completed");
                        } catch (SQLException vacuumEx) {
                            Storage.getStorage().getLogger().severe("VACUUM failed: " + vacuumEx.getMessage());
                        }
                    }
                }

                // Full integrity check
                ResultSet rs = stmt.executeQuery("PRAGMA integrity_check(10)");
                boolean hasIssues = false;
                while (rs.next()) {
                    String result = rs.getString(1);
                    if (!"ok".equalsIgnoreCase(result)) {
                        Storage.getStorage().getLogger().warning("Database integrity issue: " + result);
                        hasIssues = true;
                    }
                }

                if (!hasIssues) {
                    Storage.getStorage().getLogger().info("Database integrity check passed");
                }

            }
        } catch (SQLException e) {
            Storage.getStorage().getLogger().severe("Lỗi khi kiểm tra database: " + e.getMessage());

            // Thử tạo backup nếu có lỗi nghiêm trọng
            try {
                createEmergencyBackup();
            } catch (Exception backupEx) {
                Storage.getStorage().getLogger().severe("Emergency backup failed: " + backupEx.getMessage());
            }
        }
    }

    /**
     * Dọn dẹp các file database cũ với tên sai (do lỗi URL parameters)
     */
    private void cleanupOldDatabaseFiles() {
        try {
            File dataFolder = Storage.getStorage().getDataFolder();
            File[] files = dataFolder.listFiles();

            if (files != null) {
                for (File file : files) {
                    String fileName = file.getName();
                    // Tìm các file có tên chứa URL parameters (dấu hiệu của lỗi cũ)
                    if (fileName.startsWith("PlayerData.db?") ||
                            fileName.contains("read_uncommitted") ||
                            fileName.contains("enable_shared") ||
                            fileName.contains("cache=")) {

                        Storage.getStorage().getLogger().info("Cleaning up old database file: " + fileName);
                        if (file.delete()) {
                            Storage.getStorage().getLogger().info("Successfully deleted: " + fileName);
                        } else {
                            Storage.getStorage().getLogger().warning("Failed to delete: " + fileName);
                        }
                    }
                }
            }
        } catch (Exception e) {
            Storage.getStorage().getLogger().warning("Error during database cleanup: " + e.getMessage());
        }
    }

    /**
     * Tạo emergency backup khi database có vấn đề
     */
    private void createEmergencyBackup() {
        try {
            java.io.File dbFile = new java.io.File(Storage.getStorage().getDataFolder(), dbname + ".db");
            if (dbFile.exists()) {
                java.io.File backupFile = new java.io.File(Storage.getStorage().getDataFolder(),
                        dbname + "_emergency_backup_" + System.currentTimeMillis() + ".db");

                java.nio.file.Files.copy(dbFile.toPath(), backupFile.toPath());
                Storage.getStorage().getLogger().info("Emergency backup created: " + backupFile.getName());
            }
        } catch (Exception e) {
            Storage.getStorage().getLogger().severe("Failed to create emergency backup: " + e.getMessage());
        }
    }

}

