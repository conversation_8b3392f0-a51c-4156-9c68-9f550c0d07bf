package com.hongminh54.storage.Events;

import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.logging.Level;
import java.util.stream.Collectors;

import org.bukkit.Bukkit;
import org.bukkit.block.Block;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.block.BlockBreakEvent;

import com.hongminh54.storage.Manager.MineManager;
import com.hongminh54.storage.Storage;
import com.hongminh54.storage.Utils.File;

public class BlockBreakEvent_ implements Listener {

    // Cache để tránh thông báo kho đầy quá thường xuyên
    private static final ConcurrentHashMap<String, Long> storageFullNotificationCache = new ConcurrentHashMap<>();

    // Thời gian giữa các thông báo kho đầy (giây)
    private static final long NOTIFICATION_COOLDOWN = TimeUnit.SECONDS.toMillis(30);
    // Cache thời gian hiệu ứng cuối của mỗi người chơi
    private static final ConcurrentHashMap<String, Long> lastParticleEffectTime = new ConcurrentHashMap<>();
    // Khoảng thời gian tối thiểu giữa các hiệu ứng (milliseconds)
    private static final long PARTICLE_EFFECT_COOLDOWN = 800;
    // Cache đơn giản để kiểm soát tần suất đào block - tối ưu performance
    private static final ConcurrentHashMap<UUID, Long> lastBlockBreakTime = new ConcurrentHashMap<>();
    // Thêm biến để đếm số lượng block đã đào và kích hoạt lưu dữ liệu
    private static final ConcurrentHashMap<UUID, Integer> mineCountSinceLastSave = new ConcurrentHashMap<>();
    private static final int SAVE_THRESHOLD = 500; // Tăng ngưỡng lưu lên 500 để giảm số lần I/O và tránh backup liên tục
    // Theo dõi số lần đập block của từng người chơi cho cooldown limit
    private static final ConcurrentHashMap<UUID, Integer> playerBreakCount = new ConcurrentHashMap<>();
    private static final ConcurrentHashMap<UUID, Long> playerResetTime = new ConcurrentHashMap<>();
    // Cấu hình hiệu ứng
    private boolean effectsEnabled;
    private int maxParticleCount;
    private boolean autoPickupEnabled;
    private boolean cancelDropEnabled;
    private boolean sendMessages;
    // Cấu hình cooldown limit từ config
    private boolean limitEnabled;
    private int maxBreaks;
    private int resetInterval;

    // Constructor để khởi tạo cấu hình ngay khi class được tạo
    public BlockBreakEvent_() {
        loadConfig();
        // Đăng ký task dọn dẹp cache
        scheduleCacheCleanup();
    }

    // Tải lại cấu hình khi admin reload plugin
    public void loadConfig() {
        FileConfiguration config = File.getConfig();

        // Đọc cấu hình từ file config
        effectsEnabled = config.getBoolean("settings.effects_enabled", true);
        maxParticleCount = config.getInt("settings.max_particle_count", 15);
        autoPickupEnabled = config.getBoolean("settings.auto_pickup", true);
        cancelDropEnabled = config.getBoolean("settings.block_break.cancel_drop", true);
        sendMessages = config.getBoolean("settings.block_break.send_messages", false);

        // Đọc cấu hình cooldown limit
        limitEnabled = config.getBoolean("cooldown.limit_enabled", true);
        maxBreaks = config.getInt("cooldown.max_breaks", 60);
        resetInterval = config.getInt("cooldown.reset_interval", 15);

        // Ghi log thông tin cấu hình khi chạy ở chế độ debug
        if (Storage.getStorage().isDebug()) {
            Storage.getStorage().getLogger().info("BlockBreakEvent_ đã tải cấu hình: " + "effects=" + effectsEnabled + ", maxParticles=" + maxParticleCount + ", autoPickup=" + autoPickupEnabled + ", cancelDrop=" + cancelDropEnabled + ", sendMessages=" + sendMessages + ", chatty_miner=" + config.getBoolean("settings.chatty_miner", true) + ", chatty_miner_rate=" + config.getInt("settings.chatty_miner_rate", 15) + ", count_mined_blocks=" + config.getBoolean("settings.count_mined_blocks", true));
        }
    }

    @EventHandler(priority = EventPriority.HIGHEST, ignoreCancelled = false)
    public void onBlockBreak(BlockBreakEvent event) {
        // Kiểm tra event đã bị cancel chưa
        if (event.isCancelled()) {
            return;
        }

        Player player = event.getPlayer();

        // Kiểm tra auto pickup - nếu bật thì BlockBreakListener.java sẽ xử lý
        // Tránh duplicate processing và item drop
        if (MineManager.isAutoPickup(player)) {
            return; // BlockBreakListener.java sẽ xử lý auto pickup
        }

        Block block = event.getBlock();
        UUID playerUUID = player.getUniqueId();
        long now = System.currentTimeMillis(); // Thêm biến now

        // Kiểm tra cooldown ổn định - sử dụng giá trị từ config
        Long lastBreakTime = lastBlockBreakTime.get(playerUUID);
        long cooldownTime = File.getConfig().getLong("cooldown.block_break", 10);
        if (lastBreakTime != null && now - lastBreakTime < cooldownTime) {
            return; // Cooldown để tránh spam
        }

        lastBlockBreakTime.put(playerUUID, now);

        // Kiểm tra giới hạn đơn giản - bỏ qua nếu không cần thiết
        if (limitEnabled && !checkSimpleBreakLimit(playerUUID, now)) {
            return; // Đã vượt quá giới hạn
        }

        // Không can thiệp vào lá cây và gỗ - để AxeEnchantListener xử lý
        String blockType = block.getType().name().toUpperCase();
        if (blockType.contains("LOG") || blockType.contains("WOOD") || blockType.endsWith("_STEM") || blockType.contains("STRIPPED") || blockType.contains("LEAVES") || blockType.equals("LEAVES") || blockType.equals("LEAVES_2")) {
            return; // Không xử lý gỗ và lá cây
        }

        // Kiểm tra khối có phải là khối cần thu thập không - truy cập trực tiếp
        if (!MineManager.checkBreak(block)) {
            return;
        }

        // Lấy loại tài nguyên từ khối
        String resource = MineManager.getDrop(block);
        if (resource == null) {
            return;
        }

        // BlockBreakEvent_.java chỉ xử lý các logic khác, không xử lý auto pickup
        // Auto pickup được xử lý hoàn toàn bởi BlockBreakListener.java
        // Logic này được giữ lại cho các tính năng khác nếu cần trong tương lai
        return;


    }

    /**
     * Dọn dẹp đơn giản
     */
    public void scheduleCacheCleanup() {
        int resetInterval = File.getConfig().getInt("cooldown.reset_interval", 5);
        Bukkit.getScheduler().runTaskTimerAsynchronously(Storage.getStorage(), this::resetBreakCounters, 20L * resetInterval, 20L * resetInterval);

        // Dọn dẹp local cache
        Bukkit.getScheduler().runTaskTimerAsynchronously(Storage.getStorage(), this::cleanupLocalCache, 20 * 60, 20 * 60); // Mỗi phút
    }

    /**
     * Dọn dẹp cache cục bộ của BlockBreakEvent
     */
    private void cleanupLocalCache() {
        try {
            // Dọn dẹp thông báo kho đầy cũ
            long now = System.currentTimeMillis();
            List<String> keysToRemove = storageFullNotificationCache.entrySet().stream().filter(entry -> now - entry.getValue() > NOTIFICATION_COOLDOWN * 2).map(Map.Entry::getKey).collect(Collectors.toList());

            for (String key : keysToRemove) {
                storageFullNotificationCache.remove(key);
            }

            // Dọn dẹp thời gian hiệu ứng cuối
            List<String> effectKeysToRemove = lastParticleEffectTime.entrySet().stream().filter(entry -> now - entry.getValue() > PARTICLE_EFFECT_COOLDOWN * 5).map(Map.Entry::getKey).collect(Collectors.toList());

            for (String key : effectKeysToRemove) {
                lastParticleEffectTime.remove(key);
            }

            // Log thông tin ở chế độ debug
            if (Storage.getStorage().getLogger().isLoggable(Level.FINE)) {
                Storage.getStorage().getLogger().fine("BlockBreakEvent local cache cleanup: Removed " + keysToRemove.size() + " storage notification entries and " + effectKeysToRemove.size() + " effect entries");
            }
        } catch (Exception e) {
            // Bỏ qua lỗi khi dọn dẹp cache
            Storage.getStorage().getLogger().warning("Lỗi khi dọn dẹp local cache: " + e.getMessage());
        }
    }

    /**
     * Đặt lại bộ đếm đào block cho tất cả người chơi
     */
    private void resetBreakCounters() {
        try {
            // Dọn dẹp cache cooldown
            lastBlockBreakTime.clear();
            if (Storage.getStorage().getLogger().isLoggable(Level.FINE)) {
                Storage.getStorage().getLogger().fine("Đã đặt lại cache cooldown cho tất cả người chơi");
            }
        } catch (Exception e) {
            // Bỏ qua lỗi khi đặt lại bộ đếm
        }
    }

    /**
     * Phát hiệu ứng thu thập tài nguyên
     *
     * @param player           Người chơi
     * @param location         Vị trí khối
     * @param maxParticleCount Số lượng hạt tối đa
     */
    private void playCollectEffect(Player player, org.bukkit.Location location, int maxParticleCount) {
        // Kiểm tra xem có nhiều người chơi đang đào cùng lúc không để điều chỉnh hiệu ứng
        int onlinePlayers = Bukkit.getOnlinePlayers().size();
        int adjustedParticleLimit;

        // Điều chỉnh số lượng hạt dựa trên số người chơi online
        if (onlinePlayers > 20) {
            adjustedParticleLimit = 3; // Rất ít hạt nếu server đông
        } else if (onlinePlayers > 10) {
            adjustedParticleLimit = 5; // Ít hạt nếu có nhiều người
        } else {
            adjustedParticleLimit = 8; // Số lượng bình thường nếu ít người
        }

        // Giới hạn số lượng hạt xuống mức thấp hơn để tăng hiệu suất
        int particleLimit = Math.min(maxParticleCount, adjustedParticleLimit);

        // Đọc cấu hình hiệu ứng từ config nếu có
        String effectConfig = File.getConfig().getString("effects.collect.particle", "VILLAGER_HAPPY:0.3:0.3:0.3:0.05:3");
        String soundConfig = File.getConfig().getString("effects.collect.sound", "ENTITY_ITEM_PICKUP:0.2:0.8");

        // Xử lý hiệu ứng hạt
        if (effectConfig != null && !effectConfig.isEmpty()) {
            try {
                String[] parts = effectConfig.split(":");
                org.bukkit.Particle particleType;
                try {
                    particleType = org.bukkit.Particle.valueOf(parts[0]);
                } catch (IllegalArgumentException e) {
                    // Fallback to a safe particle if not found - tương thích đa phiên bản
                    try {
                        particleType = org.bukkit.Particle.HAPPY_VILLAGER;
                    } catch (NoSuchFieldError ex) {
                        particleType = org.bukkit.Particle.valueOf("VILLAGER_HAPPY");
                    }
                }

                double offsetX = parts.length > 1 ? Double.parseDouble(parts[1]) : 0.3;
                double offsetY = parts.length > 2 ? Double.parseDouble(parts[2]) : 0.3;
                double offsetZ = parts.length > 3 ? Double.parseDouble(parts[3]) : 0.3;
                double speed = parts.length > 4 ? Double.parseDouble(parts[4]) : 0.05;
                int count = parts.length > 5 ? Integer.parseInt(parts[5]) : 3;

                // Giới hạn số lượng hạt để tránh ảnh hưởng hiệu suất
                count = Math.min(count, particleLimit);

                // Sử dụng vị trí khối + 0.5 để hiệu ứng xuất hiện ở giữa khối
                location = location.clone().add(0.5, 0.5, 0.5);

                // Giảm tần suất phát hiệu ứng khi đào nhanh
                if (player.getHealth() > 0 && player.getGameMode() == org.bukkit.GameMode.SURVIVAL) {
                    // Chỉ hiển thị hiệu ứng cho người chơi đào - tối ưu bằng cách không hiển thị cho các người chơi khác
                    player.spawnParticle(particleType, location, count, offsetX, offsetY, offsetZ, speed);
                }
            } catch (Exception e) {
                // Bỏ qua nếu có lỗi khi tạo hiệu ứng
            }
        }

        // Xử lý âm thanh - chỉ phát 50% thời gian để giảm lag
        if (soundConfig != null && !soundConfig.isEmpty() && Math.random() > 0.5) {
            try {
                String[] parts = soundConfig.split(":");
                String soundName = parts[0];
                float volume = parts.length > 1 ? Float.parseFloat(parts[1]) : 0.2f;
                float pitch = parts.length > 2 ? Float.parseFloat(parts[2]) : 0.8f;

                // Giảm âm lượng khi đào nhanh và sử dụng SoundCompatibility
                com.hongminh54.storage.compatibility.SoundCompatibility.playSound(player, soundName, volume * 0.7f, pitch);
            } catch (Exception e) {
                // Bỏ qua nếu có lỗi khi phát âm thanh
            }
        }
    }


    /**
     * Kiểm tra giới hạn số lần đập block của người chơi
     *
     * @param playerUUID  UUID của người chơi
     * @param currentTime Thời gian hiện tại
     * @return true nếu được phép đập, false nếu vượt quá giới hạn
     */
    private boolean checkBreakLimit(UUID playerUUID, long currentTime) {
        // Kiểm tra thời gian reset
        Long lastResetTime = playerResetTime.get(playerUUID);
        long resetIntervalMs = resetInterval * 1000L;

        if (lastResetTime == null || currentTime - lastResetTime >= resetIntervalMs) {
            // Reset bộ đếm
            playerBreakCount.put(playerUUID, 1);
            playerResetTime.put(playerUUID, currentTime);
            return true;
        }

        // Tăng bộ đếm
        int currentCount = playerBreakCount.getOrDefault(playerUUID, 0) + 1;
        playerBreakCount.put(playerUUID, currentCount);

        // Kiểm tra giới hạn
        return currentCount <= maxBreaks;
    }

    /**
     * Kiểm tra break limit đơn giản - tối ưu performance
     */
    private boolean checkSimpleBreakLimit(UUID playerUUID, long currentTime) {
        // Kiểm tra thời gian reset
        Long lastResetTime = playerResetTime.get(playerUUID);
        long resetIntervalMs = resetInterval * 1000L;

        if (lastResetTime == null || currentTime - lastResetTime >= resetIntervalMs) {
            // Reset bộ đếm
            playerBreakCount.put(playerUUID, 1);
            playerResetTime.put(playerUUID, currentTime);
            return true;
        }

        // Tăng bộ đếm đơn giản
        int currentCount = playerBreakCount.getOrDefault(playerUUID, 0) + 1;
        playerBreakCount.put(playerUUID, currentCount);

        // Kiểm tra giới hạn
        return currentCount <= maxBreaks;
    }

    /**
     * Lấy tên hiển thị của vật liệu - truy cập trực tiếp config
     */
    private String getMaterialDisplayName(String materialKey) {
        if (materialKey == null) {
            return "Unknown";
        }

        String display = File.getConfig().getString("items." + materialKey);
        if (display != null) {
            return display;
        }

        String[] parts = materialKey.split(";");
        String material = parts[0];
        return formatMaterialName(material);
    }

    /**
     * Format tên material thành dạng dễ đọc
     */
    private String formatMaterialName(String material) {
        if (material == null) {
            return "Unknown";
        }

        String[] words = material.toLowerCase().split("_");
        StringBuilder result = new StringBuilder();

        for (String word : words) {
            if (result.length() > 0) {
                result.append(" ");
            }
            result.append(word.substring(0, 1).toUpperCase()).append(word.substring(1));
        }

        return result.toString();
    }
}