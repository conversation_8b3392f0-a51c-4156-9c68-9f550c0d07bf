package com.hongminh54.storage.compatibility;

import com.hongminh54.storage.NMS.NMSAssistant;
import com.hongminh54.storage.Storage;
import com.hongminh54.storage.Utils.Chat;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;

import java.lang.reflect.Constructor;

/**
 * Lớp hỗ trợ tương thích Player API cho Minecraft 1.12.2 - 1.21.x
 * Xử lý các vấn đề tương thích với action bar, title, subtitle, boss bar, v.v.
 */
public class PlayerCompatibility {

    private static final NMSAssistant nmsAssistant = new NMSAssistant();
    private static final boolean IS_PRE_113 = nmsAssistant.isVersionLessThan(13);
    private static final boolean IS_PRE_116 = nmsAssistant.isVersionLessThan(16);
    private static final boolean IS_1_20_5_OR_HIGHER = nmsAssistant.is1_20_5OrHigher();
    private static final boolean IS_1_21_4_OR_HIGHER = nmsAssistant.is1_21_4OrHigher();

    /**
     * Gửi action bar message một cách an toàn cho tất cả phiên bản
     *
     * @param player  Người chơi nhận message
     * @param message Nội dung message
     */
    public static void sendActionBar(Player player, String message) {
        if (player == null || message == null || message.trim().isEmpty()) {
            return;
        }

        // Kiểm tra player có online không
        if (!isPlayerOnline(player)) {
            return;
        }

        try {
            if (IS_PRE_116) {
                // Phiên bản 1.12.2 - 1.15.x: Sử dụng Spigot API
                sendActionBarLegacy(player, message);
            } else {
                // Phiên bản 1.16+: Sử dụng Adventure API hoặc Spigot API
                sendActionBarModern(player, message);
            }
        } catch (Exception e) {
            // Fallback: gửi message thông thường nếu actionbar thất bại
            try {
                player.sendMessage(Chat.colorizewp(message));
            } catch (Exception fallbackEx) {
                // Log lỗi nếu cả fallback cũng thất bại
                if (Storage.getStorage().isDebug()) {
                    Storage.getStorage().getLogger().warning("Không thể gửi action bar và fallback message: " + e.getMessage());
                }
            }
        }
    }

    /**
     * Gửi title và subtitle một cách an toàn
     *
     * @param player   Người chơi
     * @param title    Title chính
     * @param subtitle Subtitle
     * @param fadeIn   Thời gian fade in (ticks)
     * @param stay     Thời gian hiển thị (ticks)
     * @param fadeOut  Thời gian fade out (ticks)
     */
    public static void sendTitle(Player player, String title, String subtitle, int fadeIn, int stay, int fadeOut) {
        if (player == null) {
            return;
        }

        // Kiểm tra player có online không
        if (!isPlayerOnline(player)) {
            return;
        }

        // Kiểm tra ít nhất một trong title hoặc subtitle có nội dung
        if ((title == null || title.trim().isEmpty()) && (subtitle == null || subtitle.trim().isEmpty())) {
            return;
        }

        try {
            if (IS_PRE_113) {
                // Phiên bản 1.12.2: Sử dụng reflection
                sendTitleLegacy(player, title, subtitle, fadeIn, stay, fadeOut);
            } else {
                // Phiên bản 1.13+: Sử dụng Spigot API
                player.sendTitle(
                        title != null && !title.trim().isEmpty() ? Chat.colorizewp(title) : "",
                        subtitle != null && !subtitle.trim().isEmpty() ? Chat.colorizewp(subtitle) : "",
                        fadeIn, stay, fadeOut
                );
            }
        } catch (Exception e) {
            // Fallback: gửi message thông thường
            try {
                if (title != null && !title.trim().isEmpty()) {
                    player.sendMessage(Chat.colorizewp(title));
                }
                if (subtitle != null && !subtitle.trim().isEmpty()) {
                    player.sendMessage(Chat.colorizewp(subtitle));
                }
            } catch (Exception fallbackEx) {
                // Log lỗi nếu cả fallback cũng thất bại
                if (Storage.getStorage().isDebug()) {
                    Storage.getStorage().getLogger().warning("Không thể gửi title và fallback message: " + e.getMessage());
                }
            }
        }
    }

    /**
     * Reset title cho player
     *
     * @param player Người chơi
     */
    public static void resetTitle(Player player) {
        if (player == null) {
            return;
        }

        try {
            if (!IS_PRE_113) {
                player.resetTitle();
            }
            // Phiên bản cũ không có method resetTitle
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể reset title: " + e.getMessage());
            }
        }
    }

    /**
     * Gửi experience một cách an toàn
     *
     * @param player Người chơi
     * @param amount Số lượng experience
     */
    public static void giveExperience(Player player, int amount) {
        if (player == null || amount <= 0) {
            return;
        }

        try {
            player.giveExp(amount);
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể thêm experience: " + e.getMessage());
            }
        }
    }

    /**
     * Gửi action bar cho phiên bản mới (1.16+)
     */
    private static void sendActionBarModern(Player player, String message) {
        try {
            // Thử sử dụng Spigot API trước
            player.spigot().sendMessage(net.md_5.bungee.api.ChatMessageType.ACTION_BAR,
                    net.md_5.bungee.api.chat.TextComponent.fromLegacyText(Chat.colorizewp(message)));
        } catch (Exception e) {
            // Fallback với reflection
            sendActionBarLegacy(player, message);
        }
    }

    /**
     * Gửi action bar cho phiên bản cũ (1.12.2-1.15.x)
     */
    private static void sendActionBarLegacy(Player player, String message) {
        try {
            // Thử với Spigot API trước
            player.spigot().sendMessage(net.md_5.bungee.api.ChatMessageType.ACTION_BAR,
                    net.md_5.bungee.api.chat.TextComponent.fromLegacyText(Chat.colorizewp(message)));
        } catch (Exception e) {
            // Fallback với NMS reflection
            sendActionBarNMS(player, message);
        }
    }

    /**
     * Gửi title cho phiên bản cũ (1.12.2) sử dụng reflection
     */
    private static void sendTitleLegacy(Player player, String title, String subtitle, int fadeIn, int stay, int fadeOut) {
        try {
            String version = Bukkit.getServer().getClass().getPackage().getName().split("\\.")[3];

            // Tạo title packet
            if (title != null && !title.isEmpty()) {
                Object titlePacket = createTitlePacket(version, "TITLE", Chat.colorizewp(title), fadeIn, stay, fadeOut);
                sendPacket(player, titlePacket, version);
            }

            // Tạo subtitle packet
            if (subtitle != null && !subtitle.isEmpty()) {
                Object subtitlePacket = createTitlePacket(version, "SUBTITLE", Chat.colorizewp(subtitle), fadeIn, stay, fadeOut);
                sendPacket(player, subtitlePacket, version);
            }

        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Lỗi gửi title legacy: " + e.getMessage());
            }
        }
    }

    /**
     * Gửi action bar sử dụng NMS reflection
     */
    private static void sendActionBarNMS(Player player, String message) {
        try {
            String version = Bukkit.getServer().getClass().getPackage().getName().split("\\.")[3];

            // Tạo ChatComponentText
            Class<?> chatComponentTextClass = Class.forName("net.minecraft.server." + version + ".ChatComponentText");
            Constructor<?> chatConstructor = chatComponentTextClass.getConstructor(String.class);
            Object chatComponent = chatConstructor.newInstance(Chat.colorizewp(message));

            // Tạo PacketPlayOutChat với action bar type
            Class<?> packetClass = Class.forName("net.minecraft.server." + version + ".PacketPlayOutChat");
            Class<?> chatMessageTypeClass = Class.forName("net.minecraft.server." + version + ".ChatMessageType");

            Object packet;
            if (IS_PRE_113) {
                // 1.12.2: Sử dụng constructor với byte type
                Constructor<?> packetConstructor = packetClass.getConstructor(chatComponentTextClass, byte.class);
                packet = packetConstructor.newInstance(chatComponent, (byte) 2); // 2 = ACTION_BAR
            } else {
                // 1.13+: Sử dụng ChatMessageType enum
                Object actionBarType = chatMessageTypeClass.getEnumConstants()[2]; // ACTION_BAR
                Constructor<?> packetConstructor = packetClass.getConstructor(chatComponentTextClass, chatMessageTypeClass);
                packet = packetConstructor.newInstance(chatComponent, actionBarType);
            }

            sendPacket(player, packet, version);

        } catch (Exception e) {
            // Cuối cùng fallback thành message thông thường
            player.sendMessage(Chat.colorizewp(message));
        }
    }

    /**
     * Tạo title packet sử dụng reflection
     */
    private static Object createTitlePacket(String version, String action, String text, int fadeIn, int stay, int fadeOut) throws Exception {
        Class<?> packetClass = Class.forName("net.minecraft.server." + version + ".PacketPlayOutTitle");
        Class<?> chatComponentClass = Class.forName("net.minecraft.server." + version + ".IChatBaseComponent");
        Class<?> chatComponentTextClass = Class.forName("net.minecraft.server." + version + ".ChatComponentText");
        Class<?> enumTitleActionClass = Class.forName("net.minecraft.server." + version + ".PacketPlayOutTitle$EnumTitleAction");

        // Tạo ChatComponentText
        Constructor<?> chatConstructor = chatComponentTextClass.getConstructor(String.class);
        Object chatComponent = chatConstructor.newInstance(text);

        // Lấy action enum
        Object titleAction = null;
        for (Object enumConstant : enumTitleActionClass.getEnumConstants()) {
            if (enumConstant.toString().equals(action)) {
                titleAction = enumConstant;
                break;
            }
        }

        // Tạo packet
        Constructor<?> packetConstructor = packetClass.getConstructor(enumTitleActionClass, chatComponentClass, int.class, int.class, int.class);
        return packetConstructor.newInstance(titleAction, chatComponent, fadeIn, stay, fadeOut);
    }

    /**
     * Gửi packet đến player sử dụng reflection
     */
    private static void sendPacket(Player player, Object packet, String version) throws Exception {
        Class<?> craftPlayerClass = Class.forName("org.bukkit.craftbukkit." + version + ".entity.CraftPlayer");
        Object craftPlayer = craftPlayerClass.cast(player);

        Object handle = craftPlayerClass.getMethod("getHandle").invoke(craftPlayer);
        Object playerConnection = handle.getClass().getField("playerConnection").get(handle);
        playerConnection.getClass().getMethod("sendPacket", Class.forName("net.minecraft.server." + version + ".Packet"))
                .invoke(playerConnection, packet);
    }

    /**
     * Kiểm tra xem player có online không một cách an toàn
     *
     * @param player Người chơi
     * @return true nếu player online
     */
    public static boolean isPlayerOnline(Player player) {
        if (player == null) {
            return false;
        }

        try {
            return player.isOnline();
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * Lấy ping của player một cách an toàn
     *
     * @param player Người chơi
     * @return Ping của player hoặc -1 nếu có lỗi
     */
    public static int getPlayerPing(Player player) {
        if (player == null) {
            return -1;
        }

        try {
            if (IS_1_20_5_OR_HIGHER) {
                // Minecraft 1.20.5+: Sử dụng getPing() method
                return player.getPing();
            } else {
                // Phiên bản cũ: Sử dụng reflection
                return getPlayerPingReflection(player);
            }
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể lấy ping của player: " + e.getMessage());
            }
            return -1;
        }
    }

    /**
     * Lấy ping sử dụng reflection cho phiên bản cũ
     */
    private static int getPlayerPingReflection(Player player) {
        try {
            String version = Bukkit.getServer().getClass().getPackage().getName().split("\\.")[3];
            Class<?> craftPlayerClass = Class.forName("org.bukkit.craftbukkit." + version + ".entity.CraftPlayer");
            Object craftPlayer = craftPlayerClass.cast(player);

            Object handle = craftPlayerClass.getMethod("getHandle").invoke(craftPlayer);
            return (Integer) handle.getClass().getField("ping").get(handle);
        } catch (Exception e) {
            return -1;
        }
    }
}
