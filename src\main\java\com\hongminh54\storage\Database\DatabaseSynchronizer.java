package com.hongminh54.storage.Database;

import com.hongminh54.storage.Storage;
import com.hongminh54.storage.Utils.File;
import org.bukkit.Bukkit;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.logging.Level;

/**
 * Quản lý đồng bộ hóa các thao tác SQL để tối ưu hiệu suất và tránh xung đột
 * Hỗ trợ batch processing và queue management
 */
public class DatabaseSynchronizer {

    // Batch processing queue
    private final BlockingQueue<DatabaseOperation> operationQueue = new LinkedBlockingQueue<>();
    private final ExecutorService batchProcessor = Executors.newSingleThreadExecutor(r -> {
        Thread t = new Thread(r, "Storage-DB-Batch");
        t.setDaemon(true);
        return t;
    });

    // Counters và metrics
    private final AtomicInteger pendingOperations = new AtomicInteger(0);
    private final AtomicInteger processedOperations = new AtomicInteger(0);

    // Configuration từ config.yml
    private int maxBatchSize;
    private long batchTimeout;
    private long processInterval;
    private boolean enabled;

    private volatile boolean isShuttingDown = false;

    public DatabaseSynchronizer() {
        loadConfiguration();
        if (enabled) {
            startBatchProcessor();
            Storage.getStorage().getLogger().info("DatabaseSynchronizer đã khởi động với batch size: " + maxBatchSize);
        }
    }

    /**
     * Load cấu hình từ config.yml
     */
    private void loadConfiguration() {
        enabled = File.getConfig().getBoolean("database.batch.enabled", true);
        maxBatchSize = File.getConfig().getInt("database.batch.max_size", 5); // Giảm từ 10 xuống 5 để giảm lag
        batchTimeout = File.getConfig().getLong("database.batch.timeout", 80000); // Tăng từ 60000 lên 80000
        processInterval = File.getConfig().getLong("database.batch.process_interval", 800); // Tăng từ 400 lên 800

        // Validate configuration - Tối ưu cho TPS
        if (maxBatchSize < 1) maxBatchSize = 5; // Giảm default
        if (batchTimeout < 1000) batchTimeout = 80000; // Tăng default
        if (processInterval < 100) processInterval = 800; // Tăng default
    }

    /**
     * Khởi động batch processor
     */
    private void startBatchProcessor() {
        // Batch processor chạy trong background
        batchProcessor.submit(() -> {
            while (!isShuttingDown && !Thread.currentThread().isInterrupted()) {
                try {
                    // Kiểm tra shutdown trước khi xử lý
                    if (isShuttingDown) {
                        break;
                    }
                    processBatch();
                    Thread.sleep(processInterval); // Chờ theo config
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                } catch (Exception e) {
                    // Chỉ log error nếu không phải đang shutdown
                    if (!isShuttingDown) {
                        Storage.getStorage().getLogger().warning("Lỗi trong batch processor: " + e.getMessage());
                    }
                }
            }
        });

        // Scheduler để xử lý batch định kỳ từ main thread - Tối ưu TPS
        long schedulerInterval = Math.max(processInterval / 50, 200); // Tối thiểu 10 giây (200 ticks)
        Bukkit.getScheduler().runTaskTimerAsynchronously(Storage.getStorage(), () -> {
            if (!isShuttingDown && !operationQueue.isEmpty()) {
                try {

                    // Kiểm tra số lượng người chơi để điều chỉnh batch size
                    int playerCount = org.bukkit.Bukkit.getOnlinePlayers().size();
                    if (playerCount > 50) {
                        // Với nhiều người chơi, giảm batch size
                        processBatchWithLimit(Math.max(1, maxBatchSize / 3));
                    } else {
                        processBatch();
                    }
                } catch (Exception e) {
                    Storage.getStorage().getLogger().warning("Lỗi trong scheduled batch: " + e.getMessage());
                }
            }
        }, schedulerInterval, schedulerInterval);
    }

    /**
     * Thêm operation vào queue để xử lý batch
     */
    public void queueOperation(DatabaseOperation operation) {
        if (!enabled || isShuttingDown) {
            // Nếu đang shutdown, bỏ qua operation để tránh spam log
            if (isShuttingDown) {
                return;
            }
            // Thực hiện ngay lập tức nếu batch disabled
            executeOperationDirect(operation);
            return;
        }

        try {
            operationQueue.offer(operation, 5, TimeUnit.SECONDS);
            pendingOperations.incrementAndGet();

            // Nếu queue đầy, xử lý batch ngay (chỉ khi không shutdown)
            if (operationQueue.size() >= maxBatchSize && !isShuttingDown) {
                CompletableFuture.runAsync(this::processBatch);
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            // Fallback: thực hiện ngay (chỉ khi không shutdown)
            if (!isShuttingDown) {
                executeOperationDirect(operation);
            }
        }
    }

    /**
     * Xử lý batch operations
     */
    private void processBatch() {
        if (operationQueue.isEmpty() || isShuttingDown) return;

        Connection conn = null;
        try {
            conn = Storage.db.getConnection();
            if (conn == null) {
                // Chỉ log warning nếu không phải đang shutdown
                if (!isShuttingDown) {
                    Storage.getStorage().getLogger().warning("Không thể lấy connection cho batch processing");
                }
                return;
            }

            // Disable auto-commit cho batch
            conn.setAutoCommit(false);

            int batchCount = 0;
            long startTime = System.currentTimeMillis();

            // Xử lý tối đa maxBatchSize operations hoặc trong thời gian timeout
            while (batchCount < maxBatchSize &&
                    (System.currentTimeMillis() - startTime) < batchTimeout) {

                DatabaseOperation operation = operationQueue.poll(100, TimeUnit.MILLISECONDS);
                if (operation == null) break;

                try {
                    executeOperationInBatch(conn, operation);
                    batchCount++;
                    pendingOperations.decrementAndGet();
                } catch (Exception e) {
                    Storage.getStorage().getLogger().warning("Lỗi khi thực hiện operation trong batch: " + e.getMessage());
                }
            }

            // Commit batch nếu có operations
            if (batchCount > 0) {
                conn.commit();
                processedOperations.addAndGet(batchCount);

                if (Storage.getStorage().getLogger().isLoggable(Level.FINE)) {
                    Storage.getStorage().getLogger().fine("Đã xử lý batch với " + batchCount + " operations");
                }
            }

        } catch (Exception e) {
            Storage.getStorage().getLogger().severe("Lỗi trong batch processing: " + e.getMessage());
            if (conn != null) {
                try {
                    conn.rollback();
                } catch (SQLException rollbackEx) {
                    Storage.getStorage().getLogger().severe("Lỗi khi rollback batch: " + rollbackEx.getMessage());
                }
            }
        } finally {
            if (conn != null) {
                try {
                    conn.setAutoCommit(true);
                    Storage.db.returnConnection(conn);
                } catch (SQLException e) {
                    Storage.getStorage().getLogger().warning("Lỗi khi reset connection: " + e.getMessage());
                }
            }
        }
    }

    /**
     * Xử lý batch với giới hạn số lượng operations - Tối ưu TPS
     */
    private void processBatchWithLimit(int limit) {
        if (operationQueue.isEmpty() || limit <= 0) {
            return;
        }

        List<DatabaseOperation> batch = new ArrayList<>();

        // Lấy operations với giới hạn
        for (int i = 0; i < limit && !operationQueue.isEmpty(); i++) {
            DatabaseOperation op = operationQueue.poll();
            if (op != null) {
                batch.add(op);
            }
        }

        if (batch.isEmpty()) {
            return;
        }

        // Xử lý batch nhỏ hơn
        processBatchOperations(batch);
    }

    /**
     * Xử lý danh sách operations
     */
    private void processBatchOperations(List<DatabaseOperation> operations) {
        Connection conn = null;
        try {
            conn = Storage.db.getConnection();
            if (conn == null) {
                // Đưa operations trở lại queue nếu không có connection
                operationQueue.addAll(operations);
                return;
            }

            conn.setAutoCommit(false);

            for (DatabaseOperation operation : operations) {
                executeOperationInBatch(conn, operation);
            }

            conn.commit();

        } catch (SQLException e) {
            Storage.getStorage().getLogger().warning("Lỗi khi xử lý batch operations: " + e.getMessage());
            if (conn != null) {
                try {
                    conn.rollback();
                } catch (SQLException rollbackEx) {
                    Storage.getStorage().getLogger().severe("Lỗi khi rollback batch: " + rollbackEx.getMessage());
                }
            }
        } finally {
            if (conn != null) {
                try {
                    conn.setAutoCommit(true);
                    Storage.db.returnConnection(conn);
                } catch (SQLException e) {
                    Storage.getStorage().getLogger().warning("Lỗi khi reset connection: " + e.getMessage());
                }
            }
        }
    }

    /**
     * Thực hiện operation trong batch transaction
     */
    private void executeOperationInBatch(Connection conn, DatabaseOperation operation) throws SQLException {
        switch (operation.getType()) {
            case UPDATE:
                executeUpdateInBatch(conn, operation);
                break;
            case INSERT:
                executeInsertInBatch(conn, operation);
                break;
            case DELETE:
                executeDeleteInBatch(conn, operation);
                break;
        }
    }

    /**
     * Thực hiện UPDATE trong batch
     */
    private void executeUpdateInBatch(Connection conn, DatabaseOperation operation) throws SQLException {
        PlayerData data = operation.getPlayerData();
        try (PreparedStatement ps = conn.prepareStatement(
                "UPDATE " + Storage.db.table + " SET data = ?, max = ?, statsData = ?, auto_pickup = ? WHERE player = ?")) {
            ps.setString(1, data.getData());
            ps.setInt(2, data.getMax());
            ps.setString(3, data.getStatsData());
            ps.setBoolean(4, data.isAutoPickup());
            ps.setString(5, data.getPlayer());
            ps.executeUpdate();
        }
    }

    /**
     * Thực hiện INSERT trong batch
     */
    private void executeInsertInBatch(Connection conn, DatabaseOperation operation) throws SQLException {
        PlayerData data = operation.getPlayerData();
        try (PreparedStatement ps = conn.prepareStatement(
                "INSERT INTO " + Storage.db.table + " (player,data,max,statsData,auto_pickup) VALUES(?,?,?,?,?)")) {
            ps.setString(1, data.getPlayer());
            ps.setString(2, data.getData());
            ps.setInt(3, data.getMax());
            ps.setString(4, data.getStatsData());
            ps.setBoolean(5, data.isAutoPickup());
            ps.executeUpdate();
        }
    }

    /**
     * Thực hiện DELETE trong batch
     */
    private void executeDeleteInBatch(Connection conn, DatabaseOperation operation) throws SQLException {
        try (PreparedStatement ps = conn.prepareStatement(
                "DELETE FROM " + Storage.db.table + " WHERE player = ?")) {
            ps.setString(1, operation.getPlayerName());
            ps.executeUpdate();
        }
    }

    /**
     * Thực hiện operation ngay lập tức (không batch)
     */
    private void executeOperationDirect(DatabaseOperation operation) {
        // Bỏ qua nếu đang shutdown để tránh lỗi connection
        if (isShuttingDown) {
            return;
        }

        try {
            switch (operation.getType()) {
                case UPDATE:
                    Storage.db.updateTableDirect(operation.getPlayerData());
                    break;
                case INSERT:
                    Storage.db.createTable(operation.getPlayerData());
                    break;
                case DELETE:
                    Storage.db.deleteData(operation.getPlayerName());
                    break;
            }
        } catch (Exception e) {
            // Chỉ log error nếu không phải đang shutdown
            if (!isShuttingDown) {
                Storage.getStorage().getLogger().warning("Lỗi khi thực hiện operation trực tiếp: " + e.getMessage());
            }
        }
    }

    /**
     * Flush tất cả pending operations (dùng khi shutdown)
     */
    public void flushAll() {
        // Đánh dấu đang shutdown để ngăn operations mới
        isShuttingDown = true;

        // Shutdown batch processor trước để ngăn processing mới
        batchProcessor.shutdown();

        // Xử lý tất cả operations còn lại một cách đồng bộ
        int remainingOps = operationQueue.size();
        if (remainingOps > 0) {
            Storage.getStorage().getLogger().info("Đang xử lý " + remainingOps + " operations còn lại...");

            // Xử lý từng operation một cách đồng bộ để tránh lỗi connection
            while (!operationQueue.isEmpty()) {
                DatabaseOperation operation = operationQueue.poll();
                if (operation != null) {
                    try {
                        executeOperationDirect(operation);
                    } catch (Exception e) {
                        // Bỏ qua lỗi khi shutdown để tránh spam log
                    }
                }
            }
        }

        // Đợi batch processor shutdown
        try {
            if (!batchProcessor.awaitTermination(10, TimeUnit.SECONDS)) {
                batchProcessor.shutdownNow();
            }
        } catch (InterruptedException e) {
            batchProcessor.shutdownNow();
            Thread.currentThread().interrupt();
        }

        Storage.getStorage().getLogger().info("DatabaseSynchronizer đã shutdown. Đã xử lý " +
                processedOperations.get() + " operations");
    }

    /**
     * Lấy số operations đang chờ xử lý
     */
    public int getPendingOperations() {
        return pendingOperations.get();
    }

    /**
     * Lấy tổng số operations đã xử lý
     */
    public int getProcessedOperations() {
        return processedOperations.get();
    }

    /**
     * Kiểm tra xem synchronizer có đang hoạt động không
     */
    public boolean isEnabled() {
        return enabled && !isShuttingDown;
    }
}
