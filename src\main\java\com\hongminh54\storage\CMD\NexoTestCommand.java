package com.hongminh54.storage.CMD;

import com.hongminh54.storage.API.CMDBase;
import com.hongminh54.storage.Utils.Chat;
import com.hongminh54.storage.nexo.NexoIntegration;
import com.hongminh54.storage.nexo.NexoItem;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Command để test Nexo integration
 * Chỉ dành cho debug và testing
 *
 * <AUTHOR>
 */
public class NexoTestCommand extends CMDBase {

    public NexoTestCommand() {
        super("nexotest");
    }

    @Override
    public void execute(CommandSender sender, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(Chat.colorize("&cChỉ người chơi mới có thể sử dụng lệnh này!"));
            return;
        }

        Player player = (Player) sender;

        if (args.length == 0) {
            sendHelp(player);
            return;
        }

        switch (args[0].toLowerCase()) {
            case "status":
                checkNexoStatus(player);
                break;
            case "create":
                if (args.length < 2) {
                    player.sendMessage(Chat.colorize("&cSử dụng: /nexotest create <nexo_item_id>"));
                    return;
                }
                createNexoItem(player, args[1]);
                break;
            case "check":
                checkItemInHand(player);
                break;
            case "info":
                if (args.length < 2) {
                    player.sendMessage(Chat.colorize("&cSử dụng: /nexotest info <nexo_item_id>"));
                    return;
                }
                getItemInfo(player, args[1]);
                break;
            default:
                sendHelp(player);
                break;
        }
    }

    private void sendHelp(Player player) {
        player.sendMessage(Chat.colorize("&e&l=== Nexo Test Commands ==="));
        player.sendMessage(Chat.colorize("&a/nexotest status &7- Kiểm tra trạng thái Nexo"));
        player.sendMessage(Chat.colorize("&a/nexotest create <id> &7- Tạo Nexo item"));
        player.sendMessage(Chat.colorize("&a/nexotest check &7- Kiểm tra item trong tay"));
        player.sendMessage(Chat.colorize("&a/nexotest info <id> &7- Thông tin về Nexo item"));
    }

    private void checkNexoStatus(Player player) {
        player.sendMessage(Chat.colorize("&e&l=== Nexo Integration Status ==="));

        boolean available = NexoIntegration.isNexoAvailable();
        if (available) {
            player.sendMessage(Chat.colorize("&a✓ Nexo plugin: &fĐã kích hoạt"));
            player.sendMessage(Chat.colorize("&a✓ Integration: &fHoạt động bình thường"));
        } else {
            player.sendMessage(Chat.colorize("&c✗ Nexo plugin: &fKhông có hoặc chưa kích hoạt"));
            player.sendMessage(Chat.colorize("&c✗ Integration: &fKhông khả dụng"));
        }

        player.sendMessage(Chat.colorize("&7Fallback: &fCustom model data thông thường"));
    }

    private void createNexoItem(Player player, String nexoItemId) {
        player.sendMessage(Chat.colorize("&eĐang thử tạo Nexo item: &f" + nexoItemId));

        // Thử tạo với NexoIntegration
        ItemStack nexoItem = NexoIntegration.createNexoItem(nexoItemId);
        if (nexoItem != null) {
            player.getInventory().addItem(nexoItem);
            player.sendMessage(Chat.colorize("&a✓ Đã tạo Nexo item thành công!"));
            return;
        }

        // Thử tạo với fallback
        ItemStack fallbackItem = NexoIntegration.createItemWithNexoSupport(nexoItemId, 12345);
        if (fallbackItem != null) {
            player.getInventory().addItem(fallbackItem);
            player.sendMessage(Chat.colorize("&6⚠ Nexo item không tồn tại, đã tạo fallback item"));
        } else {
            player.sendMessage(Chat.colorize("&c✗ Không thể tạo item"));
        }
    }

    private void checkItemInHand(Player player) {
        ItemStack itemInHand = player.getInventory().getItemInMainHand();
        if (itemInHand == null || itemInHand.getType().name().equals("AIR")) {
            player.sendMessage(Chat.colorize("&cBạn không cầm item nào trong tay!"));
            return;
        }

        player.sendMessage(Chat.colorize("&e&l=== Item Analysis ==="));

        // Kiểm tra xem có phải Nexo item không
        boolean isNexo = NexoIntegration.isNexoItem(itemInHand);
        if (isNexo) {
            String nexoId = NexoIntegration.getNexoItemId(itemInHand);
            player.sendMessage(Chat.colorize("&a✓ Đây là Nexo item"));
            player.sendMessage(Chat.colorize("&fNexo ID: &e" + nexoId));
        } else {
            player.sendMessage(Chat.colorize("&7Đây không phải Nexo item"));
        }

        // Thông tin chung về item
        String itemInfo = NexoItem.getItemInfo(itemInHand);
        player.sendMessage(Chat.colorize("&fThông tin: &7" + itemInfo));
        player.sendMessage(Chat.colorize("&fMaterial: &7" + itemInHand.getType().name()));
        player.sendMessage(Chat.colorize("&fAmount: &7" + itemInHand.getAmount()));
    }

    private void getItemInfo(Player player, String nexoItemId) {
        player.sendMessage(Chat.colorize("&eThông tin về Nexo item: &f" + nexoItemId));

        if (!NexoIntegration.isNexoAvailable()) {
            player.sendMessage(Chat.colorize("&c✗ Nexo không khả dụng"));
            return;
        }

        ItemStack item = NexoIntegration.createNexoItem(nexoItemId);
        if (item != null) {
            player.sendMessage(Chat.colorize("&a✓ Item tồn tại"));
            player.sendMessage(Chat.colorize("&fMaterial: &7" + item.getType().name()));
            if (item.hasItemMeta() && item.getItemMeta().hasDisplayName()) {
                player.sendMessage(Chat.colorize("&fDisplay Name: &7" + item.getItemMeta().getDisplayName()));
            }
        } else {
            player.sendMessage(Chat.colorize("&c✗ Item không tồn tại hoặc không thể tạo"));
        }
    }

    @Override
    public List<String> TabComplete(CommandSender sender, String[] args) {
        List<String> completions = new ArrayList<>();

        if (args.length == 1) {
            completions.addAll(Arrays.asList("status", "create", "check", "info"));
        } else if (args.length == 2 && (args[0].equalsIgnoreCase("create") || args[0].equalsIgnoreCase("info"))) {
            // Có thể thêm tab completion cho Nexo item IDs nếu cần
            completions.add("nexo_example_item");
        }

        return completions;
    }
}
