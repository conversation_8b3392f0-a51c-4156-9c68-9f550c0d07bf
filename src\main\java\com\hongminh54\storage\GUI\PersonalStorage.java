package com.hongminh54.storage.GUI;

import com.hongminh54.storage.Database.PlayerData;
import com.hongminh54.storage.GUI.manager.IGUI;
import com.hongminh54.storage.GUI.manager.InteractiveItem;
import com.hongminh54.storage.Manager.ItemManager;
import com.hongminh54.storage.Manager.MineManager;
import com.hongminh54.storage.Manager.SoundManager;
import com.hongminh54.storage.Storage;
import com.hongminh54.storage.Utils.Chat;
import com.hongminh54.storage.Utils.File;
import com.hongminh54.storage.Utils.GUIText;
import com.hongminh54.storage.Utils.Number;
import com.hongminh54.storage.compatibility.MaterialCompatibility;
import org.bukkit.Bukkit;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

public class PersonalStorage implements IGUI {

    private final Player p;
    private final FileConfiguration config;

    public PersonalStorage(Player p) {
        this.p = p;
        config = File.getGUIStorage();

        // Validate config
        if (!validateConfig()) {
            Storage.getStorage().getLogger().warning("Config storage.yml có vấn đề cho PersonalStorage");
        }

        // Phát âm thanh mở GUI
        playOpenSound();
    }

    @NotNull
    @Override
    public Inventory getInventory() {
        Inventory inventory = Bukkit.createInventory(p, config.getInt("size") * 9, GUIText.format(Objects.requireNonNull(config.getString("title")).replace("#player#", p.getName())));
        for (String item_tag : Objects.requireNonNull(config.getConfigurationSection("items")).getKeys(false)) {
            String slot = Objects.requireNonNull(config.getString("items." + item_tag + ".slot")).replace(" ", "");
            if (item_tag.equalsIgnoreCase("storage_item")) {
                if (slot.contains(",")) {
                    List<String> slot_list = new ArrayList<>(Arrays.asList(slot.split(",")));
                    List<String> item_list = new ArrayList<>(MineManager.getPluginBlocks());
                    for (int i = 0; i < item_list.size(); i++) {
                        String material = MineManager.getMaterial(item_list.get(i));
                        String name = File.getConfig().getString("items." + item_list.get(i));
                        ItemStack itemStack = ItemManager.getItemConfig(p, material, name != null ? name : item_list.get(i).split(";")[0], config.getConfigurationSection("items.storage_item"));
                        InteractiveItem interactiveItem = new InteractiveItem(itemStack, Number.getInteger(slot_list.get(i))).onClick((player, clickType) -> player.openInventory(new ItemStorage(p, material).getInventory()));
                        inventory.setItem(interactiveItem.getSlot(), interactiveItem);
                    }
                }
            } else if (item_tag.equalsIgnoreCase("toggle_item")) {
                if (slot.contains(",")) {
                    for (String slot_string : slot.split(",")) {
                        InteractiveItem item = new InteractiveItem(ItemManager.getItemConfig(p, Objects.requireNonNull(config.getConfigurationSection("items." + item_tag))), Number.getInteger(slot_string)).onClick((player, clickType) -> {
                            // Lấy trạng thái hiện tại và đảo ngược
                            boolean newToggleState = !MineManager.toggle.getOrDefault(p, true);

                            // Cập nhật vào HashMap
                            MineManager.toggle.put(p, newToggleState);

                            // Hiển thị thông báo 
                            p.sendMessage(Chat.colorize(Objects.requireNonNull(File.getMessage().getString("user.status.toggle")).replace("#status#", ItemManager.getStatus(p))));

                            // Mở lại giao diện
                            p.openInventory(this.getInventory());

                            // Lưu vào database bất đồng bộ
                            Bukkit.getScheduler().runTaskAsynchronously(Storage.getStorage(), () -> {
                                try {
                                    PlayerData playerData = Storage.db.getData(p.getName());
                                    if (playerData != null) {
                                        playerData.setAutoPickup(newToggleState);
                                        Storage.db.updateTable(playerData);
                                        Storage.getStorage().getLogger().info("Đã cập nhật trạng thái auto-pickup cho " + p.getName() + ": " + newToggleState);
                                    }
                                } catch (Exception e) {
                                    Storage.getStorage().getLogger().severe("Lỗi khi cập nhật trạng thái auto-pickup: " + e.getMessage());
                                    e.printStackTrace();
                                }
                            });
                        });
                        inventory.setItem(item.getSlot(), item);
                    }
                } else {
                    InteractiveItem item = new InteractiveItem(ItemManager.getItemConfig(p, Objects.requireNonNull(config.getConfigurationSection("items." + item_tag))), Number.getInteger(slot)).onClick((player, clickType) -> {
                        // Lấy trạng thái hiện tại và đảo ngược
                        boolean newToggleState = !MineManager.toggle.getOrDefault(p, true);

                        // Cập nhật vào HashMap
                        MineManager.toggle.put(p, newToggleState);

                        // Hiển thị thông báo 
                        p.sendMessage(Chat.colorize(Objects.requireNonNull(File.getMessage().getString("user.status.toggle")).replace("#status#", ItemManager.getStatus(p))));

                        // Mở lại giao diện
                        p.openInventory(this.getInventory());

                        // Lưu vào database bất đồng bộ
                        Bukkit.getScheduler().runTaskAsynchronously(Storage.getStorage(), () -> {
                            try {
                                PlayerData playerData = Storage.db.getData(p.getName());
                                if (playerData != null) {
                                    playerData.setAutoPickup(newToggleState);
                                    Storage.db.updateTable(playerData);
                                    Storage.getStorage().getLogger().info("Đã cập nhật trạng thái auto-pickup cho " + p.getName() + ": " + newToggleState);
                                }
                            } catch (Exception e) {
                                Storage.getStorage().getLogger().severe("Lỗi khi cập nhật trạng thái auto-pickup: " + e.getMessage());
                                e.printStackTrace();
                            }
                        });
                    });
                    inventory.setItem(item.getSlot(), item);
                }
            } else {
                if (slot.contains(",")) {
                    for (String slot_string : slot.split(",")) {
                        InteractiveItem item = new InteractiveItem(ItemManager.getItemConfig(Objects.requireNonNull(config.getConfigurationSection("items." + item_tag))), Number.getInteger(slot_string));
                        inventory.setItem(item.getSlot(), item);
                    }
                } else {
                    InteractiveItem item = new InteractiveItem(ItemManager.getItemConfig(Objects.requireNonNull(config.getConfigurationSection("items." + item_tag))), Number.getInteger(slot));
                    inventory.setItem(item.getSlot(), item);
                }
            }
        }
        return inventory;
    }

    public Player getPlayer() {
        return p;
    }

    public FileConfiguration getConfig() {
        return config;
    }

    /**
     * Validate config file
     *
     * @return true nếu config hợp lệ
     */
    private boolean validateConfig() {
        try {
            if (config == null) {
                return false;
            }

            // Kiểm tra các trường bắt buộc
            if (!config.contains("title") || !config.contains("size") || !config.contains("items")) {
                return false;
            }

            // Kiểm tra size hợp lệ
            int size = config.getInt("size", 6);
            return size >= 1 && size <= 6;
        } catch (Exception e) {
            handleError("Error validating config: " + e.getMessage(), false);
            return false;
        }
    }

    /**
     * Phát âm thanh mở GUI từ config
     */
    private void playOpenSound() {
        try {
            String openSound = config.getString("sounds.open", "BLOCK_CHEST_OPEN:0.5:1.0");
            SoundManager.playSoundFromConfig(p, openSound);
        } catch (Exception e) {
            // Bỏ qua lỗi âm thanh để không ảnh hưởng đến GUI
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().fine("Sound error in PersonalStorage: " + e.getMessage());
            }
        }
    }

    /**
     * Xử lý lỗi và hiển thị thông báo
     *
     * @param error        Thông điệp lỗi
     * @param showToPlayer Có hiển thị cho người chơi không
     */
    private void handleError(String error, boolean showToPlayer) {
        Storage.getStorage().getLogger().warning("PersonalStorage Error: " + error);
        if (showToPlayer && p != null && p.isOnline()) {
            try {
                String failSound = config.getString("sounds.error", "ENTITY_VILLAGER_NO:0.5:1.0");
                SoundManager.playSoundFromConfig(p, failSound);
            } catch (Exception e) {
                // Bỏ qua lỗi âm thanh
            }
        }
    }

    /**
     * Tạo ItemStack tương thích đa phiên bản
     */
    private org.bukkit.inventory.ItemStack createCompatibleItemStack(org.bukkit.Material material, int amount) {
        try {
            if (MaterialCompatibility.isPre113()) {
                return new org.bukkit.inventory.ItemStack(material, amount, (short) 0);
            } else {
                return new org.bukkit.inventory.ItemStack(material, amount);
            }
        } catch (Exception e) {
            handleError("Error creating compatible ItemStack: " + e.getMessage(), false);
            return new org.bukkit.inventory.ItemStack(org.bukkit.Material.PAPER, amount);
        }
    }
}
