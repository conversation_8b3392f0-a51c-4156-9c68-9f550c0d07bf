package com.hongminh54.storage.Utils;

import com.hongminh54.storage.Storage;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Method;
import java.net.URL;
import java.net.URLClassLoader;
import java.net.URLConnection;
import java.nio.file.Files;
import java.nio.file.StandardCopyOption;
import java.util.logging.Level;

/**
 * Library loader - tải SQLite JDBC khi runtime
 */
public class LibraryLoader {

    private static final String SQLITE_JDBC_VERSION = "3.46.1.3";
    private static final String[] SQLITE_JDBC_URLS = {"https://repo1.maven.org/maven2/org/xerial/sqlite-jdbc/" + SQLITE_JDBC_VERSION + "/sqlite-jdbc-" + SQLITE_JDBC_VERSION + ".jar", "https://central.sonatype.com/artifact/org.xerial/sqlite-jdbc/" + SQLITE_JDBC_VERSION + "/sqlite-jdbc-" + SQLITE_JDBC_VERSION + ".jar", "https://search.maven.org/remotecontent?filepath=org/xerial/sqlite-jdbc/" + SQLITE_JDBC_VERSION + "/sqlite-jdbc-" + SQLITE_JDBC_VERSION + ".jar"};
    private static final Object loadLock = new Object();
    private static volatile boolean loaded = false;

    /**
     * Tải SQLite JDBC library động
     */
    public static void loadSQLiteJDBC() {
        if (loaded) {
            return;
        }

        synchronized (loadLock) {
            if (loaded) {
                return;
            }

            try {
                Class.forName("org.sqlite.JDBC");
                Storage.getStorage().getLogger().info("SQLite JDBC đã có sẵn trong classpath");
                loaded = true;
                return;
            } catch (ClassNotFoundException e) {
                Storage.getStorage().getLogger().info("Đang tải SQLite JDBC library...");
            }

            try {
                File libDir = new File(Storage.getStorage().getDataFolder(), "libs");
                if (!libDir.exists()) {
                    libDir.mkdirs();
                }

                File sqliteJar = new File(libDir, "sqlite-jdbc-" + SQLITE_JDBC_VERSION + ".jar");

                if (!sqliteJar.exists()) {
                    downloadLibrary(sqliteJar);
                }

                addToClasspath(sqliteJar);

                if (testSQLiteConnection()) {
                    Storage.getStorage().getLogger().info("SQLite JDBC library đã được tải thành công!");
                    loaded = true;
                } else {
                    throw new Exception("SQLite JDBC không thể sử dụng");
                }

            } catch (Exception e) {
                Storage.getStorage().getLogger().log(Level.SEVERE, "Không thể tải SQLite JDBC library", e);
                Storage.getStorage().getLogger().warning("Plugin sẽ hoạt động với SQLite có sẵn trong server (nếu có)");
            }
        }
    }

    /**
     * Tải library từ Maven repository với fallback URLs
     */
    private static void downloadLibrary(File targetFile) throws IOException {
        Storage.getStorage().getLogger().info("Đang tải SQLite JDBC từ Maven repository...");

        IOException lastException = null;

        for (String urlString : SQLITE_JDBC_URLS) {
            try {
                URL url = new URL(urlString);
                URLConnection connection = url.openConnection();
                connection.setConnectTimeout(30000);
                connection.setReadTimeout(60000);

                try (InputStream in = connection.getInputStream()) {
                    Files.copy(in, targetFile.toPath(), StandardCopyOption.REPLACE_EXISTING);
                }
                Storage.getStorage().getLogger().info("Đã tải xong: " + targetFile.getName());
                return;
            } catch (IOException e) {
                lastException = e;
                Storage.getStorage().getLogger().warning("Không thể tải từ repository: " + e.getMessage());
            }
        }

        throw new IOException("Không thể tải SQLite JDBC từ bất kỳ repository nào", lastException);
    }

    /**
     * Thêm JAR file vào classpath động
     */
    private static void addToClasspath(File jarFile) throws Exception {
        ClassLoader classLoader = Storage.class.getClassLoader();
        URL jarUrl = jarFile.toURI().toURL();

        // Strategy 1: URLClassLoader (Java 8)
        if (classLoader instanceof URLClassLoader) {
            try {
                URLClassLoader urlClassLoader = (URLClassLoader) classLoader;
                Method addURL = URLClassLoader.class.getDeclaredMethod("addURL", URL.class);
                addURL.setAccessible(true);
                addURL.invoke(urlClassLoader, jarUrl);
                return;
            } catch (Exception e) {
                // Continue to next strategy
            }
        }

        // Strategy 2: PluginClassLoader reflection
        try {
            Method addURL = classLoader.getClass().getDeclaredMethod("addURL", URL.class);
            addURL.setAccessible(true);
            addURL.invoke(classLoader, jarUrl);
            return;
        } catch (Exception e) {
            // Continue to next strategy
        }

        // Strategy 3: System ClassLoader manipulation
        try {
            ClassLoader systemClassLoader = ClassLoader.getSystemClassLoader();
            if (systemClassLoader instanceof URLClassLoader) {
                Method addURL = URLClassLoader.class.getDeclaredMethod("addURL", URL.class);
                addURL.setAccessible(true);
                addURL.invoke(systemClassLoader, jarUrl);
                return;
            }
        } catch (Exception e) {
            // All strategies failed
        }

        throw new Exception("Không thể thêm library vào classpath");
    }

    /**
     * Kiểm tra xem SQLite JDBC đã được load chưa
     */
    public static boolean isSQLiteLoaded() {
        try {
            Class.forName("org.sqlite.JDBC");
            return true;
        } catch (ClassNotFoundException e) {
            return false;
        }
    }

    /**
     * Test thực tế SQLite connection để đảm bảo có thể sử dụng
     */
    private static boolean testSQLiteConnection() {
        try {
            Class.forName("org.sqlite.JDBC");

            java.sql.Connection conn = java.sql.DriverManager.getConnection("jdbc:sqlite::memory:");
            java.sql.Statement stmt = conn.createStatement();
            java.sql.ResultSet rs = stmt.executeQuery("SELECT 1 as test");

            boolean success = rs.next() && rs.getInt("test") == 1;

            if (success) {
                stmt.execute("CREATE TABLE test_table (id INTEGER, name TEXT)");
                stmt.execute("INSERT INTO test_table VALUES (1, 'test')");
                rs = stmt.executeQuery("SELECT COUNT(*) as count FROM test_table");
                success = rs.next() && rs.getInt("count") == 1;
            }

            rs.close();
            stmt.close();
            conn.close();

            return success;

        } catch (Exception e) {
            Storage.getStorage().getLogger().warning("SQLite test failed: " + e.getMessage());
            return false;
        }
    }

    /**
     * Load tất cả libraries cần thiết
     */
    public static void loadAllLibraries() {
        Storage.getStorage().getLogger().info("Đang kiểm tra và tải libraries...");

        loadSQLiteJDBC();

        if (loaded) {
            Storage.getStorage().getLogger().info("SQLite JDBC sẵn sàng sử dụng!");
        } else {
            Storage.getStorage().getLogger().warning("SQLite JDBC không khả dụng - sử dụng fallback");
        }

        Storage.getStorage().getLogger().info("Hoàn thành việc tải libraries");
    }

    /**
     * Test SQLite với database thực tế của plugin
     */
    public static boolean testPluginDatabase() {
        if (!loaded) {
            return false;
        }

        try {
            File dbFile = new File(Storage.getStorage().getDataFolder(), "test_connection.db");
            String connectionUrl = "jdbc:sqlite:" + dbFile.getAbsolutePath();

            java.sql.Connection conn = java.sql.DriverManager.getConnection(connectionUrl);
            java.sql.Statement stmt = conn.createStatement();

            stmt.execute("CREATE TABLE IF NOT EXISTS connection_test (id INTEGER PRIMARY KEY, timestamp TEXT)");
            stmt.execute("INSERT INTO connection_test (timestamp) VALUES ('" + System.currentTimeMillis() + "')");

            java.sql.ResultSet rs = stmt.executeQuery("SELECT COUNT(*) as count FROM connection_test");
            boolean success = rs.next() && rs.getInt("count") > 0;

            rs.close();
            stmt.close();
            conn.close();

            if (dbFile.exists()) {
                dbFile.delete();
            }

            return success;

        } catch (Exception e) {
            Storage.getStorage().getLogger().warning("Plugin database test error: " + e.getMessage());
            return false;
        }
    }

}
