package com.hongminh54.storage.CMD;

import com.hongminh54.storage.API.CMDBase;
import com.hongminh54.storage.CMD.manager.StorageCommandManager;
import com.hongminh54.storage.GUI.PersonalStorage;
import com.hongminh54.storage.Utils.Chat;
import com.hongminh54.storage.Utils.File;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.jetbrains.annotations.NotNull;

import java.util.List;

public class StorageCMD extends CMDBase {
    private final StorageCommandManager commandManager;

    public StorageCMD(String name) {
        super(name);
        this.commandManager = new StorageCommandManager();
    }

    @Override
    public void execute(@NotNull CommandSender c, String[] args) {
        if (args.length == 0) {
            if (c instanceof Player) {
                try {
                    ((Player) c).openInventory(new PersonalStorage((Player) c).getInventory());
                } catch (IndexOutOfBoundsException e) {
                    c.sendMessage(Chat.colorize(File.getMessage().getString("admin.not_enough_slot")));
                }
            }
        } else {
            commandManager.executeCommand(c, args);
        }
    }

    @Override
    public List<String> TabComplete(@NotNull CommandSender sender, String[] args) {
        return commandManager.getTabComplete(sender, args);
    }
}
