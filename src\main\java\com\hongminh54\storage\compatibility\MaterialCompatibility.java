package com.hongminh54.storage.compatibility;

import com.cryptomorin.xseries.XEnchantment;
import com.cryptomorin.xseries.XMaterial;
import com.hongminh54.storage.NMS.NMSAssistant;
import com.hongminh54.storage.Storage;
import org.bukkit.Material;
import org.bukkit.enchantments.Enchantment;
import org.bukkit.inventory.ItemFlag;
import org.bukkit.inventory.ItemStack;
import org.bukkit.potion.PotionEffectType;

import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Lớp tối ưu hỗ trợ tương thích vật liệu giữa các phiên bản Minecraft
 */
public class MaterialCompatibility {

    private static final NMSAssistant nmsAssistant = new NMSAssistant();
    private static final boolean PRE_1_13 = nmsAssistant.isVersionLessThan(13);
    private static final boolean PRE_1_16 = nmsAssistant.isVersionLessThan(16);
    private static final boolean IS_1_20_5_OR_HIGHER = nmsAssistant.is1_20_5OrHigher();
    private static final boolean IS_1_21_4_OR_HIGHER = nmsAssistant.is1_21_4OrHigher();

    // Cache để tối ưu performance với giới hạn kích thước
    private static final ConcurrentHashMap<String, XMaterial> MATERIAL_CACHE = new ConcurrentHashMap<>();
    private static final ConcurrentHashMap<String, ItemStack> ITEMSTACK_CACHE = new ConcurrentHashMap<>();
    private static final int MAX_CACHE_SIZE = 500;

    /**
     * Lấy vật liệu tương ứng dựa trên phiên bản - Tối ưu với XMaterial
     *
     * @param materialName Tên material (hỗ trợ cả legacy và modern)
     * @return Material tương ứng với phiên bản hiện tại
     */
    public static Material getMaterial(String materialName) {
        if (materialName == null || materialName.isEmpty()) {
            return Material.PAPER;
        }

        // Sử dụng cache để tối ưu performance
        XMaterial cached = MATERIAL_CACHE.get(materialName.toUpperCase());
        if (cached != null) {
            Material result = cached.parseMaterial();
            return result != null ? result : Material.PAPER;
        }

        // Tìm với XMaterial
        Optional<XMaterial> xMaterial = XMaterial.matchXMaterial(materialName);
        if (xMaterial.isPresent()) {
            // Kiểm tra cache size trước khi thêm
            if (MATERIAL_CACHE.size() >= MAX_CACHE_SIZE) {
                MATERIAL_CACHE.clear(); // Clear cache khi đầy để tránh memory leak
            }
            MATERIAL_CACHE.put(materialName.toUpperCase(), xMaterial.get());
            Material result = xMaterial.get().parseMaterial();
            return result != null ? result : Material.PAPER;
        }

        return Material.PAPER; // Fallback an toàn
    }


    /**
     * Lấy vật liệu glass pane - Tối ưu với XMaterial
     *
     * @param color Màu sắc
     * @return Material tương ứng
     */
    public static Material getStainedGlassPane(String color) {
        return getMaterial(color + "_STAINED_GLASS_PANE");
    }

    /**
     * Lấy vật liệu glass - Tối ưu với XMaterial
     *
     * @param color Màu sắc
     * @return Material tương ứng
     */
    public static Material getStainedGlass(String color) {
        return getMaterial(color + "_STAINED_GLASS");
    }

    /**
     * Lấy vật liệu theo tên hiện đại, nếu không có sẽ sử dụng fallback
     *
     * @param modernMaterial Tên vật liệu ở phiên bản mới
     * @param fallback       Vật liệu mặc định nếu không tìm thấy
     * @return Material tương ứng hoặc fallback
     */
    public static Material getMaterialWithFallback(String modernMaterial, Material fallback) {
        try {
            return Material.valueOf(modernMaterial);
        } catch (IllegalArgumentException e) {
            return fallback;
        }
    }

    /**
     * Tạo ItemStack với material tương thích
     *
     * @param modernMaterial Tên material phiên bản mới
     * @param legacyMaterial Tên material phiên bản cũ
     * @param amount         Số lượng
     * @return ItemStack tương thích
     */
    public static ItemStack createItemStack(String modernMaterial, String legacyMaterial, int amount) {
        String materialName = PRE_1_13 ? legacyMaterial : modernMaterial;
        Material material = getMaterial(materialName);
        return new ItemStack(material, amount);
    }

    /**
     * Tạo ItemStack với XMaterial - Tối ưu với cache
     *
     * @param xMaterial XMaterial enum
     * @param amount    Số lượng
     * @return ItemStack tương thích
     */
    public static ItemStack createItemStackWithXMaterial(XMaterial xMaterial, int amount) {
        if (xMaterial == null) {
            return new ItemStack(Material.PAPER, amount);
        }

        try {
            ItemStack item = xMaterial.parseItem();
            if (item != null) {
                item.setAmount(amount);
                return item;
            }
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể tạo ItemStack với XMaterial: " + xMaterial);
            }
        }
        return new ItemStack(Material.PAPER, amount);
    }

    /**
     * Kiểm tra xem material có tồn tại không
     *
     * @param materialName Tên material
     * @return true nếu tồn tại
     */
    public static boolean materialExists(String materialName) {
        try {
            Material.valueOf(materialName);
            return true;
        } catch (IllegalArgumentException e) {
            return false;
        }
    }

    /**
     * Lấy wool material - Tối ưu với XMaterial
     *
     * @param color Màu sắc
     * @return Material wool tương ứng
     */
    public static Material getWool(String color) {
        return getMaterial(color + "_WOOL");
    }

    /**
     * Lấy concrete material - Tối ưu với XMaterial
     *
     * @param color Màu sắc
     * @return Material concrete tương ứng (fallback wool cho 1.12.2)
     */
    public static Material getConcrete(String color) {
        Material concrete = getMaterial(color + "_CONCRETE");
        // Nếu không tìm thấy concrete (1.12.2), fallback wool
        return concrete != Material.PAPER ? concrete : getMaterial(color + "_WOOL");
    }

    /**
     * Lấy terracotta material - Tối ưu với XMaterial
     *
     * @param color Màu sắc
     * @return Material terracotta tương ứng
     */
    public static Material getTerracotta(String color) {
        return getMaterial(color + "_TERRACOTTA");
    }

    /**
     * Lấy dye material - Tối ưu với XMaterial
     *
     * @param color Màu sắc
     * @return Material dye tương ứng
     */
    public static Material getDye(String color) {
        return getMaterial(color + "_DYE");
    }

    /**
     * Lấy bed material - Tối ưu với XMaterial
     *
     * @param color Màu sắc
     * @return Material bed tương ứng
     */
    public static Material getBed(String color) {
        return getMaterial(color + "_BED");
    }

    /**
     * Lấy banner material - Tối ưu với XMaterial
     *
     * @param color Màu sắc
     * @return Material banner tương ứng
     */
    public static Material getBanner(String color) {
        return getMaterial(color + "_BANNER");
    }

    /**
     * Lấy carpet material - Tối ưu với XMaterial
     *
     * @param color Màu sắc
     * @return Material carpet tương ứng
     */
    public static Material getCarpet(String color) {
        return getMaterial(color + "_CARPET");
    }

    /**
     * Lấy âm thanh tương thích với phiên bản hiện tại
     * Hỗ trợ fallback cho các âm thanh không tồn tại trong phiên bản cũ
     *
     * @param modernSound   Tên âm thanh phiên bản mới (1.13+)
     * @param legacySound   Tên âm thanh phiên bản cũ (1.12.2)
     * @param fallbackSound Âm thanh fallback an toàn
     * @return Tên âm thanh tương thích
     */
    public static String getCompatibleSound(String modernSound, String legacySound, String fallbackSound) {
        if (PRE_1_13) {
            // Sử dụng âm thanh phiên bản cũ hoặc fallback
            return legacySound != null ? legacySound : fallbackSound;
        } else {
            // Sử dụng âm thanh phiên bản mới
            return modernSound;
        }
    }

    /**
     * Lấy potion effect type tương thích
     *
     * @param modernName Tên effect phiên bản mới
     * @param legacyName Tên effect phiên bản cũ
     * @return PotionEffectType tương thích
     */
    public static PotionEffectType getCompatiblePotionEffect(String modernName, String legacyName) {
        try {
            if (PRE_1_13 && legacyName != null) {
                return PotionEffectType.getByName(legacyName);
            } else {
                return PotionEffectType.getByName(modernName);
            }
        } catch (Exception e) {
            // Thử với tên khác nếu không tìm thấy
            try {
                return PotionEffectType.getByName(PRE_1_13 ? modernName : legacyName);
            } catch (Exception ex) {
                if (Storage.getStorage().isDebug()) {
                    Storage.getStorage().getLogger().warning("Không tìm thấy PotionEffectType: " + modernName + "/" + legacyName);
                }
                return null;
            }
        }
    }

    /**
     * Lấy material tương thích giữa các phiên bản - Tối ưu với XMaterial
     *
     * @param materialName Tên material gốc
     * @return Tên material tương thích
     */
    public static String getCompatibleMaterial(String materialName) {
        if (materialName == null || materialName.isEmpty()) {
            return "PAPER";
        }

        Optional<XMaterial> xMaterial = XMaterial.matchXMaterial(materialName);
        if (xMaterial.isPresent()) {
            return xMaterial.get().name();
        }

        return materialName;
    }

    /**
     * Lấy Material enum một cách an toàn - Tối ưu với XMaterial
     *
     * @param materialName Tên material
     * @return Material enum hoặc null nếu không tìm thấy
     */
    public static Material getMaterialSafely(String materialName) {
        if (materialName == null || materialName.isEmpty()) {
            return null;
        }

        // Sử dụng cache để tối ưu performance
        XMaterial cached = MATERIAL_CACHE.get(materialName.toUpperCase());
        if (cached != null) {
            return cached.parseMaterial();
        }

        // Tìm với XMaterial
        Optional<XMaterial> xMaterial = XMaterial.matchXMaterial(materialName);
        if (xMaterial.isPresent()) {
            // Kiểm tra cache size trước khi thêm
            if (MATERIAL_CACHE.size() >= MAX_CACHE_SIZE) {
                MATERIAL_CACHE.clear();
            }
            MATERIAL_CACHE.put(materialName.toUpperCase(), xMaterial.get());
            return xMaterial.get().parseMaterial();
        }

        if (Storage.getStorage().isDebug()) {
            Storage.getStorage().getLogger().warning("Không tìm thấy Material: " + materialName);
        }
        return null;
    }


    /**
     * Lấy item fallback thông minh thay vì STONE
     *
     * @param originalMaterial Material gốc
     * @return ItemStack fallback phù hợp
     */
    public static ItemStack getSmartFallbackItem(String originalMaterial) {
        if (originalMaterial == null || originalMaterial.isEmpty()) {
            return new ItemStack(Material.PAPER);
        }

        String upperMaterial = originalMaterial.toUpperCase();

        // Xử lý format "MATERIAL;DATA" trước tiên
        if (upperMaterial.contains(";")) {
            try {
                String[] parts = upperMaterial.split(";");
                String materialName = parts[0];
                byte data = parts.length > 1 ? Byte.parseByte(parts[1]) : 0;

                // Thử tạo ItemStack với material và data
                Material mat = Material.valueOf(materialName);
                if (PRE_1_13) {
                    return new ItemStack(mat, 1, data);
                } else {
                    // Phiên bản 1.13+ bỏ qua data value
                    return new ItemStack(mat, 1);
                }
            } catch (Exception e) {
                // Nếu không thành công, thử với tên material không có data
                try {
                    String materialName = upperMaterial.split(";")[0];
                    Material mat = Material.valueOf(materialName);
                    return new ItemStack(mat, 1);
                } catch (Exception ex) {
                    // Tiếp tục với logic fallback khác
                }
            }
        }

        // Thử tạo với XMaterial trước
        try {
            Optional<XMaterial> xMaterial = XMaterial.matchXMaterial(upperMaterial);
            if (xMaterial.isPresent()) {
                ItemStack item = xMaterial.get().parseItem();
                if (item != null) {
                    return item;
                }
            }
        } catch (Exception e) {
            // Tiếp tục với logic fallback khác
        }

        // Thử với Material.valueOf trực tiếp để tránh infinite recursion
        try {
            String materialName = upperMaterial.contains(";") ? upperMaterial.split(";")[0] : upperMaterial;
            Material compatibleMaterial = Material.valueOf(materialName);
            if (compatibleMaterial != null) {
                return new ItemStack(compatibleMaterial);
            }
        } catch (Exception e) {
            // Tiếp tục với logic fallback khác
        }

        // Fallback dựa trên loại material
        if (isMaterialType(upperMaterial, "HEAD", "SKULL")) {
            return createFallbackHead();
        } else if (isMaterialType(upperMaterial, "SIGN")) {
            return createFallbackSign();
        } else if (isMaterialType(upperMaterial, "GLASS_PANE", "STAINED_GLASS_PANE")) {
            return createFallbackGlassPane();
        } else if (isMaterialType(upperMaterial, "WOOL")) {
            return createFallbackWool();
        } else if (isMaterialType(upperMaterial, "TERRACOTTA", "STAINED_CLAY")) {
            return createFallbackTerracotta();
        } else if (isMaterialType(upperMaterial, "PLANKS", "WOOD")) {
            return createFallbackWood();
        } else if (isMaterialType(upperMaterial, "FLOWER", "PLANT", "SUNFLOWER", "LILAC", "ROSE")) {
            return createFallbackFlower();
        } else {
            // Fallback cuối cùng - chỉ log warning cho material không phổ biến
            String materialName = upperMaterial.contains(";") ? upperMaterial.split(";")[0] : upperMaterial;
            if (!isCommonMaterial(materialName)) {
                Storage.getStorage().getLogger().warning("Không tìm thấy fallback cho material: " + originalMaterial + ", sử dụng PAPER");
            }
            return new ItemStack(Material.PAPER);
        }
    }

    /**
     * Kiểm tra material có thuộc loại nào đó không
     */
    public static boolean isMaterialType(String material, String... types) {
        for (String type : types) {
            if (material.contains(type)) {
                return true;
            }
        }
        return false;
    }

    /**
     * Kiểm tra xem material có phải là material phổ biến không
     *
     * @param materialName Tên material
     * @return true nếu là material phổ biến
     */
    public static boolean isCommonMaterial(String materialName) {
        // Danh sách các material phổ biến trong plugin tài nguyên (từ config.yml)
        String[] commonMaterials = {
                // Các material chính từ config.yml
                "COBBLESTONE", "COAL", "COAL_BLOCK", "IRON_INGOT", "IRON_BLOCK",
                "GOLD_INGOT", "GOLD_BLOCK", "REDSTONE", "REDSTONE_BLOCK",
                "LAPIS_LAZULI", "LAPIS_BLOCK", "DIAMOND", "DIAMOND_BLOCK",
                "EMERALD", "EMERALD_BLOCK",
                // Các ore blocks
                "STONE", "COAL_ORE", "IRON_ORE", "GOLD_ORE", "REDSTONE_ORE",
                "LAPIS_ORE", "DIAMOND_ORE", "EMERALD_ORE",
                // Các material phổ biến khác
                "DIRT", "SAND", "GRAVEL", "NETHERRACK", "OBSIDIAN", "BEDROCK",
                "GLOWSTONE", "NETHER_QUARTZ_ORE", "QUARTZ"
        };

        for (String common : commonMaterials) {
            if (materialName.equals(common)) {
                return true;
            }
        }
        return false;
    }

    /**
     * Tạo fallback cho đầu
     */
    public static ItemStack createFallbackHead() {
        if (PRE_1_13) {
            return new ItemStack(Material.valueOf("SKULL_ITEM"), 1, (byte) 3);
        } else {
            try {
                return new ItemStack(Material.valueOf("PLAYER_HEAD"));
            } catch (IllegalArgumentException e) {
                return new ItemStack(Material.valueOf("SKULL_ITEM"), 1, (byte) 3);
            }
        }
    }

    /**
     * Tạo fallback cho biển
     */
    public static ItemStack createFallbackSign() {
        if (PRE_1_13) {
            return new ItemStack(Material.valueOf("SIGN"));
        } else {
            return new ItemStack(Material.valueOf("OAK_SIGN"));
        }
    }

    /**
     * Tạo fallback cho kính
     */
    public static ItemStack createFallbackGlassPane() {
        if (PRE_1_13) {
            return new ItemStack(Material.valueOf("STAINED_GLASS_PANE"), 1, (byte) 7); // Gray
        } else {
            return new ItemStack(Material.valueOf("GRAY_STAINED_GLASS_PANE"));
        }
    }

    /**
     * Tạo fallback cho len
     */
    public static ItemStack createFallbackWool() {
        if (PRE_1_13) {
            return new ItemStack(Material.valueOf("WOOL"), 1, (byte) 0); // White
        } else {
            return new ItemStack(Material.valueOf("WHITE_WOOL"));
        }
    }

    /**
     * Tạo fallback cho đất sét nung
     */
    public static ItemStack createFallbackTerracotta() {
        if (PRE_1_13) {
            return new ItemStack(Material.valueOf("STAINED_CLAY"), 1, (byte) 0); // White
        } else {
            return new ItemStack(Material.valueOf("WHITE_TERRACOTTA"));
        }
    }

    /**
     * Tạo fallback cho gỗ
     */
    public static ItemStack createFallbackWood() {
        if (PRE_1_13) {
            return new ItemStack(Material.valueOf("WOOD"), 1, (byte) 0); // Oak
        } else {
            return new ItemStack(Material.valueOf("OAK_PLANKS"));
        }
    }

    /**
     * Tạo fallback cho hoa
     */
    public static ItemStack createFallbackFlower() {
        if (PRE_1_13) {
            return new ItemStack(Material.valueOf("DOUBLE_PLANT"), 1, (byte) 0); // Sunflower
        } else {
            return new ItemStack(Material.valueOf("SUNFLOWER"));
        }
    }

    /**
     * Tạo ItemStack tương thích từ cấu hình material - Tối ưu với cache
     *
     * @param materialConfig Cấu hình material (hỗ trợ MATERIAL:DATA format)
     * @return ItemStack tương thích
     */
    public static ItemStack createCompatibleItemStack(String materialConfig) {
        if (materialConfig == null || materialConfig.isEmpty()) {
            return new ItemStack(Material.PAPER);
        }

        // Sử dụng cache để tối ưu performance
        ItemStack cached = ITEMSTACK_CACHE.get(materialConfig);
        if (cached != null) {
            return cached.clone();
        }

        ItemStack result = createItemStackInternal(materialConfig);

        // Cache kết quả nếu thành công
        if (result != null && result.getType() != Material.PAPER) {
            ITEMSTACK_CACHE.put(materialConfig, result.clone());
        }

        return result;
    }

    /**
     * Tạo ItemStack internal logic
     */
    private static ItemStack createItemStackInternal(String materialConfig) {
        try {
            // Thử với XMaterial trước
            Optional<XMaterial> xMaterial = XMaterial.matchXMaterial(materialConfig);
            if (xMaterial.isPresent()) {
                ItemStack item = xMaterial.get().parseItem();
                if (item != null) {
                    return item;
                }
            }
        } catch (Exception e) {
            // Tiếp tục với logic khác
        }

        // Fallback với smart fallback
        return getSmartFallbackItem(materialConfig);
    }

    /**
     * Lấy enchantment tương thích đa phiên bản
     *
     * @param modernName Tên enchantment phiên bản mới (1.20.5+)
     * @param legacyName Tên enchantment phiên bản cũ (trước 1.20.5)
     * @return Enchantment tương thích
     */
    public static Enchantment getCompatibleEnchantment(String modernName, String legacyName) {
        try {
            // Thử với XEnchantment trước
            Optional<XEnchantment> xEnchant = XEnchantment.matchXEnchantment(modernName);
            if (xEnchant.isPresent() && xEnchant.get().getEnchant() != null) {
                return xEnchant.get().getEnchant();
            }

            // Thử với legacy name
            xEnchant = XEnchantment.matchXEnchantment(legacyName);
            if (xEnchant.isPresent() && xEnchant.get().getEnchant() != null) {
                return xEnchant.get().getEnchant();
            }

            // Fallback cuối cùng
            return Enchantment.UNBREAKING; // Safe fallback
        } catch (Exception e) {
            Storage.getStorage().getLogger().warning("Không thể tìm enchantment: " + modernName + "/" + legacyName);
            return Enchantment.UNBREAKING;
        }
    }

    /**
     * Lấy ItemFlag tương thích đa phiên bản
     *
     * @param modernName Tên flag phiên bản mới (1.20.5+)
     * @param legacyName Tên flag phiên bản cũ (trước 1.20.5)
     * @return ItemFlag tương thích
     */
    public static ItemFlag getCompatibleItemFlag(String modernName, String legacyName) {
        try {
            // Thử với tên mới trước
            try {
                return ItemFlag.valueOf(modernName);
            } catch (IllegalArgumentException e) {
                // Thử với tên cũ
                return ItemFlag.valueOf(legacyName);
            }
        } catch (Exception e) {
            Storage.getStorage().getLogger().warning("Không thể tìm item flag: " + modernName + "/" + legacyName);
            return ItemFlag.HIDE_ATTRIBUTES; // Safe fallback
        }
    }

    /**
     * Áp dụng potion effect một cách an toàn
     *
     * @param player     Player nhận effect
     * @param effectType PotionEffectType
     * @param duration   Thời gian (ticks)
     * @param amplifier  Cấp độ effect (0 = level 1)
     * @param ambient    Có phải ambient effect không
     * @param particles  Hiển thị particles không
     * @return true nếu áp dụng thành công
     */
    public static boolean applyPotionEffect(org.bukkit.entity.Player player, PotionEffectType effectType,
                                            int duration, int amplifier, boolean ambient, boolean particles) {
        if (player == null || effectType == null) {
            return false;
        }

        try {
            org.bukkit.potion.PotionEffect effect;
            if (PRE_1_13) {
                // Phiên bản 1.12.2: không có icon parameter
                effect = new org.bukkit.potion.PotionEffect(effectType, duration, amplifier, ambient, particles);
            } else {
                // Phiên bản 1.13+: có icon parameter
                try {
                    effect = new org.bukkit.potion.PotionEffect(effectType, duration, amplifier, ambient, particles, true);
                } catch (Exception e) {
                    // Fallback nếu constructor mới không hoạt động
                    effect = new org.bukkit.potion.PotionEffect(effectType, duration, amplifier, ambient, particles);
                }
            }

            return player.addPotionEffect(effect);
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể áp dụng potion effect: " + e.getMessage());
            }
            return false;
        }
    }

    /**
     * Remove potion effect một cách an toàn
     *
     * @param player     Player cần remove effect
     * @param effectType PotionEffectType cần remove
     * @return true nếu remove thành công
     */
    public static boolean removePotionEffect(org.bukkit.entity.Player player, PotionEffectType effectType) {
        if (player == null || effectType == null) {
            return false;
        }

        try {
            player.removePotionEffect(effectType);
            return true;
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể remove potion effect: " + e.getMessage());
            }
            return false;
        }
    }

    /**
     * Kiểm tra player có effect không
     *
     * @param player     Player cần kiểm tra
     * @param effectType PotionEffectType cần kiểm tra
     * @return true nếu player có effect này
     */
    public static boolean hasEffect(org.bukkit.entity.Player player, PotionEffectType effectType) {
        if (player == null || effectType == null) {
            return false;
        }

        try {
            return player.hasPotionEffect(effectType);
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * Kiểm tra xem có phải phiên bản 1.12.2 không
     *
     * @return true nếu là phiên bản 1.12.2
     */
    public static boolean isPre113() {
        return PRE_1_13;
    }

    /**
     * Xóa cache để tối ưu bộ nhớ
     */
    public static void clearCache() {
        MATERIAL_CACHE.clear();
        ITEMSTACK_CACHE.clear();
    }

    /**
     * Lấy thông tin cache hiện tại (debug)
     *
     * @return Thông tin cache
     */
    public static String getCacheInfo() {
        return String.format("Material Cache: %d items, ItemStack Cache: %d items",
                MATERIAL_CACHE.size(), ITEMSTACK_CACHE.size());
    }
}
