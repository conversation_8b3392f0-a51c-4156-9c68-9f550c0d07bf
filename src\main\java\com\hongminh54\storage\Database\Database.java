package com.hongminh54.storage.Database;

import com.hongminh54.storage.Storage;
import com.hongminh54.storage.Utils.File;
import org.jetbrains.annotations.NotNull;

import java.sql.*;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.logging.Level;


public abstract class Database {
    // Connection pool để tối ưu hiệu suất kết nối - Thread-safe implementation
    private final ConcurrentLinkedQueue<Connection> connectionPool = new ConcurrentLinkedQueue<>();
    private final Object poolLock = new Object(); // Lock cho thread safety
    private final AtomicInteger activeConnections = new AtomicInteger(0); // Đếm số kết nối đang hoạt động
    // ThreadLocal connection để đảm bảo mỗi thread có kết nối riêng với SQLite
    private final ThreadLocal<Connection> threadLocalConnection = new ThreadLocal<>();
    private final Set<Connection> allConnections = ConcurrentHashMap.newKeySet(); // Theo dõi tất cả kết nối
    public String table = "PlayerData";
    Storage main;
    Connection connection;
    private volatile boolean isShuttingDown = false; // Flag để ngăn tạo kết nối mới khi shutdown
    // Database synchronizer cho batch processing
    private DatabaseSynchronizer synchronizer;

    public Database(Storage instance) {
        main = instance;
    }

    // Lấy max pool size từ config
    private int getMaxPoolSize() {
        try {
            return File.getConfig().getInt("database.sqlite.max_pool_size", 5); // Giảm xuống 5 để tránh quá tải
        } catch (Exception e) {
            return 5; // Default fallback - giảm từ 8 xuống 5
        }
    }

    public abstract Connection getSQLConnection();

    public abstract void load();

    public void initialize() {
        connection = getSQLConnection();
        try {
            PreparedStatement ps = connection.prepareStatement("SELECT * FROM " + table + " WHERE player = ?");
            ps.setString(1, "init");
            ResultSet rs = ps.executeQuery();
            close(ps, rs);

            // Khởi tạo database synchronizer
            synchronizer = new DatabaseSynchronizer();
            Storage.getStorage().getLogger().info("Database synchronizer đã được khởi tạo");

        } catch (SQLException ex) {
            Storage.getStorage().getLogger().log(Level.SEVERE, "Unable to retrieve connection", ex);
        }
    }

    /**
     * Lấy kết nối từ pool hoặc tạo mới nếu pool trống - Thread-safe implementation
     * Sử dụng ThreadLocal cho SQLite để tránh xung đột thread
     *
     * @return Kết nối SQL
     */
    public Connection getConnection() {
        if (isShuttingDown) {
            return null; // Không tạo kết nối mới khi đang shutdown
        }

        // Đối với SQLite, sử dụng ThreadLocal connection để đảm bảo thread safety
        if (this instanceof com.hongminh54.storage.Database.SQLite) {
            return getThreadLocalConnection();
        }

        // Đối với MySQL và các database khác, sử dụng connection pool
        synchronized (poolLock) {
            // Thử lấy kết nối từ pool trước
            Connection conn = connectionPool.poll();
            if (conn != null) {
                try {
                    // Kiểm tra kết nối còn hoạt động không với timeout ngắn hơn
                    if (!conn.isClosed() && conn.isValid(1)) {
                        activeConnections.incrementAndGet();
                        return conn;
                    } else {
                        // Kết nối không hợp lệ, đóng và tạo mới
                        safeCloseConnection(conn);
                    }
                } catch (SQLException e) {
                    // Lỗi khi kiểm tra kết nối, đóng và tạo mới
                    safeCloseConnection(conn);
                }
            }

            // Kiểm tra giới hạn số kết nối đang hoạt động
            if (activeConnections.get() >= getMaxPoolSize()) {
                // Chờ một chút và thử lại
                try {
                    poolLock.wait(100); // Chờ tối đa 100ms
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    return null;
                }

                // Thử lại lấy từ pool
                conn = connectionPool.poll();
                if (conn != null) {
                    try {
                        if (!conn.isClosed() && conn.isValid(1)) {
                            activeConnections.incrementAndGet();
                            return conn;
                        } else {
                            safeCloseConnection(conn);
                        }
                    } catch (SQLException e) {
                        safeCloseConnection(conn);
                    }
                }
            }

            // Tạo kết nối mới
            conn = getSQLConnection();
            if (conn != null) {
                activeConnections.incrementAndGet();
            }
            return conn;
        }
    }

    /**
     * Lấy kết nối ThreadLocal cho SQLite - mỗi thread có kết nối riêng
     *
     * @return Kết nối SQL cho thread hiện tại
     */
    private Connection getThreadLocalConnection() {
        Connection conn = threadLocalConnection.get();

        try {
            // Kiểm tra kết nối hiện tại
            if (conn != null && !conn.isClosed() && conn.isValid(1)) {
                return conn;
            }
        } catch (SQLException e) {
            // Kết nối không hợp lệ, cần tạo mới
            Storage.getStorage().getLogger().fine("ThreadLocal connection không hợp lệ: " + e.getMessage());
        }

        // Đóng kết nối cũ nếu có
        if (conn != null) {
            safeCloseConnection(conn);
            allConnections.remove(conn);
        }

        // Tạo kết nối mới cho thread này
        conn = getSQLConnection();
        if (conn != null) {
            threadLocalConnection.set(conn);
            allConnections.add(conn);

            // Thiết lập SQLite-specific settings cho thread safety
            try {
                conn.setAutoCommit(true); // Đảm bảo auto-commit
                // Kiểm tra nếu là SQLite connection (cả relocated và original)
                String connClass = conn.getClass().getName();
                if (connClass.contains("sqlite") || connClass.contains("SQLite")) {
                    // Thiết lập busy timeout cho kết nối này
                    try (Statement stmt = conn.createStatement()) {
                        stmt.execute("PRAGMA busy_timeout = 30000");
                        stmt.execute("PRAGMA synchronous = NORMAL"); // Cân bằng giữa tốc độ và an toàn
                    }
                }
            } catch (SQLException e) {
                Storage.getStorage().getLogger().warning("Không thể thiết lập PRAGMA cho ThreadLocal connection: " + e.getMessage());
            }
        }

        return conn;
    }

    /**
     * Đóng kết nối một cách an toàn
     */
    private void safeCloseConnection(Connection conn) {
        if (conn != null) {
            try {
                if (!conn.isClosed()) {
                    conn.close();
                }
            } catch (SQLException ignored) {
                // Bỏ qua lỗi khi đóng
            }
        }
    }

    /**
     * Trả kết nối về pool thay vì đóng - Thread-safe implementation
     * Đối với SQLite, không trả về pool mà giữ trong ThreadLocal
     *
     * @param conn Kết nối cần trả về
     */
    public void returnConnection(Connection conn) {
        if (conn == null) return;

        // Đối với SQLite, không trả về pool mà giữ kết nối trong ThreadLocal
        if (this instanceof com.hongminh54.storage.Database.SQLite) {
            // Chỉ kiểm tra tính hợp lệ, không đóng kết nối
            try {
                if (conn.isClosed() || !conn.isValid(1)) {
                    // Kết nối không hợp lệ, xóa khỏi ThreadLocal
                    threadLocalConnection.remove();
                    allConnections.remove(conn);
                    safeCloseConnection(conn);
                }
            } catch (SQLException e) {
                Storage.getStorage().getLogger().fine("Lỗi khi kiểm tra ThreadLocal connection: " + e.getMessage());
                threadLocalConnection.remove();
                allConnections.remove(conn);
                safeCloseConnection(conn);
            }
            return;
        }

        // Đối với MySQL và các database khác, sử dụng connection pool
        synchronized (poolLock) {
            try {
                // Giảm số lượng kết nối đang hoạt động
                activeConnections.decrementAndGet();

                // Thông báo cho các thread đang chờ
                poolLock.notifyAll();

                // Nếu đang shutdown, đóng kết nối thay vì trả về pool
                if (isShuttingDown) {
                    safeCloseConnection(conn);
                    return;
                }

                // Kiểm tra kết nối còn hợp lệ không
                if (conn.isClosed() || !conn.isValid(1)) {
                    safeCloseConnection(conn);
                    return;
                }

                // Đảm bảo không còn transaction đang hoạt động khi trả kết nối về pool
                try {
                    if (!conn.getAutoCommit()) {
                        conn.rollback(); // Rollback bất kỳ transaction nào chưa commit
                        conn.setAutoCommit(true);
                    }
                } catch (SQLException e) {
                    Storage.getStorage().getLogger().warning("Không thể đặt lại autoCommit khi trả connection: " + e.getMessage());
                    safeCloseConnection(conn);
                    return;
                }

                // Trả kết nối về pool nếu còn chỗ và chưa shutdown
                if (connectionPool.size() < getMaxPoolSize() && !isShuttingDown) {
                    connectionPool.add(conn);
                } else {
                    // Pool đầy hoặc đang shutdown, đóng kết nối
                    safeCloseConnection(conn);
                }
            } catch (SQLException e) {
                // Xử lý lỗi khi kiểm tra trạng thái kết nối
                Storage.getStorage().getLogger().warning("Lỗi khi trả connection về pool: " + e.getMessage());
                safeCloseConnection(conn);
                activeConnections.decrementAndGet(); // Đảm bảo counter được giảm
                poolLock.notifyAll(); // Thông báo cho các thread đang chờ
            }
        }
    }

    /**
     * Tạo kết nối riêng biệt cho async operations để tránh xung đột
     *
     * @return Kết nối mới hoặc null nếu không thể tạo
     */
    public Connection createDedicatedConnection() {
        if (isShuttingDown) {
            return null;
        }

        Connection conn = getSQLConnection();
        if (conn != null) {
            try {
                conn.setAutoCommit(true);
                // Thiết lập timeout ngắn hơn cho async operations
                String connClass = conn.getClass().getName();
                if (connClass.contains("sqlite") || connClass.contains("SQLite")) {
                    try (Statement stmt = conn.createStatement()) {
                        stmt.execute("PRAGMA busy_timeout = 10000"); // 10 giây thay vì 30
                        stmt.execute("PRAGMA synchronous = NORMAL");
                    }
                }
            } catch (SQLException e) {
                Storage.getStorage().getLogger().warning("Không thể thiết lập dedicated connection: " + e.getMessage());
                safeCloseConnection(conn);
                return null;
            }
        }
        return conn;
    }

    /**
     * Đóng tất cả kết nối ThreadLocal khi shutdown
     */
    public void closeAllThreadLocalConnections() {
        isShuttingDown = true;

        // Flush tất cả pending operations trước khi shutdown
        if (synchronizer != null) {
            Storage.getStorage().getLogger().info("Đang flush pending database operations...");
            synchronizer.flushAll();
        }

        // Đóng tất cả ThreadLocal connections
        for (Connection conn : allConnections) {
            safeCloseConnection(conn);
        }
        allConnections.clear();

        // Xóa ThreadLocal
        threadLocalConnection.remove();

        Storage.getStorage().getLogger().info("Đã đóng tất cả ThreadLocal connections");
    }

    /**
     * Lưu dữ liệu đồng bộ (sử dụng batch processing nếu có thể)
     */
    public void updateTableSync(PlayerData playerData) {
        if (synchronizer != null && synchronizer.isEnabled()) {
            // Sử dụng batch processing
            synchronizer.queueOperation(DatabaseOperation.createUpdate(playerData));
        } else {
            // Fallback: thực hiện ngay lập tức
            updateTable(playerData);
        }
    }

    /**
     * Lưu dữ liệu urgent (ưu tiên cao, bypass batch)
     */
    public void updateTableUrgent(PlayerData playerData) {
        if (synchronizer != null && synchronizer.isEnabled()) {
            synchronizer.queueOperation(DatabaseOperation.createUrgentUpdate(playerData));
        } else {
            updateTable(playerData);
        }
    }

    /**
     * Tạo dữ liệu mới đồng bộ
     */
    public void createTableSync(PlayerData playerData) {
        if (synchronizer != null && synchronizer.isEnabled()) {
            synchronizer.queueOperation(DatabaseOperation.createInsert(playerData));
        } else {
            createTable(playerData);
        }
    }

    /**
     * Xóa dữ liệu đồng bộ
     */
    public void deleteDataSync(String playerName) {
        if (synchronizer != null && synchronizer.isEnabled()) {
            synchronizer.queueOperation(DatabaseOperation.createDelete(playerName));
        } else {
            deleteData(playerName);
        }
    }

    /**
     * Lấy thông tin về pending operations
     */
    public String getSynchronizerStatus() {
        if (synchronizer == null) {
            return "Synchronizer không khả dụng";
        }
        return String.format("Pending: %d, Processed: %d, Enabled: %s",
                synchronizer.getPendingOperations(),
                synchronizer.getProcessedOperations(),
                synchronizer.isEnabled());
    }

    /**
     * Flush tất cả pending operations ngay lập tức
     */
    public void flushPendingOperations() {
        if (synchronizer != null) {
            // Tạo một thread riêng để flush để không block main thread
            new Thread(() -> {
                try {
                    int pendingBefore = synchronizer.getPendingOperations();
                    // Force process tất cả operations còn lại
                    while (synchronizer.getPendingOperations() > 0) {
                        Thread.sleep(50); // Chờ synchronizer xử lý
                    }
                    Storage.getStorage().getLogger().info("Đã flush " + pendingBefore + " pending operations");
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }, "Storage-Flush-Thread").start();
        }
    }

    // These are the methods you can use to get things out of your database. You of course can make new ones to return different things in the database.
    // This returns the number of people the player killed.
    public PlayerData getData(String player) {
        if (player == null || player.isEmpty()) {
            Storage.getStorage().getLogger().warning("Không thể truy vấn dữ liệu với player null hoặc rỗng");
            return null;
        }

        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;

        // Biến đếm số lần thử lại
        int retryCount = 0;
        final int maxRetries = 3;
        final long retryDelay = 500; // 500ms

        while (retryCount <= maxRetries) {
            try {
                conn = getConnection();

                if (conn == null) {
                    Storage.getStorage().getLogger().warning("Không thể lấy kết nối database để truy vấn dữ liệu cho " + player);
                    if (retryCount < maxRetries) {
                        retryCount++;
                        try {
                            Thread.sleep(retryDelay * retryCount);
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt();
                            break;
                        }
                        continue; // Thử lại
                    } else {
                        break; // Hết số lần thử, thoát khỏi vòng lặp
                    }
                }

                // Đảm bảo kết nối còn hiệu lực
                try {
                    if (conn.isClosed() || !conn.isValid(1)) {
                        if (conn != null) {
                            try {
                                conn.close();
                            } catch (SQLException ignored) {
                            }
                        }
                        conn = getConnection(); // Tạo kết nối mới
                        if (conn == null) {
                            if (retryCount < maxRetries) {
                                retryCount++;
                                Thread.sleep(retryDelay * retryCount);
                                continue;
                            } else {
                                break;
                            }
                        }
                    }
                } catch (SQLException e) {
                    // Nếu không kiểm tra được trạng thái kết nối, tiếp tục sử dụng kết nối hiện tại
                    Storage.getStorage().getLogger().fine("Không thể kiểm tra trạng thái kết nối: " + e.getMessage());
                }

                // Sử dụng try-with-resources để đảm bảo đóng các tài nguyên
                try {
                    // Sử dụng PreparedStatement với tham số để tránh SQL injection
                    ps = conn.prepareStatement("SELECT * FROM " + table + " WHERE player = ?");
                    ps.setString(1, player);
                    ps.setQueryTimeout(30); // Thiết lập timeout 30 giây
                    rs = ps.executeQuery();

                    if (rs.next()) {
                        String statsData = "{}";
                        boolean autoPickup = true;

                        try {
                            statsData = rs.getString("statsData");
                            if (statsData == null) statsData = "{}";
                        } catch (SQLException e) {
                            // Cột có thể chưa tồn tại trong bảng cũ
                            statsData = "{}";
                            Storage.getStorage().getLogger().fine("Không tìm thấy cột statsData: " + e.getMessage());
                        }

                        try {
                            // Thêm kiểm tra an toàn cho ResultSet trước khi gọi getBoolean
                            if (rs != null && !rs.isClosed()) {
                                autoPickup = rs.getBoolean("auto_pickup");
                            } else {
                                autoPickup = true;
                                Storage.getStorage().getLogger().warning("ResultSet đã bị đóng khi đọc auto_pickup cho " + player);
                            }
                        } catch (SQLException e) {
                            // Cột có thể chưa tồn tại trong bảng cũ hoặc có lỗi native
                            autoPickup = true; // Giá trị mặc định là true
                            Storage.getStorage().getLogger().fine("Không thể đọc cột auto_pickup: " + e.getMessage());
                        } catch (Exception e) {
                            // Bắt tất cả các lỗi khác bao gồm native crashes
                            autoPickup = true;
                            Storage.getStorage().getLogger().warning("Lỗi native khi đọc auto_pickup cho " + player + ": " + e.getMessage());
                        }

                        // Kiểm tra trường dữ liệu chính với bảo vệ native crash
                        String data = "{}";
                        int maxStorage = 0;

                        try {
                            if (rs != null && !rs.isClosed()) {
                                data = rs.getString("data");
                                if (data == null) data = "{}";
                                maxStorage = rs.getInt("max");
                            } else {
                                Storage.getStorage().getLogger().warning("ResultSet đã bị đóng khi đọc dữ liệu chính cho " + player);
                            }
                        } catch (Exception e) {
                            Storage.getStorage().getLogger().warning("Lỗi khi đọc dữ liệu chính cho " + player + ": " + e.getMessage());
                            data = "{}";
                            maxStorage = File.getConfig().getInt("settings.default_max_storage", 1000);
                        }

                        // Tạo đối tượng PlayerData trước khi đóng ResultSet
                        PlayerData result = new PlayerData(player, data, maxStorage, statsData, autoPickup);

                        // Trả về đối tượng PlayerData
                        return result;
                    }
                } finally {
                    // Sử dụng phương thức close() an toàn
                    close(ps, rs);
                }

                // Không tìm thấy dữ liệu và không có lỗi, thoát khỏi vòng lặp
                break;

            } catch (SQLException ex) {
                // Kiểm tra xem lỗi có phải là database locked không
                boolean isLockError = ex.getMessage().contains("locked") ||
                        ex.getMessage().contains("busy") ||
                        ex.getMessage().contains("mutex") ||
                        ex.getErrorCode() == 5; // SQLite error code for busy/locked

                if (isLockError && retryCount < maxRetries) {
                    retryCount++;

                    // Log thông tin về việc thử lại
                    Storage.getStorage().getLogger().warning("Database bị khóa khi truy vấn dữ liệu cho " + player +
                            ". Đang thử lại lần " + retryCount + "/" + maxRetries);

                    try {
                        // Đóng tài nguyên trước khi thử lại sử dụng phương thức an toàn
                        close(ps, rs);
                        ps = null;
                        rs = null;

                        // Trả kết nối về pool (hoặc đóng nếu có vấn đề)
                        if (conn != null) {
                            try {
                                returnConnection(conn);
                                conn = null; // Đánh dấu conn là null để tạo mới ở lần lặp tiếp theo
                            } catch (Exception e) {
                                // Nếu không thể trả về, thử đóng
                                try {
                                    conn.close();
                                } catch (SQLException ignored) {
                                }
                                conn = null;
                            }
                        }

                        // Chờ một chút trước khi thử lại
                        Thread.sleep(retryDelay * retryCount);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    } catch (Exception e) {
                        Storage.getStorage().getLogger().warning("Lỗi khi dọn dẹp tài nguyên để thử lại: " + e.getMessage());
                        break;
                    }

                    // Tiếp tục vòng lặp để thử lại
                    continue;
                } else {
                    // Ghi log lỗi chi tiết
                    Storage.getStorage().getLogger().log(Level.SEVERE, "Lỗi SQL khi truy vấn dữ liệu cho " + player + ": " + ex.getMessage(), ex);
                    break;
                }
            } catch (Exception ex) {
                // Xử lý các loại lỗi khác
                Storage.getStorage().getLogger().log(Level.SEVERE, "Lỗi không xác định khi truy vấn dữ liệu cho " + player + ": " + ex.getMessage(), ex);
                break;
            } finally {
                // Đảm bảo trả kết nối về pool
                if (conn != null) {
                    try {
                        returnConnection(conn);
                    } catch (Exception e) {
                        Storage.getStorage().getLogger().warning("Lỗi khi trả connection về pool: " + e.getMessage());
                        try {
                            conn.close();
                        } catch (SQLException ignored) {
                        }
                    }
                }
            }
        }

        if (retryCount > maxRetries) {
            Storage.getStorage().getLogger().severe("Không thể truy vấn dữ liệu cho " + player + " sau " + maxRetries + " lần thử");
        }

        return null;
    }

    public void createTable(@NotNull PlayerData playerData) {
        Connection conn = null;
        PreparedStatement ps = null;
        try {
            conn = getConnection();
            ps = conn.prepareStatement("INSERT INTO " + table + " (player,data,max,statsData,auto_pickup) VALUES(?,?,?,?,?)");
            ps.setString(1, playerData.getPlayer());
            ps.setString(2, playerData.getData());
            ps.setInt(3, playerData.getMax());
            ps.setString(4, playerData.getStatsData());
            ps.setBoolean(5, playerData.isAutoPickup());
            ps.executeUpdate();
        } catch (SQLException ex) {
            Storage.getStorage().getLogger().log(Level.SEVERE, Errors.sqlConnectionExecute(), ex);
        } finally {
            try {
                if (ps != null)
                    ps.close();
                if (conn != null)
                    returnConnection(conn);
            } catch (SQLException ex) {
                Storage.getStorage().getLogger().log(Level.SEVERE, Errors.sqlConnectionClose(), ex);
            }
        }
    }


    public void updateTable(@NotNull PlayerData playerData) {
        // Sử dụng retry đơn giản cho SQLite
        if (this instanceof com.hongminh54.storage.Database.SQLite) {
            com.hongminh54.storage.Database.SQLite sqliteDb = (com.hongminh54.storage.Database.SQLite) this;
            boolean success = sqliteDb.executeWithSimpleRetry(() -> {
                updateTableDirect(playerData);
            }, 3);

            if (!success) {
                Storage.getStorage().getLogger().warning("Không thể cập nhật dữ liệu sau 3 lần thử cho: " + playerData.getPlayer());
            }
        } else {
            updateTableDirect(playerData);
        }
    }

    /**
     * Cập nhật trực tiếp không có retry
     */
    public void updateTableDirect(@NotNull PlayerData playerData) {
        Connection conn = null;
        PreparedStatement ps = null;

        try {
            conn = getConnection();

            // Kiểm tra kết nối
            if (conn == null || conn.isClosed()) {
                Storage.getStorage().getLogger().severe("Không thể cập nhật dữ liệu: kết nối null hoặc đã đóng");
                return;
            }

            // Thực thi câu lệnh SQL đơn giản với autoCommit mặc định
            ps = conn.prepareStatement("UPDATE " + table + " SET data = ?, max = ?, statsData = ?, auto_pickup = ? " +
                    "WHERE player = ?");
            ps.setString(1, playerData.getData());
            ps.setInt(2, playerData.getMax());
            ps.setString(3, playerData.getStatsData());
            ps.setBoolean(4, playerData.isAutoPickup());
            ps.setString(5, playerData.getPlayer());
            ps.executeUpdate();

        } catch (SQLException ex) {
            Storage.getStorage().getLogger().log(Level.SEVERE, "Lỗi khi cập nhật dữ liệu: " + ex.getMessage(), ex);
        } finally {
            // Đóng PreparedStatement
            if (ps != null) {
                try {
                    ps.close();
                } catch (SQLException e) {
                    Storage.getStorage().getLogger().warning("Không thể đóng PreparedStatement: " + e.getMessage());
                }
            }

            // Trả kết nối về pool
            if (conn != null) {
                returnConnection(conn);
            }
        }
    }

    public void deleteData(String player) {
        Connection conn = null;
        PreparedStatement ps = null;
        try {
            conn = getConnection();
            ps = conn.prepareStatement("DELETE FROM " + table + " WHERE player = ?");
            ps.setString(1, player);
            ps.executeUpdate();
        } catch (SQLException ex) {
            Storage.getStorage().getLogger().log(Level.SEVERE, Errors.sqlConnectionExecute(), ex);
        } finally {
            try {
                if (ps != null)
                    ps.close();
                if (conn != null)
                    returnConnection(conn);
            } catch (SQLException ex) {
                Storage.getStorage().getLogger().log(Level.SEVERE, Errors.sqlConnectionClose(), ex);
            }
        }
    }

    public void close(PreparedStatement ps, ResultSet rs) {
        try {
            if (rs != null && !rs.isClosed()) {
                rs.close();
            }
        } catch (SQLException ex) {
            Storage.getStorage().getLogger().fine("Lỗi khi đóng ResultSet: " + ex.getMessage());
        }

        try {
            if (ps != null && !ps.isClosed()) {
                ps.close();
            }
        } catch (SQLException ex) {
            Storage.getStorage().getLogger().fine("Lỗi khi đóng PreparedStatement: " + ex.getMessage());
        }
    }

    /**
     * Đóng kết nối database khi plugin tắt - Thread-safe implementation
     */
    public void closeConnection() {
        synchronized (poolLock) {
            // Đặt flag shutdown để ngăn tạo kết nối mới
            isShuttingDown = true;

            // Thông báo cho tất cả thread đang chờ
            poolLock.notifyAll();

            // Đóng tất cả các kết nối trong pool
            Connection conn;
            while ((conn = connectionPool.poll()) != null) {
                safeCloseConnection(conn);
            }

            // Reset counter
            activeConnections.set(0);
        }

        // Đóng kết nối chính nếu có
        try {
            if (connection != null && !connection.isClosed()) {
                connection.close();
            }
        } catch (SQLException e) {
            Storage.getStorage().getLogger().log(Level.WARNING, "Không thể đóng kết nối chính khi shutdown", e);
        }
    }
}
