package com.hongminh54.storage.Manager;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.UUID;

import org.bukkit.Material;
import org.bukkit.block.Block;
import org.bukkit.block.BlockFace;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.scheduler.BukkitRunnable;

import com.hongminh54.storage.NMS.NMSAssistant;
import com.hongminh54.storage.Storage;
import com.hongminh54.storage.Utils.Chat;
import com.hongminh54.storage.Utils.File;

/**
 * Manager quản lý các phù phép cho cuốc
 */
public class HoeEnchantManager {

    // Danh sách phù phép hỗ trợ
    public static final String FARMERS_TOUCH = "farmers_touch";
    public static final String FERTILE_SOIL = "fertile_soil";
    public static final String FARMERS_WISDOM = "farmers_wisdom";
    public static final String REGENERATION = "regeneration";

    // Cooldown để tránh lag
    private static final Map<UUID, Map<String, Long>> cooldownMap = new HashMap<>();
    private static final Random random = new Random();

    // Thêm biến nmsAssistant để kiểm tra phiên bản
    private static final NMSAssistant nmsAssistant = new NMSAssistant();

    /**
     * Kiểm tra xem một item có phải là cuốc hay không
     *
     * @param item ItemStack cần kiểm tra
     * @return true nếu là cuốc, false nếu không phải
     */
    public static boolean isHoe(ItemStack item) {
        if (item == null) return false;

        String typeName = item.getType().name();

        // Kiểm tra trực tiếp tất cả các loại cuốc có thể có
        return typeName.equals("WOOD_HOE") ||
                typeName.equals("STONE_HOE") ||
                typeName.equals("IRON_HOE") ||
                typeName.equals("GOLD_HOE") ||
                typeName.equals("DIAMOND_HOE") ||
                typeName.equals("WOODEN_HOE") ||  // 1.13+
                typeName.equals("GOLDEN_HOE") ||  // 1.13+
                typeName.equals("NETHERITE_HOE") || // 1.16+
                typeName.endsWith("_HOE"); // Để tương thích với các phiên bản khác
    }

    /**
     * Kiểm tra xem một block có phải là cây trồng không
     * Tương thích đa phiên bản 1.12.2 - 1.21.x
     *
     * @param block Block cần kiểm tra
     * @return true nếu là cây trồng, false nếu không phải
     */
    public static boolean isCrop(Block block) {
        if (block == null) return false;

        String typeName = block.getType().name();

        // Kiểm tra tất cả các tên có thể có cho từng phiên bản
        return
                // Wheat crops
                typeName.equals("CROPS") || typeName.equals("WHEAT") ||
                        // Carrot crops
                        typeName.equals("CARROT") || typeName.equals("CARROTS") ||
                        // Potato crops
                        typeName.equals("POTATO") || typeName.equals("POTATOES") ||
                        // Beetroot crops
                        typeName.equals("BEETROOT_BLOCK") || typeName.equals("BEETROOTS") ||
                        // Other crops
                        typeName.equals("NETHER_WART") ||
                        typeName.equals("PUMPKIN_STEM") ||
                        typeName.equals("MELON_STEM") ||
                        typeName.equals("COCOA") ||
                        // Fallback patterns
                        typeName.contains("WHEAT") || typeName.contains("CARROT") || typeName.contains("POTATO") ||
                        typeName.contains("BEETROOT") || typeName.contains("NETHER_WART") ||
                        typeName.contains("PUMPKIN_STEM") || typeName.contains("MELON_STEM") || typeName.contains("COCOA");
    }

    /**
     * Kiểm tra xem một block có phải là đất màu mỡ không
     * Tương thích đa phiên bản 1.12.2 - 1.21.x
     *
     * @param block Block cần kiểm tra
     * @return true nếu là đất màu mỡ, false nếu không phải
     */
    public static boolean isSoil(Block block) {
        if (block == null) return false;

        String typeName = block.getType().name();

        // Kiểm tra tất cả các tên có thể có cho farmland qua các phiên bản
        return typeName.equals("SOIL") ||           // MC 1.12.2
                typeName.equals("FARMLAND") ||       // MC 1.13+
                typeName.contains("FARMLAND") ||     // Fallback pattern
                typeName.contains("SOIL");           // Fallback pattern
    }

    /**
     * Lấy cấp độ phù phép của một loại phù phép cụ thể
     *
     * @param item        ItemStack cần kiểm tra
     * @param enchantType Loại phù phép
     * @return Cấp độ phù phép (0 nếu không có)
     */
    public static int getEnchantLevel(ItemStack item, String enchantType) {
        if (item == null || !item.hasItemMeta() || !item.getItemMeta().hasLore()) {
            return 0;
        }

        List<String> lore = item.getItemMeta().getLore();
        if (lore == null) return 0;

        String configPath = "hoe_enchant." + enchantType + ".lore_prefix";
        String defaultPrefix = "&a&l" + getDefaultPrefix(enchantType);
        String enchantPrefix = Chat.colorizewp(File.getEnchants().getString(configPath, defaultPrefix));

        for (String line : lore) {
            if (line.contains(enchantPrefix)) {
                if (line.contains("III")) return 3;
                if (line.contains("II")) return 2;
                if (line.contains("I")) return 1;
            }
        }

        return 0;
    }

    /**
     * Lấy tiền tố mặc định cho phù phép
     *
     * @param enchantType Loại phù phép
     * @return Tiền tố mặc định
     */
    private static String getDefaultPrefix(String enchantType) {
        switch (enchantType) {
            case FARMERS_TOUCH:
                return "Nông Dân Chuyên Nghiệp";
            case FERTILE_SOIL:
                return "Đất Màu Mỡ";
            case FARMERS_WISDOM:
                return "Kinh Nghiệm Nông Dân";
            case REGENERATION:
                return "Tái Sinh";
            default:
                return enchantType;
        }
    }

    /**
     * Thêm phù phép vào cuốc
     *
     * @param item        Cuốc cần thêm phù phép
     * @param enchantType Loại phù phép
     * @param level       Cấp độ phù phép (1-3)
     * @return Item đã được thêm phù phép
     */
    public static ItemStack addHoeEnchant(ItemStack item, String enchantType, int level) {
        if (!isHoe(item)) {
            return item;
        }

        if (level < 1 || level > 3) {
            level = 1;
        }

        ItemMeta meta = item.getItemMeta();
        if (meta == null) return item;

        List<String> lore = meta.hasLore() ? meta.getLore() : new ArrayList<>();
        if (lore == null) lore = new ArrayList<>();

        // Xóa phù phép cũ cùng loại nếu có
        String configPath = "hoe_enchant." + enchantType + ".lore_prefix";
        String defaultPrefix = "&a&l" + getDefaultPrefix(enchantType);
        String enchantPrefix = Chat.colorizewp(File.getEnchants().getString(configPath, defaultPrefix));

        lore.removeIf(line -> line.contains(enchantPrefix));

        // Thêm phù phép mới
        String enchantText = Chat.colorizewp(enchantPrefix + " " + getRomanNumeral(level));
        lore.add(enchantText);

        meta.setLore(lore);
        item.setItemMeta(meta);

        if (Storage.getStorage().isDebug()) {
            Storage.getStorage().getLogger().info("Đã thêm phù phép " + enchantType + " " + level + " vào cuốc: " + item.getType().name());
        }

        return item;
    }

    /**
     * Xóa phù phép khỏi item
     *
     * @param item        Item cần xóa phù phép
     * @param enchantType Loại phù phép cần xóa
     * @return Item sau khi đã xóa phù phép
     */
    public static ItemStack removeHoeEnchant(ItemStack item, String enchantType) {
        if (item == null || !item.hasItemMeta() || !item.getItemMeta().hasLore()) {
            return item;
        }

        ItemMeta meta = item.getItemMeta();
        List<String> lore = meta.getLore();

        // Xóa phù phép cụ thể
        String configPath = "hoe_enchant." + enchantType + ".lore_prefix";
        String defaultPrefix = "&a&l" + getDefaultPrefix(enchantType);
        String enchantPrefix = Chat.colorizewp(File.getEnchants().getString(configPath, defaultPrefix));

        lore.removeIf(line -> line.contains(enchantPrefix));

        meta.setLore(lore);
        item.setItemMeta(meta);

        return item;
    }

    /**
     * Kiểm tra cooldown của phù phép (không set cooldown)
     *
     * @param player      Người chơi
     * @param enchantType Loại phù phép
     * @return true nếu hết cooldown, false nếu vẫn trong thời gian cooldown
     */
    public static boolean checkCooldown(Player player, String enchantType) {
        UUID uuid = player.getUniqueId();
        long currentTime = System.currentTimeMillis();

        // Khởi tạo map nếu chưa có
        if (!cooldownMap.containsKey(uuid)) {
            cooldownMap.put(uuid, new HashMap<>());
        }

        Map<String, Long> playerCooldowns = cooldownMap.get(uuid);

        // Kiểm tra cooldown
        if (playerCooldowns.containsKey(enchantType)) {
            long lastUse = playerCooldowns.get(enchantType);
            int cooldownTime = File.getEnchants().getInt("hoe_enchant." + enchantType + ".cooldown", 1000); // 1 giây mặc định

            return currentTime - lastUse >= cooldownTime; // Vẫn trong thời gian cooldown
        }

        return true; // Hết cooldown, nhưng chưa set cooldown mới
    }

    /**
     * Set cooldown cho phù phép (chỉ gọi khi function thành công)
     *
     * @param player      Người chơi
     * @param enchantType Loại phù phép
     */
    public static void setCooldown(Player player, String enchantType) {
        UUID uuid = player.getUniqueId();
        long currentTime = System.currentTimeMillis();

        // Khởi tạo map nếu chưa có
        if (!cooldownMap.containsKey(uuid)) {
            cooldownMap.put(uuid, new HashMap<>());
        }

        Map<String, Long> playerCooldowns = cooldownMap.get(uuid);
        playerCooldowns.put(enchantType, currentTime);
    }

    /**
     * Xử lý phù phép Nông Dân Chuyên Nghiệp (Farmers Touch)
     *
     * @param player Người chơi
     * @param block  Block cây trồng bị phá
     * @param level  Cấp độ phù phép
     * @return true nếu thành công, false nếu thất bại
     */
    public static boolean handleFarmersTouch(Player player, Block block, int level) {
        if (!checkCooldown(player, FARMERS_TOUCH)) {
            return false;
        }

        Material cropType = block.getType();
        Block soilBlock = block.getRelative(BlockFace.DOWN);

        // Kiểm tra xem block bên dưới có phải là đất màu mỡ không
        if (!isSoil(soilBlock)) {
            return false;
        }

        // Kiểm tra xem cây trồng có chín chưa
        if (!isMatureCrop(block)) {
            return false;
        }

        // Lấy vật liệu hạt giống từ loại cây trồng
        Material seedType = getSeedType(cropType);
        if (seedType == null) {
            return false;
        }

        // Tính tỷ lệ thành công dựa vào cấp độ phù phép
        int successChance = 50 + (level * 20); // 70/90/100% tùy cấp độ
        if (random.nextInt(100) >= successChance) {
            return false;
        }

        // Lấy crop type từ seed type
        Material newCropType = getCropType(seedType);
        if (newCropType == null) {
            return false;
        }

        // Lên lịch trình tự động trồng lại với delay lâu hơn để đảm bảo block đã được clear
        final Block finalBlock = block;
        final Material finalCropType = newCropType;
        final Material finalSeedType = seedType;
        int delay = File.getEnchants().getInt("hoe_enchant.farmers_touch.replant_delay", 10);

        new BukkitRunnable() {
            @Override
            public void run() {
                try {
                    // Kiểm tra block có phải AIR không (đã được clear)
                    if (finalBlock.getType() != Material.AIR) {
                        finalBlock.setType(Material.AIR);

                        // Delay thêm 1 tick để đảm bảo block được clear hoàn toàn
                        new BukkitRunnable() {
                            @Override
                            public void run() {
                                plantCrop(finalBlock, finalCropType, finalSeedType, player.getName());
                            }
                        }.runTaskLater(Storage.getStorage(), 1L);
                        return;
                    }

                    plantCrop(finalBlock, finalCropType, finalSeedType, player.getName());
                } catch (Exception e) {
                    Storage.getStorage().getLogger().warning("Lỗi khi tự động trồng lại cây: " + e.getMessage());
                }
            }
        }.runTaskLater(Storage.getStorage(), delay);

        // Set cooldown chỉ khi function thành công
        setCooldown(player, FARMERS_TOUCH);
        return true;
    }

    /**
     * Trồng cây tại vị trí cụ thể
     *
     * @param block      Block để trồng cây
     * @param cropType   Loại crop block
     * @param seedType   Loại seed (để debug)
     * @param playerName Tên người chơi (để debug)
     */
    private static void plantCrop(Block block, Material cropType, Material seedType, String playerName) {
        try {
            // Kiểm tra block hiện tại
            if (block.getType() != Material.AIR) {
                block.setType(Material.AIR);
            }

            // Đặt loại cây trồng mới
            block.setType(cropType);

            // Đối với phiên bản 1.13+ sử dụng BlockData để đặt giai đoạn ban đầu
            if (nmsAssistant.isVersionGreaterThanOrEqualTo(13)) {
                try {
                    // Sử dụng reflection để gọi phương thức BlockData một cách an toàn
                    Class<?> blockDataClass = Class.forName("org.bukkit.block.data.BlockData");
                    Class<?> ageable = Class.forName("org.bukkit.block.data.Ageable");

                    Object blockData = block.getClass().getMethod("getBlockData").invoke(block);

                    if (ageable.isInstance(blockData)) {
                        ageable.getMethod("setAge", int.class).invoke(blockData, 0);
                        block.getClass().getMethod("setBlockData", blockDataClass).invoke(block, blockData);
                    }
                } catch (Exception e) {
                    // Ignore reflection errors
                }
            } else {
                // Đối với phiên bản 1.12.2, sử dụng byte data
                try {
                    // Sử dụng reflection để gọi phương thức setData an toàn
                    block.getClass().getMethod("setData", byte.class).invoke(block, (byte) 0);
                } catch (Exception e) {
                    // Ignore reflection errors
                }
            }
        } catch (Exception e) {
            // Ignore planting errors
        }
    }

    /**
     * Kiểm tra xem cây trồng đã trưởng thành chưa
     * Tương thích đa phiên bản 1.12.2 - 1.21.x
     *
     * @param block Block cần kiểm tra
     * @return true nếu đã trưởng thành, false nếu chưa
     */
    @SuppressWarnings("deprecation")
    private static boolean isMatureCrop(Block block) {
        if (!isCrop(block)) return false;

        String typeName = block.getType().name();
        NMSAssistant nmsAssistant = new NMSAssistant();

        try {
            // Thử sử dụng BlockData API cho MC 1.13+
            if (nmsAssistant.isVersionGreaterThanOrEqualTo(13)) {
                Object blockData = block.getClass().getMethod("getBlockData").invoke(block);

                // Kiểm tra xem có phải Ageable không
                Class<?> ageableClass = Class.forName("org.bukkit.block.data.Ageable");
                if (ageableClass.isInstance(blockData)) {
                    int age = (Integer) ageableClass.getMethod("getAge").invoke(blockData);
                    int maxAge = (Integer) ageableClass.getMethod("getMaximumAge").invoke(blockData);
                    return age >= maxAge;
                }
            }
        } catch (Exception e) {
            // Fallback cho phiên bản cũ hoặc khi reflection thất bại
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().info("Sử dụng fallback method cho crop maturity check: " + e.getMessage());
            }
        }

        // Fallback method cho MC 1.12.2 và các trường hợp khác
        // Sử dụng data value để kiểm tra (deprecated nhưng cần thiết cho 1.12.2)
        byte data = block.getData();

        // Wheat crops (CROPS/WHEAT)
        if (typeName.equals("CROPS") || typeName.equals("WHEAT")) {
            return data >= 7; // Mature stage cho wheat
        }
        // Carrot crops (CARROT/CARROTS)
        else if (typeName.equals("CARROT") || typeName.equals("CARROTS")) {
            return data >= 7; // Mature stage cho carrots
        }
        // Potato crops (POTATO/POTATOES)
        else if (typeName.equals("POTATO") || typeName.equals("POTATOES")) {
            return data >= 7; // Mature stage cho potatoes
        }
        // Beetroot crops (BEETROOT_BLOCK/BEETROOTS)
        else if (typeName.equals("BEETROOT_BLOCK") || typeName.equals("BEETROOTS")) {
            return data >= 3; // Mature stage cho beetroots
        }
        // Nether wart
        else if (typeName.equals("NETHER_WART")) {
            return data >= 3; // Mature stage cho nether wart
        }
        // Cocoa
        else if (typeName.equals("COCOA")) {
            return (data & 12) == 8; // Cocoa mature check với bit masking
        }
        // Stems
        else if (typeName.equals("PUMPKIN_STEM") || typeName.equals("MELON_STEM")) {
            return data >= 7; // Mature stage cho stems
        }

        return false;
    }

    /**
     * Lấy loại hạt giống tương ứng với cây trồng
     * Tương thích đa phiên bản 1.12.2 - 1.21.x
     *
     * @param cropType Loại cây trồng
     * @return Material của hạt giống
     */
    private static Material getSeedType(Material cropType) {
        String typeName = cropType.name();

        // Wheat crops
        if (typeName.equals("CROPS") || typeName.equals("WHEAT")) {
            Material wheatSeeds = com.hongminh54.storage.compatibility.MaterialCompatibility.getMaterialSafely("WHEAT_SEEDS");
            if (wheatSeeds != null) {
                return wheatSeeds;
            }
            // Fallback cho 1.12.2
            return com.hongminh54.storage.compatibility.MaterialCompatibility.getMaterialSafely("SEEDS");
        }
        // Carrot crops
        else if (typeName.equals("CARROTS") || typeName.equals("CARROT")) {
            // Carrot seed chính là carrot item
            Material carrotItem = com.hongminh54.storage.compatibility.MaterialCompatibility.getMaterialSafely("CARROT");
            if (carrotItem != null) {
                return carrotItem;
            }
            return Material.CARROT; // Fallback
        }
        // Potato crops
        else if (typeName.equals("POTATOES") || typeName.equals("POTATO")) {
            // Potato seed chính là potato item
            Material potatoItem = com.hongminh54.storage.compatibility.MaterialCompatibility.getMaterialSafely("POTATO");
            if (potatoItem != null) {
                return potatoItem;
            }
            return Material.POTATO; // Fallback
        }
        // Beetroot crops
        else if (typeName.equals("BEETROOTS") || typeName.equals("BEETROOT_BLOCK")) {
            // Beetroot chỉ có từ 1.9+
            return com.hongminh54.storage.compatibility.MaterialCompatibility.getMaterialSafely("BEETROOT_SEEDS");
        }
        // Other crops
        else if (typeName.equals("NETHER_WART")) {
            return Material.NETHER_WART;
        } else if (typeName.equals("COCOA")) {
            Material cocoaBeans = com.hongminh54.storage.compatibility.MaterialCompatibility.getMaterialSafely("COCOA_BEANS");
            if (cocoaBeans != null) {
                return cocoaBeans;
            }
            // Fallback cho 1.12.2
            return com.hongminh54.storage.compatibility.MaterialCompatibility.getMaterialSafely("INK_SACK");
        } else if (typeName.equals("PUMPKIN_STEM")) {
            Material pumpkinSeeds = com.hongminh54.storage.compatibility.MaterialCompatibility.getMaterialSafely("PUMPKIN_SEEDS");
            if (pumpkinSeeds != null) {
                return pumpkinSeeds;
            }
            return Material.PUMPKIN_SEEDS; // Fallback
        } else if (typeName.equals("MELON_STEM")) {
            Material melonSeeds = com.hongminh54.storage.compatibility.MaterialCompatibility.getMaterialSafely("MELON_SEEDS");
            if (melonSeeds != null) {
                return melonSeeds;
            }
            return Material.MELON_SEEDS; // Fallback
        }

        return null;
    }

    /**
     * Lấy loại crop block tương ứng với seed type
     * Tương thích đa phiên bản 1.12.2 - 1.21.x
     *
     * @param seedType Loại hạt giống
     * @return Material của crop block
     */
    private static Material getCropType(Material seedType) {
        String seedName = seedType.name();

        // Wheat seeds
        if (seedName.equals("WHEAT_SEEDS") || seedName.equals("SEEDS")) {
            // MC 1.13+ sử dụng WHEAT, MC 1.12.2 sử dụng CROPS
            Material wheatCrop = com.hongminh54.storage.compatibility.MaterialCompatibility.getMaterialSafely("WHEAT");
            if (wheatCrop != null) {
                return wheatCrop;
            }
            return com.hongminh54.storage.compatibility.MaterialCompatibility.getMaterialSafely("CROPS");
        }
        // Carrot
        else if (seedName.equals("CARROT")) {
            Material carrotCrop = com.hongminh54.storage.compatibility.MaterialCompatibility.getMaterialSafely("CARROTS");
            if (carrotCrop != null) {
                return carrotCrop;
            }
            return com.hongminh54.storage.compatibility.MaterialCompatibility.getMaterialSafely("CARROT");
        }
        // Potato
        else if (seedName.equals("POTATO")) {
            Material potatoCrop = com.hongminh54.storage.compatibility.MaterialCompatibility.getMaterialSafely("POTATOES");
            if (potatoCrop != null) {
                return potatoCrop;
            }
            return com.hongminh54.storage.compatibility.MaterialCompatibility.getMaterialSafely("POTATO");
        }
        // Beetroot
        else if (seedName.equals("BEETROOT_SEEDS")) {
            Material beetrootCrop = com.hongminh54.storage.compatibility.MaterialCompatibility.getMaterialSafely("BEETROOTS");
            if (beetrootCrop != null) {
                return beetrootCrop;
            }
            return com.hongminh54.storage.compatibility.MaterialCompatibility.getMaterialSafely("BEETROOT_BLOCK");
        }
        // Nether Wart
        else if (seedName.equals("NETHER_WART")) {
            return Material.NETHER_WART; // Seed và crop giống nhau
        }
        // Pumpkin Seeds
        else if (seedName.equals("PUMPKIN_SEEDS")) {
            return com.hongminh54.storage.compatibility.MaterialCompatibility.getMaterialSafely("PUMPKIN_STEM");
        }
        // Melon Seeds
        else if (seedName.equals("MELON_SEEDS")) {
            return com.hongminh54.storage.compatibility.MaterialCompatibility.getMaterialSafely("MELON_STEM");
        }
        // Cocoa Beans
        else if (seedName.equals("COCOA_BEANS") || seedName.equals("INK_SACK")) {
            return Material.COCOA;
        }

        return null;
    }

    /**
     * Xử lý phù phép Đất Màu Mỡ (Fertile Soil)
     *
     * @param player Người chơi
     * @param block  Block cây trồng bị phá
     * @param level  Cấp độ phù phép
     * @return true nếu thành công, false nếu thất bại
     */
    public static boolean handleFertileSoil(Player player, Block block, int level) {
        if (!checkCooldown(player, FERTILE_SOIL)) {
            return false;
        }

        // Kiểm tra xem block bên dưới có phải là đất màu mỡ không
        Block soilBlock = block.getRelative(BlockFace.DOWN);
        if (!isSoil(soilBlock)) {
            return false;
        }

        // Tính tỷ lệ thành công dựa vào cấp độ phù phép
        int chanceToUpgrade = 30 + (level * 15); // 45/60/75% tùy cấp độ
        if (random.nextInt(100) >= chanceToUpgrade) {
            return false;
        }

        // Tìm các cây trồng lân cận để đẩy nhanh quá trình phát triển
        int radius = level; // Phạm vi tăng theo cấp độ phù phép
        int upgraded = 0;
        int maxBlocksToProcess = File.getEnchants().getInt("hoe_enchant.fertile_soil.max_blocks_per_use", 16); // Giới hạn 16 block để tránh lag

        for (int x = -radius; x <= radius; x++) {
            for (int z = -radius; z <= radius; z++) {
                if (x == 0 && z == 0) continue; // Bỏ qua block hiện tại

                // Giới hạn số block được xử lý để tránh lag
                if (upgraded >= maxBlocksToProcess) {
                    break;
                }

                Block nearbyBlock = block.getRelative(x, 0, z);

                // Kiểm tra xem block lân cận có phải là cây trồng không
                if (!isCrop(nearbyBlock)) {
                    continue;
                }

                // Kiểm tra xem cây trồng đã chín chưa
                if (isMatureCrop(nearbyBlock)) {
                    continue;
                }

                // Đối với phiên bản từ 1.13 trở lên, sử dụng BlockData
                if (nmsAssistant.isVersionGreaterThanOrEqualTo(13)) {
                    try {
                        // Sử dụng reflection để gọi phương thức BlockData một cách an toàn
                        Class<?> blockDataClass = Class.forName("org.bukkit.block.data.BlockData");
                        Class<?> ageableClass = Class.forName("org.bukkit.block.data.Ageable");

                        Object blockData = nearbyBlock.getClass().getMethod("getBlockData").invoke(nearbyBlock);

                        if (ageableClass.isInstance(blockData)) {
                            int currentAge = (int) ageableClass.getMethod("getAge").invoke(blockData);
                            int maxAge = (int) ageableClass.getMethod("getMaximumAge").invoke(blockData);

                            if (currentAge < maxAge) {
                                int newAge = Math.min(currentAge + level, maxAge);
                                ageableClass.getMethod("setAge", int.class).invoke(blockData, newAge);
                                nearbyBlock.getClass().getMethod("setBlockData", blockDataClass).invoke(nearbyBlock, blockData);
                                upgraded++;
                            }
                        }
                    } catch (Exception e) {
                        if (Storage.getStorage().isDebug()) {
                            Storage.getStorage().getLogger().warning("Lỗi khi tăng tuổi cây trồng: " + e.getMessage());
                        }
                    }
                } else {
                    // Đối với phiên bản 1.12.2, sử dụng byte data
                    byte currentData = 0;
                    byte maxData = 0;

                    try {
                        // Sử dụng reflection để gọi phương thức getData an toàn
                        currentData = (byte) nearbyBlock.getClass().getMethod("getData").invoke(nearbyBlock);
                        maxData = getCropMaxStage(nearbyBlock.getType());

                        if (currentData < maxData) {
                            byte newData = (byte) Math.min(currentData + level, maxData);
                            // Sử dụng reflection để gọi phương thức setData an toàn
                            nearbyBlock.getClass().getMethod("setData", byte.class).invoke(nearbyBlock, newData);
                            upgraded++;
                        }
                    } catch (Exception e) {
                        if (Storage.getStorage().isDebug()) {
                            Storage.getStorage().getLogger().warning("Lỗi khi cập nhật data cho block: " + e.getMessage());
                        }
                    }
                }
            }

            // Break vòng lặp ngoài nếu đã đạt giới hạn
            if (upgraded >= maxBlocksToProcess) {
                break;
            }
        }

        if (Storage.getStorage().isDebug()) {
            Storage.getStorage().getLogger().info("HoeEnchant: Đã tăng trưởng " + upgraded + " cây trồng xung quanh");
        }

        // Set cooldown chỉ khi có ít nhất 1 cây được tăng trưởng
        if (upgraded > 0) {
            setCooldown(player, FERTILE_SOIL);
        }

        return upgraded > 0;
    }

    /**
     * Lấy giai đoạn phát triển tối đa của cây trồng
     *
     * @param cropType Loại cây trồng
     * @return Giá trị data của giai đoạn phát triển tối đa
     */
    private static byte getCropMaxStage(Material cropType) {
        String typeName = cropType.name();

        if (typeName.equals("CROPS") || typeName.equals("WHEAT") ||
                typeName.equals("CARROTS") || typeName.equals("CARROT") ||
                typeName.equals("POTATOES") || typeName.equals("POTATO") ||
                typeName.equals("PUMPKIN_STEM") || typeName.equals("MELON_STEM")) {
            return 7;
        } else if (typeName.equals("BEETROOTS") || typeName.equals("BEETROOT_BLOCK") ||
                typeName.equals("NETHER_WART")) {
            return 3;
        } else if (typeName.equals("COCOA")) {
            return 8; // Cocoa có bit thứ 4 được set khi trưởng thành
        }

        return 7; // Mặc định cho các loại khác
    }

    /**
     * Chuyển đổi số thành chữ số La Mã
     *
     * @param number Số cần chuyển đổi
     * @return Chuỗi chữ số La Mã
     */
    private static String getRomanNumeral(int number) {
        switch (number) {
            case 1:
                return "I";
            case 2:
                return "II";
            case 3:
                return "III";
            case 4:
                return "IV";
            case 5:
                return "V";
            default:
                return String.valueOf(number);
        }
    }
} 