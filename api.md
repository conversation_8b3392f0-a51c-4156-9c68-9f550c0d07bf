# Storage API

This is the official API for the Storage plugin, providing methods to interact with the ore storage system.

## How to Use

### 1. Add the plugin as a dependency in your plugin.yml

```yaml
depend: [Storage]
```

or

```yaml
softdepend: [Storage]
```

### 2. Use the API in your code

```java
import com.hongminh54.storage.API.StorageAPI;
import org.bukkit.entity.Player;

public class YourPlugin extends JavaPlugin {
    @Override
    public void onEnable() {
        // Check if API has been initialized
        if (!StorageAPI.isInitialized()) {
            getLogger().warning("Storage API has not been initialized!");
            return;
        }
        
        // Now you can use the API
    }
    
    // Example: Method to check player's ore amount
    public int getPlayerOreAmount(Player player, String material) {
        return StorageAPI.getPlayerResourceAmount(player, material);
    }
    
    // Example: Method to add ores to player's storage
    public boolean addOreToPlayerStorage(Player player, String material, int amount) {
        return StorageAPI.addPlayerResource(player, material, amount);
    }
}
```

## API Methods

### Resource Management

- `getPlayerResourceAmount(Player player, String material)` - Get player's resource amount
- `addPlayerResource(Player player, String material, int amount)` - Add resources to storage
- `removePlayerResource(Player player, String material, int amount)` - Remove resources from storage
- `isStorageFull(Player player)` - Check if storage is full
- `isResourceFull(Player player, String material)` - Check if storage for a specific resource is full
- `getStorageLimit(Player player)` - Get player's storage limit
- `getAllPlayerResources(Player player)` - Get all player's resources
- `transferResource(Player sender, Player receiver, String material, int amount)` - Transfer resources between players

### Statistics

- `getTotalMined(Player player)` - Get total mined resources
- `getTotalDeposited(Player player)` - Get total deposited resources
- `getTotalWithdrawn(Player player)` - Get total withdrawn resources
- `getTotalSold(Player player)` - Get total sold resources
- `getStatsInfo(Player player)` - Get statistics information list

### Configuration

- `getSupportedMaterials()` - Get list of all supported resource types

### Auto Pickup

- `isAutoPickupEnabled(Player player)` - Check if auto pickup is enabled
- `setAutoPickup(Player player, boolean enabled)` - Set auto pickup status

### Data Storage

- `savePlayerStorage(Player player)` - Save player's storage data
- `reloadPlayerStorage(Player player)` - Reload player's storage data

### Block Checking

- `isValidOreBlock(Block block)` - Check if a block is a valid ore block

### Block Conversion

- `canConvertToBlock(String material)` - Check if material can be converted to block
- `canConvertToIngot(String blockMaterial)` - Check if block can be converted to ingots
- `getBlockMaterial(String material)` - Get corresponding block material name
- `getIngotMaterial(String blockMaterial)` - Get corresponding ingot material name
- `convertToBlock(Player player, String material, int amount)` - Convert ingots to blocks
- `convertToIngot(Player player, String blockMaterial, int amount)` - Convert blocks to ingots
- `getPossibleBlocksToCreate(Player player, String material)` - Get number of blocks that can be created
- `getPossibleIngotsToCreate(Player player, String blockMaterial)` - Get number of ingots that can be created
- `canPerformConversion(Player player, String inputMaterial, String outputMaterial, int inputAmount, int outputAmount)` - Check if conversion is possible
- `getConversionRatio()` - Get ingots per block ratio (default: 9)

## ConversionResult Class

The `ConversionResult` class is returned by conversion methods and contains:

- `isSuccess()` - Returns true if conversion was successful
- `getMessage()` - Returns result message
- `getInputUsed()` - Returns amount of input materials used
- `getOutputCreated()` - Returns amount of output materials created

## Extended Examples

### Block Conversion Example

```java
public class ConversionExample {
    // Convert ingots to blocks with detailed result
    public void convertPlayerIngots(Player player, String material, int amount) {
        // Check if conversion is possible first
        StorageAPI.ConversionResult checkResult = StorageAPI.canPerformConversion(
            player, material, StorageAPI.getBlockMaterial(material), amount, amount / 9);

        if (!checkResult.isSuccess()) {
            player.sendMessage("§cCannot convert: " + checkResult.getMessage());
            return;
        }

        // Perform the conversion
        StorageAPI.ConversionResult result = StorageAPI.convertToBlock(player, material, amount);

        if (result.isSuccess()) {
            player.sendMessage(String.format("§aSuccessfully converted %d %s into %d blocks!",
                result.getInputUsed(), material, result.getOutputCreated()));
        } else {
            player.sendMessage("§cConversion failed: " + result.getMessage());
        }
    }

    // Convert all possible ingots to blocks
    public void convertAllToBlocks(Player player, String material) {
        int possibleBlocks = StorageAPI.getPossibleBlocksToCreate(player, material);

        if (possibleBlocks <= 0) {
            player.sendMessage("§cNo blocks can be created from your " + material);
            return;
        }

        // Convert all possible (-1 means convert all)
        StorageAPI.ConversionResult result = StorageAPI.convertToBlock(player, material, -1);

        if (result.isSuccess()) {
            player.sendMessage(String.format("§aConverted all ingots into %d blocks!",
                result.getOutputCreated()));
        } else {
            player.sendMessage("§cConversion failed: " + result.getMessage());
        }
    }

    // Get conversion information
    public void showConversionInfo(Player player, String material) {
        if (!StorageAPI.canConvertToBlock(material)) {
            player.sendMessage("§c" + material + " cannot be converted to blocks");
            return;
        }

        int currentAmount = StorageAPI.getPlayerResourceAmount(player, material);
        int possibleBlocks = StorageAPI.getPossibleBlocksToCreate(player, material);
        int ratio = StorageAPI.getConversionRatio();

        player.sendMessage("§e=== Conversion Info ===");
        player.sendMessage("§fMaterial: §b" + material);
        player.sendMessage("§fCurrent amount: §e" + currentAmount);
        player.sendMessage("§fPossible blocks: §a" + possibleBlocks);
        player.sendMessage("§fConversion ratio: §6" + ratio + " ingots = 1 block");

        String blockMaterial = StorageAPI.getBlockMaterial(material);
        if (blockMaterial != null) {
            player.sendMessage("§fBlock type: §d" + blockMaterial);
        }
    }
}
```

### Economy Integration

```java
public class EconomyIntegration {
    // Method to sell all player's resources
    public void sellAllResources(Player player, Economy economy) {
        // Get all player's resources
        Map<String, Integer> resources = StorageAPI.getAllPlayerResources(player);
        double totalMoney = 0.0;
        
        // Assuming you have a price map for resources
        Map<String, Double> prices = getPrices();
        
        // Process each resource type
        for (Map.Entry<String, Integer> entry : resources.entrySet()) {
            String material = entry.getKey();
            int amount = entry.getValue();
            
            if (amount > 0 && prices.containsKey(material)) {
                double price = prices.get(material);
                double money = amount * price;
                
                // Remove resources from storage
                if (StorageAPI.removePlayerResource(player, material, amount)) {
                    // Add money to player
                    economy.depositPlayer(player, money);
                    totalMoney += money;
                }
            }
        }
        
        player.sendMessage("§aYou have sold all resources and received §e" + totalMoney + " coins");
    }

    // Method to sell with conversion option
    public void sellWithConversion(Player player, String material, boolean convertToBlocks) {
        if (convertToBlocks && StorageAPI.canConvertToBlock(material)) {
            // Convert to blocks first for better price
            StorageAPI.ConversionResult result = StorageAPI.convertToBlock(player, material, -1);
            if (result.isSuccess()) {
                player.sendMessage("§aConverted " + result.getInputUsed() + " " + material +
                    " into " + result.getOutputCreated() + " blocks for better selling price!");

                // Now sell the blocks
                String blockMaterial = StorageAPI.getBlockMaterial(material);
                if (blockMaterial != null) {
                    int blockAmount = StorageAPI.getPlayerResourceAmount(player, blockMaterial);
                    // Sell blocks at higher price...
                }
            }
        } else {
            // Sell ingots directly
            int amount = StorageAPI.getPlayerResourceAmount(player, material);
            // Sell ingots...
        }
    }

    // Method to get resource prices
    private Map<String, Double> getPrices() {
        Map<String, Double> prices = new HashMap<>();
        prices.put("DIAMOND", 50.0);
        prices.put("EMERALD", 60.0);
        prices.put("GOLD_ORE", 20.0);
        prices.put("IRON_ORE", 10.0);
        // Add other values...
        return prices;
    }
}
```

### Shop Integration

```java
public class ShopIntegration {
    // Method to display storage GUI
    public void openStorageGUI(Player player) {
        // Get all player's resources
        Map<String, Integer> resources = StorageAPI.getAllPlayerResources(player);
        
        // Get storage limit
        int maxStorage = StorageAPI.getStorageLimit(player);
        
        // Create GUI with storage info
        InventoryGUI gui = new InventoryGUI("Resource Storage", 54);
        
        // Display resources and amounts
        int slot = 0;
        for (Map.Entry<String, Integer> entry : resources.entrySet()) {
            String material = entry.getKey();
            int amount = entry.getValue();
            
            // Create item for GUI
            ItemStack item = createItemForGUI(material, amount, maxStorage);
            gui.setItem(slot++, item);
        }
        
        // Show GUI to player
        gui.open(player);
    }
    
    // Method to create GUI items
    private ItemStack createItemForGUI(String material, int amount, int maxStorage) {
        // Create item based on resource type
        // ...
        return null; // Just an example
    }
}
```

## Notes

- This API only works when the Storage plugin has been loaded and initialized.
- Always check if the API is initialized using the `isInitialized()` method before using it.
- The conversion methods now return `ConversionResult` objects instead of simple boolean values for better error handling and information.
- Block conversion follows Minecraft vanilla ratios: 9 ingots = 1 block.
- Supported materials include: IRON, GOLD, DIAMOND, EMERALD, COAL, REDSTONE, LAPIS_LAZULI, COPPER, NETHERITE, QUARTZ, AMETHYST_SHARD, and RAW ores (1.17+).
- All conversion methods include proper validation and rollback mechanisms to prevent data loss.
- Use `-1` as amount parameter in conversion methods to convert all available materials.
- If you encounter any issues while using the API, please contact me in discord or facebook.

## Version Compatibility

- **Minecraft 1.12.2 - 1.21.x**: Full support with automatic version detection
- **Material Format**: Supports both modern names (1.13+) and legacy format with data values (1.12.2)
- **New Materials**: Automatic support for newer materials like Copper (1.17+), Netherite (1.16+), etc.
