# C<PERSON><PERSON> hình <PERSON>UI xem kho của người chơi khác

title: "&#8B4513Kho của: &#32CD32{player} &#FFD700{status}"
# Kích thước của giao diện: 1,2,3,4,5,6
size: 6

# C<PERSON>u hình âm thanh
sounds:
  # Âm thanh khi mở giao diện
  open: "BLOCK_CHEST_OPEN:0.5:1.0"
  # Âm thanh khi đóng giao diện
  close: "BLOCK_CHEST_CLOSE:0.5:1.0"
  # Âm thanh khi click vào nút
  click: "UI_BUTTON_CLICK:0.5:1.0"

items:
  # Vật phẩm trang trí
  decorates:
    # Các slot viền (hàng trên, d<PERSON><PERSON><PERSON>, tr<PERSON><PERSON>, ph<PERSON><PERSON>)
    slot: 0, 1, 2, 3, 5, 6, 7, 8, 9, 17, 18, 26, 27, 35, 36, 44, 45, 46, 47, 48, 50, 51, 52, 53
    # Tê<PERSON> hiển thị
    name: "&7 "
    # Material (tương thích 1.12.2+)
    material: GRAY_STAINED_GLASS_PANE
    # <PERSON><PERSON> tả của vật phẩm
    lore:
      - "&7 "
    # Số lượng vật phẩm
    amount: 1
    # Dành cho 1.14+ nếu máy chủ của bạn có hỗ trợ rsp
    custom-model-data: 1
    # Vật phẩm sẽ không bị phá hủy?
    unbreakable: true
    # Enchant cho vật phẩm
    enchants:
      DURABILITY: 1
    # Flag cho vật phẩm | Nếu sử dụng ALL: true -> Tất cả flag sẽ được áp dụng cho vật phẩm
    flags:
      ALL: true

  # Thông tin người chơi (đầu người chơi)
  player_info:
    # Slot hiển thị đầu người chơi
    slot: 4
    name: "&a{player}"
    # Material sẽ được set động thành PLAYER_HEAD
    material: PLAYER_HEAD
    lore:
      - "&7Trạng thái: {status}"
      - "&7Giới hạn kho: &e{max_storage}"
      - "&7 "
      - "&eXem kho tài nguyên của người chơi này"
    amount: 1
    custom-model-data: 1
    unbreakable: true
    enchants:
      DURABILITY: 1
    flags:
      ALL: true

  # Nút đóng GUI
  close_button:
    # Slot nút đóng
    slot: 49
    name: "&c&lĐóng"
    material: BARRIER
    lore:
      - "&7Click để đóng giao diện"
    amount: 1
    custom-model-data: 1
    unbreakable: true
    flags:
      ALL: true

  # Khu vực hiển thị tài nguyên
  resource_area:
    # Các slot để hiển thị tài nguyên (từ slot 10-16, 19-25, 28-34, 37-43)
    slots: 10, 11, 12, 13, 14, 15, 16, 19, 20, 21, 22, 23, 24, 25, 28, 29, 30, 31, 32, 33, 34, 37, 38, 39, 40, 41, 42, 43
    # Template cho hiển thị tài nguyên
    resource_template:
      name: "&a{display_name}"
      lore:
        - "&7Số lượng: &e{amount}"
      amount: 1
      custom-model-data: 1
      unbreakable: true
      enchants:
        DURABILITY: 1
      flags:
        ALL: true

  # Vật phẩm hiển thị khi không có tài nguyên
  no_resources:
    # Slot hiển thị (giữa GUI)
    slot: 22
    name: "&cKhông có tài nguyên"
    material: BARRIER
    lore:
      - "&7Người chơi này chưa có"
      - "&7tài nguyên nào trong kho"
    amount: 1
    custom-model-data: 1
    unbreakable: true
    enchants:
      DURABILITY: 1
    flags:
      ALL: true

# Cấu hình hiển thị trạng thái
status:
  online: "&2[Online]"
  offline: "&c[Offline]"

# Cấu hình thông báo lỗi
error_messages:
  player_not_found: "&cKhông tìm thấy dữ liệu kho của người chơi &e{player}"
  no_data: "&cKhông có dữ liệu để hiển thị"
