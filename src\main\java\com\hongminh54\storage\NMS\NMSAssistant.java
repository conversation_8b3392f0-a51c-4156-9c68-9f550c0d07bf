package com.hongminh54.storage.NMS;

/**
 * Lớp hỗ trợ kiểm tra phiên bản NMS cho Minecraft 1.16.5 - 1.21.x
 */
public class NMSAssistant {

    /**
     * Method to get the NMS Version which stands for the current server-version.
     *
     * @return {@link NMSVersion}.
     */
    public NMSVersion getNMSVersion() {
        return new NMSVersion();
    }

    /**
     * Kiểm tra xem phiên bản máy chủ có lớn hơn phiên bản cho trước không.
     *
     * @param version phiên bản cần kiểm tra.
     * @return {@code true} nếu lớn hơn.
     */
    public boolean isVersionGreaterThan(int version) {
        return getNMSVersion().getMinor() > version;
    }

    /**
     * Kiểm tra xem phiên bản máy chủ có lớn hơn hoặc bằng phiên bản cho trước không.
     *
     * @param version phiên bản cần kiểm tra.
     * @return {@code true} nếu lớn hơn hoặc bằng.
     */
    public boolean isVersionGreaterThanOrEqualTo(int version) {
        return getNMSVersion().getMinor() >= version;
    }

    /**
     * Kiểm tra xem phiên bản máy chủ có nhỏ hơn phiên bản cho trước không.
     *
     * @param version phiên bản cần kiểm tra.
     * @return {@code true} nếu nhỏ hơn.
     */
    public boolean isVersionLessThan(int version) {
        return getNMSVersion().getMinor() < version;
    }

    /**
     * Kiểm tra xem phiên bản máy chủ có nhỏ hơn hoặc bằng phiên bản cho trước không.
     *
     * @param version phiên bản cần kiểm tra.
     * @return {@code true} nếu nhỏ hơn hoặc bằng.
     */
    public boolean isVersionLessThanOrEqualTo(int version) {
        return getNMSVersion().getMinor() <= version;
    }

    /**
     * Kiểm tra xem máy chủ có đang chạy phiên bản cụ thể này không.
     *
     * @param version phiên bản cần kiểm tra.
     * @return {@code true} nếu đúng.
     */
    public boolean isVersion(int version) {
        return getNMSVersion().getMinor() == version;
    }

    /**
     * Kiểm tra xem máy chủ có đang không chạy phiên bản cụ thể này không.
     *
     * @param version phiên bản cần kiểm tra.
     * @return {@code true} nếu không phải.
     */
    public boolean isNotVersion(int version) {
        return getNMSVersion().getMinor() != version;
    }

    /**
     * Kiểm tra xem phiên bản máy chủ có nằm trong khoảng 1.12.2 - 1.25.x không.
     *
     * @return {@code true} nếu phiên bản được hỗ trợ.
     */
    public boolean isSupportedVersion() {
        return getNMSVersion().isSupported();
    }

    /**
     * Kiểm tra xem có phải phiên bản 1.21.4 trở lên không.
     *
     * @return {@code true} nếu là 1.21.4+.
     */
    public boolean is1_21_4OrHigher() {
        return getNMSVersion().is1_21_4OrHigher();
    }

    /**
     * Kiểm tra xem có phải phiên bản 1.20.5 trở lên không.
     *
     * @return {@code true} nếu là 1.20.5+.
     */
    public boolean is1_20_5OrHigher() {
        return getNMSVersion().is1_20_5OrHigher();
    }

    /**
     * Kiểm tra phiên bản với revision cụ thể.
     *
     * @param major    phiên bản major (thường là 1).
     * @param minor    phiên bản minor (12, 13, 14, ..., 21).
     * @param revision phiên bản revision (0, 1, 2, ...).
     * @return {@code true} nếu phiên bản hiện tại >= phiên bản chỉ định.
     */
    public boolean isVersionGreaterThanOrEqualTo(int major, int minor, int revision) {
        NMSVersion current = getNMSVersion();
        if (current.getMajor() > major) return true;
        if (current.getMajor() < major) return false;
        if (current.getMinor() > minor) return true;
        if (current.getMinor() < minor) return false;
        return current.getRevision() >= revision;
    }
}