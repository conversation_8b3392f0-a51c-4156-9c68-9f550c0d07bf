package com.hongminh54.storage.CMD.commands;

import com.hongminh54.storage.GUI.ConvertBlockGUI;
import com.hongminh54.storage.Manager.MineManager;
import com.hongminh54.storage.Storage;
import com.hongminh54.storage.Utils.Chat;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

public class DoiBlockCommand implements IStorageCommand {

    @Override
    public boolean execute(CommandSender sender, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(Chat.colorize("&cLệnh này chỉ có thể được sử dụng bởi người chơi."));
            return true;
        }

        Player player = (Player) sender;

        try {
            // Log để debug
            Storage.getStorage().getLogger().info("Người chơi " + player.getName() + " đang mở GUI đổi block");

            // Kiểm tra xem có dữ liệu người chơi không
            if (!MineManager.hasPlayerBlock(player, "COAL_ORE") &&
                    !MineManager.hasPlayerBlock(player, "IRON_ORE") &&
                    !MineManager.hasPlayerBlock(player, "GOLD_ORE") &&
                    !MineManager.hasPlayerBlock(player, "DIAMOND_ORE")) {
                // Tải dữ liệu người chơi nếu chưa có
                MineManager.loadPlayerData(player);
            }

            // Mở giao diện đổi block
            player.openInventory(new ConvertBlockGUI(player).getInventory());

            // Gửi thông báo thành công
            player.sendMessage(Chat.colorize("&aĐã mở giao diện đổi block!"));

        } catch (Exception e) {
            // Xử lý ngoại lệ chi tiết hơn
            Storage.getStorage().getLogger().severe("Lỗi khi mở GUI đổi block cho " + player.getName() + ": " + e.getMessage());
            e.printStackTrace();

            player.sendMessage(Chat.colorize("&cĐã xảy ra lỗi khi mở giao diện đổi block!"));
            player.sendMessage(Chat.colorize("&cVui lòng thử lại sau hoặc liên hệ admin."));
            player.sendMessage(Chat.colorize("&7Lỗi: " + e.getMessage()));
        }

        return true;
    }

    @Override
    public List<String> getTabComplete(CommandSender sender, String[] args) {
        // Không cần tab complete cho lệnh này
        return new ArrayList<>();
    }

    @Override
    public String getCommandName() {
        return "doiblock";
    }

    @Override
    public List<String> getAliases() {
        return Collections.singletonList("convertblock");
    }

    @Override
    public boolean hasPermission(CommandSender sender) {
        return sender.hasPermission("storage.doiblock") ||
                sender.hasPermission("storage.convert") ||
                sender.hasPermission("storage.user") ||
                sender.hasPermission("storage.*") ||
                sender.isOp();
    }
}
