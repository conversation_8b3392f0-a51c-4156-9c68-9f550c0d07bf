title: "&#FF4500X<PERSON>c nhận chuyển đổi &8| &#4A90E2#player#"
# <PERSON><PERSON><PERSON> thước của giao diện: 1,2,3,4,5,6
size: 3

# Cấu hình âm thanh
sounds:
  # Âm thanh khi mở giao diện
  open: "BLOCK_CHEST_OPEN:0.5:1.0"
  # Âm thanh khi đóng giao diện
  close: "BLOCK_CHEST_CLOSE:0.5:1.0"
  # Âm thanh khi click vào nút
  click: "UI_BUTTON_CLICK:0.5:1.0"
  # Âm thanh khi xác nhận
  confirm: "BLOCK_ANVIL_USE:0.8:1.2"
  # Âm thanh khi hủy
  cancel: "UI_BUTTON_CLICK:0.5:1.0"

items:
  # Vật phẩm trang trí
  decorates:
    # <PERSON>ác slot trang trí
    slot: 0, 1, 2, 6, 7, 8, 9, 17, 18, 19, 20, 24, 25, 26
    # Tên hiển thị của vật phẩm
    name: "&7 "
    # Vật liệu cho 1.12.2+
    material: BLACK_STAINED_GLASS_PANE
    # Mô tả của vật phẩm
    lore: "&7 "
    # Số lượng vật phẩm
    amount: 1
    # Dành cho 1.14+ nếu máy chủ của bạn có hỗ trợ rsp
    custom-model-data: 1
    # Vật phẩm sẽ không bị phá hủy?
    unbreakable: true
    # Enchant cho vật phẩm
    enchants:
      DURABILITY: 1
    # Flag cho vật phẩm | Nếu sử dụng ALL: true -> Tất cả flag sẽ được áp dụng cho vật phẩm
    flags:
      ALL: true

  # Thông tin chuyển đổi
  conversion_info:
    slot: 13
    # Material sẽ được tự động lấy từ material đang chuyển đổi
    # Nếu muốn override, có thể set material cụ thể ở đây
    # material: PAPER
    name: "&eThông tin chuyển đổi"
    # Lore sẽ được tự động tạo dựa trên thông tin chuyển đổi
    amount: 1
    custom-model-data: 1
    unbreakable: true
    enchants:
      DURABILITY: 1
    flags:
      ALL: true

  # Nút xác nhận
  confirm:
    slot: 11
    name: "&a&lXÁC NHẬN"
    material: EMERALD_BLOCK
    lore: "&eClick để xác nhận chuyển đổi"
    amount: 1
    custom-model-data: 1
    unbreakable: true
    enchants:
      DURABILITY: 1
    flags:
      ALL: true

  # Nút hủy
  cancel:
    slot: 15
    name: "&c&lHỦY"
    material: BARRIER
    lore: "&eClick để hủy và quay lại"
    amount: 1
    custom-model-data: 1
    unbreakable: true
    enchants:
      DURABILITY: 1
    flags:
      ALL: true
