package com.hongminh54.storage.Manager;

import com.cryptomorin.xseries.XSound;
import com.hongminh54.storage.NMS.NMSAssistant;
import com.hongminh54.storage.Utils.File;
import com.hongminh54.storage.Wrapper.SoundWrapper;
import org.bukkit.Location;
import org.bukkit.Sound;
import org.bukkit.entity.Player;

/**
 * Lớp quản lý âm thanh cho Minecraft 1.12.2 - 1.21.x
 * Sử dụng XSound và SoundWrapper để đảm bảo tương thích tối đa
 */
public class SoundManager {

    private static final boolean IS_PRE_113 = new NMSAssistant().isVersionLessThan(13);

    // C<PERSON>c phương thức chuyển đổi đã đư<PERSON><PERSON> chuyển sang SoundCompatibility

    // Các phương thức xử lý Sound enum đã đượ<PERSON> chuyển sang SoundCompatibility

    /**
     * Phát âm thanh sử dụng XSound (<PERSON><PERSON><PERSON><PERSON>n nghị cho cross-version compatibility)
     *
     * @param player Ng<PERSON><PERSON><PERSON> chơi nghe âm thanh
     * @param xSound XSound enum
     * @param volume Âm lượng
     * @param pitch  Cao độ
     */
    public static void playSound(Player player, XSound xSound, float volume, float pitch) {
        if (player == null || xSound == null) {
            return;
        }

        // Đảm bảo giá trị hợp lệ
        volume = Math.max(0.0f, Math.min(2.0f, volume));
        pitch = Math.max(0.5f, Math.min(2.0f, pitch));

        try {
            xSound.play(player, volume, pitch);
        } catch (Exception e) {
            // Fallback với SoundWrapper
            SoundWrapper.playDefaultSound(player, volume, pitch);
        }
    }

    /**
     * Phát âm thanh cho người chơi với cải thiện xử lý
     *
     * @param player    Người chơi nghe âm thanh
     * @param soundName Tên âm thanh
     * @param volume    Âm lượng
     * @param pitch     Cao độ
     */
    public static void playSound(Player player, String soundName, float volume, float pitch) {
        if (player == null || soundName == null || soundName.isEmpty()) {
            return;
        }

        // Đảm bảo giá trị hợp lệ
        volume = Math.max(0.0f, Math.min(2.0f, volume));
        pitch = Math.max(0.5f, Math.min(2.0f, pitch));

        // Sử dụng SoundCompatibility với logic cải thiện
        com.hongminh54.storage.compatibility.SoundCompatibility.playSoundByName(player, soundName, volume, pitch);
    }

    /**
     * Phát âm thanh cho người chơi - Cải thiện cho 1.12.2
     *
     * @param player Người chơi nghe âm thanh
     * @param sound  Enum Sound (deprecated - sử dụng string thay thế)
     * @param volume Âm lượng
     * @param pitch  Cao độ
     * @deprecated Sử dụng playSound(Player, String, float, float) thay thế để tránh IncompatibleClassChangeError
     */
    @Deprecated
    public static void playSound(Player player, Sound sound, float volume, float pitch) {
        if (player == null || sound == null) {
            return;
        }

        try {
            // Sử dụng SoundWrapper thay vì Sound enum trực tiếp
            SoundWrapper.playSound(player, sound.name(), volume, pitch);
        } catch (Exception e) {
            // Fallback với SoundCompatibility
            com.hongminh54.storage.compatibility.SoundCompatibility.playSound(player, sound.name(), volume, pitch);
        }
    }

    /**
     * Phát âm thanh tại vị trí cụ thể
     *
     * @param location  Vị trí phát âm thanh
     * @param soundName Tên âm thanh
     * @param volume    Âm lượng
     * @param pitch     Cao độ
     */
    public static void playSound(Location location, String soundName, float volume, float pitch) {
        if (location == null || location.getWorld() == null || soundName == null || soundName.isEmpty()) {
            return;
        }

        try {
            // Sử dụng SoundCompatibilityV2 để phát âm thanh an toàn
            try {
                location.getWorld().playSound(location, soundName, volume, pitch);
            } catch (Exception e1) {
                // Thử với tên âm thanh đã chuyển đổi
                String convertedName = convertSoundNameForLocation(soundName);
                try {
                    location.getWorld().playSound(location, convertedName, volume, pitch);
                } catch (Exception e2) {
                    // Bỏ qua lỗi nhưng ghi log
                    System.out.println("Không thể phát âm thanh tại vị trí: " + soundName);
                }
            }
        } catch (Exception e) {
            // Bỏ qua lỗi
            System.out.println("Lỗi khi phát âm thanh: " + e.getMessage());
        }
    }

    /**
     * Phát âm thanh từ chuỗi cấu hình với cải thiện xử lý lỗi
     * Ví dụ: "ENTITY_VILLAGER_NO:1.0:1.0" hoặc "NOTE_PLING:0.5:1.2"
     *
     * @param player      Người chơi
     * @param soundConfig Chuỗi cấu hình âm thanh dạng "TÊN_ÂM_THANH:VOLUME:PITCH"
     */
    public static void playSoundFromConfig(Player player, String soundConfig) {
        if (player == null || soundConfig == null || soundConfig.isEmpty()) {
            return;
        }

        // Kiểm tra xem effects có được bật không
        if (!File.getConfig().getBoolean("effects.enabled", true)) {
            return;
        }

        // Ưu tiên sử dụng SoundWrapper
        if (SoundWrapper.playSoundFromConfig(player, soundConfig)) {
            return; // Thành công
        }

        try {
            // Fallback với SoundCompatibility
            com.hongminh54.storage.compatibility.SoundCompatibility.playSoundFromConfig(player, soundConfig);
        } catch (Exception e) {
            // Fallback cuối cùng với SoundWrapper
            try {
                String defaultSound = SoundWrapper.getDefaultSoundName();
                SoundWrapper.playSound(player, defaultSound, 0.5f, 1.0f);
            } catch (Exception ignored) {
                // Bỏ qua lỗi cuối cùng
            }
        }
    }

    /**
     * Phát âm thanh GUI với kiểm tra effects enabled
     * Tránh phát âm thanh trùng lặp với main effects
     *
     * @param player      Người chơi
     * @param soundConfig Chuỗi cấu hình âm thanh
     * @param soundType   Loại âm thanh (open, close, click, etc.)
     */
    public static void playGUISound(Player player, String soundConfig, String soundType) {
        if (player == null || soundConfig == null || soundConfig.isEmpty()) {
            return;
        }

        // Kiểm tra xem effects có được bật không
        if (!File.getConfig().getBoolean("effects.enabled", true)) {
            return;
        }

        // Chỉ phát âm thanh GUI nếu không trùng với main effects
        playSoundFromConfig(player, soundConfig);
    }

    /**
     * Phát âm thanh effect từ config
     *
     * @param player     Người chơi nghe âm thanh
     * @param effectType Loại effect (storage_open, transfer_success, collect, etc.)
     */
    public static void playEffectSound(Player player, String effectType) {
        if (player == null || effectType == null || effectType.isEmpty()) {
            return;
        }

        try {
            // Lấy sound config từ effects section
            String soundConfig = com.hongminh54.storage.Utils.File.getConfig()
                    .getString("effects." + effectType + ".sound");

            if (soundConfig != null && !soundConfig.isEmpty()) {
                playSoundFromConfig(player, soundConfig);
            } else {
                // Fallback với sound mặc định cho từng effect type
                String fallbackSound = getFallbackSoundForEffect(effectType);
                if (fallbackSound != null) {
                    playSoundFromConfig(player, fallbackSound);
                }
            }
        } catch (Exception e) {
            // Bỏ qua lỗi
            if (com.hongminh54.storage.Storage.getStorage().isDebug()) {
                System.out.println("Lỗi khi phát effect sound: " + effectType + " - " + e.getMessage());
            }
        }
    }

    /**
     * Lấy sound fallback cho effect type
     *
     * @param effectType Loại effect
     * @return Sound config fallback
     */
    private static String getFallbackSoundForEffect(String effectType) {
        switch (effectType.toLowerCase()) {
            case "collect":
                return "ENTITY_ITEM_PICKUP:0.2:0.8";
            case "transfer_success":
                return "ENTITY_PLAYER_LEVELUP:0.5:1.0";
            case "transfer_fail":
                return "ENTITY_VILLAGER_NO:1.0:1.0";
            case "search_success":
                return "ENTITY_EXPERIENCE_ORB_PICKUP:0.5:1.0";
            case "search_fail":
                return "ENTITY_VILLAGER_NO:1.0:1.0";
            case "history_view":
                return "BLOCK_NOTE_BLOCK_PLING:0.5:1.2";
            default:
                return "UI_BUTTON_CLICK:0.5:1.0";
        }
    }

    /**
     * Phát âm thanh với fallback từ config
     *
     * @param player       Người chơi
     * @param config       Config object
     * @param primaryPath  Đường dẫn config chính
     * @param fallbackPath Đường dẫn config fallback
     */
    public static void playSoundWithFallback(Player player, org.bukkit.configuration.file.FileConfiguration config,
                                             String primaryPath, String fallbackPath) {
        if (player == null || config == null) {
            return;
        }

        try {
            String soundConfig = config.getString(primaryPath);
            if (soundConfig != null && !soundConfig.isEmpty()) {
                playSoundFromConfig(player, soundConfig);
                return;
            }

            // Thử fallback
            String fallbackConfig = config.getString(fallbackPath);
            if (fallbackConfig != null && !fallbackConfig.isEmpty()) {
                playSoundFromConfig(player, fallbackConfig);
                return;
            }

            // Fallback cuối cùng
            playSoundFromConfig(player, "UI_BUTTON_CLICK:0.5:1.0");
        } catch (Exception e) {
            // Bỏ qua lỗi
            if (com.hongminh54.storage.Storage.getStorage().isDebug()) {
                System.out.println("Lỗi khi phát sound với fallback: " + e.getMessage());
            }
        }
    }

    /**
     * Chuyển đổi tên âm thanh cho location playSound
     *
     * @param soundName Tên âm thanh gốc
     * @return Tên âm thanh đã chuyển đổi
     */
    private static String convertSoundNameForLocation(String soundName) {
        // Sử dụng logic tương tự SoundCompatibilityV2
        if (IS_PRE_113) {
            // Chuyển từ tên mới sang tên cũ
            switch (soundName) {
                case "BLOCK_NOTE_BLOCK_PLING":
                    return "NOTE_PLING";
                case "BLOCK_CHEST_OPEN":
                    return "CHEST_OPEN";
                case "UI_BUTTON_CLICK":
                    return "CLICK";
                case "ENTITY_VILLAGER_NO":
                    return "VILLAGER_NO";
                default:
                    return soundName;
            }
        } else {
            // Chuyển từ tên cũ sang tên mới
            switch (soundName) {
                case "NOTE_PLING":
                    return "BLOCK_NOTE_BLOCK_PLING";
                case "CHEST_OPEN":
                    return "BLOCK_CHEST_OPEN";
                case "CLICK":
                    return "UI_BUTTON_CLICK";
                case "VILLAGER_NO":
                    return "ENTITY_VILLAGER_NO";
                default:
                    return soundName;
            }
        }
    }
}