package com.hongminh54.storage.CMD.commands;

import com.hongminh54.storage.Manager.LeaderboardManager;
import com.hongminh54.storage.Manager.StatsManager;
import com.hongminh54.storage.Storage;
import com.hongminh54.storage.Utils.Chat;
import com.hongminh54.storage.Utils.File;
import org.bukkit.Bukkit;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.bukkit.util.StringUtil;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class ResetCommand implements IStorageCommand {

    @Override
    public boolean execute(CommandSender sender, String[] args) {
        if (args[0].equalsIgnoreCase("resetleaderboard")) {
            return handleResetLeaderboard(sender);
        } else if (args[0].equalsIgnoreCase("resetstats")) {
            return handleResetStats(sender, args);
        }

        return false;
    }

    private boolean handleResetLeaderboard(CommandSender sender) {
        if (!sender.hasPermission("storage.admin.resetleaderboard")) {
            sender.sendMessage(Chat.colorize(File.getMessage().getString("admin.no_permission")));
            return true;
        }

        try {
            // Hiển thị thông báo bắt đầu
            sender.sendMessage(Chat.colorize("&a&lĐang reset bảng xếp hạng, vui lòng đợi..."));

            // Reset bảng xếp hạng bất đồng bộ để tránh lag
            Bukkit.getScheduler().runTaskAsynchronously(Storage.getStorage(), () -> {
                try {
                    // Reset tất cả các loại bảng xếp hạng
                    LeaderboardManager.resetLeaderboard();

                    // Thông báo hoàn thành
                    Bukkit.getScheduler().runTask(Storage.getStorage(), () -> {
                        sender.sendMessage(Chat.colorize("&a&l✓ Đã reset thành công tất cả bảng xếp hạng!"));
                        Storage.getStorage().getLogger().info("Bảng xếp hạng đã được reset bởi " + sender.getName());
                    });
                } catch (Exception e) {
                    Bukkit.getScheduler().runTask(Storage.getStorage(), () -> {
                        sender.sendMessage(Chat.colorize("&c&l✕ Lỗi khi reset bảng xếp hạng: " + e.getMessage()));
                    });
                    Storage.getStorage().getLogger().severe("Lỗi khi reset bảng xếp hạng: " + e.getMessage());
                    e.printStackTrace();
                }
            });

        } catch (Exception e) {
            sender.sendMessage(Chat.colorize("&c&l✕ Lỗi khi reset bảng xếp hạng: " + e.getMessage()));
            Storage.getStorage().getLogger().severe("Lỗi khi reset bảng xếp hạng: " + e.getMessage());
            e.printStackTrace();
        }

        return true;
    }

    private boolean handleResetStats(CommandSender sender, String[] args) {
        if (!sender.hasPermission("storage.admin.resetstats")) {
            sender.sendMessage(Chat.colorize(File.getMessage().getString("admin.no_permission")));
            return true;
        }

        if (args.length < 2) {
            sender.sendMessage(Chat.colorize("&cCách sử dụng: /storage resetstats <tên người chơi>"));
            return true;
        }

        String targetPlayerName = args[1];
        Player targetPlayer = Bukkit.getPlayer(targetPlayerName);

        try {
            // Reset thống kê bất đồng bộ
            Bukkit.getScheduler().runTaskAsynchronously(Storage.getStorage(), () -> {
                try {
                    // Reset thống kê của người chơi
                    boolean success = StatsManager.resetPlayerStats(targetPlayerName);

                    Bukkit.getScheduler().runTask(Storage.getStorage(), () -> {
                        if (success) {
                            sender.sendMessage(Chat.colorize("&a&l✓ Đã reset thành công thống kê của &e" + targetPlayerName));

                            // Thông báo cho người chơi nếu đang online
                            if (targetPlayer != null) {
                                targetPlayer.sendMessage(Chat.colorize("&e&l⚠ Thống kê của bạn đã được reset bởi &c" + sender.getName()));
                            }

                            Storage.getStorage().getLogger().info("Thống kê của " + targetPlayerName + " đã được reset bởi " + sender.getName());
                        } else {
                            sender.sendMessage(Chat.colorize("&c&l✕ Không thể reset thống kê của " + targetPlayerName + ". Người chơi có thể không tồn tại."));
                        }
                    });
                } catch (Exception e) {
                    Bukkit.getScheduler().runTask(Storage.getStorage(), () -> {
                        sender.sendMessage(Chat.colorize("&c&l✕ Lỗi khi reset thống kê: " + e.getMessage()));
                    });
                    Storage.getStorage().getLogger().severe("Lỗi khi reset thống kê: " + e.getMessage());
                    e.printStackTrace();
                }
            });

        } catch (Exception e) {
            sender.sendMessage(Chat.colorize("&c&l✕ Lỗi khi reset thống kê: " + e.getMessage()));
            Storage.getStorage().getLogger().severe("Lỗi khi reset thống kê: " + e.getMessage());
            e.printStackTrace();
        }

        return true;
    }

    @Override
    public List<String> getTabComplete(CommandSender sender, String[] args) {
        List<String> completions = new ArrayList<>();

        if (args.length == 2 && args[0].equalsIgnoreCase("resetstats")) {
            // Tab completion cho tên người chơi
            List<String> playerNames = new ArrayList<>();
            for (Player player : Bukkit.getOnlinePlayers()) {
                playerNames.add(player.getName());
            }
            StringUtil.copyPartialMatches(args[1], playerNames, completions);
        }

        return completions;
    }

    @Override
    public String getCommandName() {
        return "";
    }

    @Override
    public List<String> getAliases() {
        return Arrays.asList("resetleaderboard", "resetstats");
    }

    @Override
    public boolean hasPermission(CommandSender sender) {
        return sender.hasPermission("storage.admin.resetleaderboard") ||
                sender.hasPermission("storage.admin.resetstats");
    }
}
