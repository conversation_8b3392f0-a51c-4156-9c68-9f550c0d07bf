package com.hongminh54.storage.Database;

/**
 * Đ<PERSON><PERSON> diện cho một thao tác database cần được thực hiện
 * Hỗ trợ batch processing và queue management
 */
public class DatabaseOperation {

    private final OperationType type;
    private final PlayerData playerData;
    private final String playerName;
    private final long timestamp;
    private final int priority;
    /**
     * Constructor cho INSERT và UPDATE operations
     */
    public DatabaseOperation(OperationType type, PlayerData playerData) {
        this(type, playerData, 0);
    }

    /**
     * Constructor cho INSERT và UPDATE operations với priority
     */
    public DatabaseOperation(OperationType type, PlayerData playerData, int priority) {
        this.type = type;
        this.playerData = playerData;
        this.playerName = playerData != null ? playerData.getPlayer() : null;
        this.timestamp = System.currentTimeMillis();
        this.priority = priority;
    }

    /**
     * Constructor cho DELETE operations
     */
    public DatabaseOperation(String playerName) {
        this.type = OperationType.DELETE;
        this.playerData = null;
        this.playerName = playerName;
        this.timestamp = System.currentTimeMillis();
        this.priority = 0;
    }

    /**
     * Constructor cho DELETE operations với priority
     */
    public DatabaseOperation(String playerName, int priority) {
        this.type = OperationType.DELETE;
        this.playerData = null;
        this.playerName = playerName;
        this.timestamp = System.currentTimeMillis();
        this.priority = priority;
    }

    /**
     * Tạo operation UPDATE
     */
    public static DatabaseOperation createUpdate(PlayerData playerData) {
        return new DatabaseOperation(OperationType.UPDATE, playerData);
    }

    /**
     * Tạo operation UPDATE với priority cao (urgent)
     */
    public static DatabaseOperation createUrgentUpdate(PlayerData playerData) {
        return new DatabaseOperation(OperationType.UPDATE, playerData, 10);
    }

    /**
     * Tạo operation INSERT
     */
    public static DatabaseOperation createInsert(PlayerData playerData) {
        return new DatabaseOperation(OperationType.INSERT, playerData);
    }

    /**
     * Tạo operation DELETE
     */
    public static DatabaseOperation createDelete(String playerName) {
        return new DatabaseOperation(playerName);
    }

    /**
     * Tạo operation DELETE với priority cao
     */
    public static DatabaseOperation createUrgentDelete(String playerName) {
        return new DatabaseOperation(playerName, 10);
    }

    /**
     * Lấy loại thao tác
     */
    public OperationType getType() {
        return type;
    }

    /**
     * Lấy dữ liệu người chơi (cho INSERT/UPDATE)
     */
    public PlayerData getPlayerData() {
        return playerData;
    }

    /**
     * Lấy tên người chơi
     */
    public String getPlayerName() {
        return playerName;
    }

    /**
     * Lấy timestamp khi operation được tạo
     */
    public long getTimestamp() {
        return timestamp;
    }

    /**
     * Lấy priority của operation (số càng cao càng ưu tiên)
     */
    public int getPriority() {
        return priority;
    }

    /**
     * Kiểm tra xem operation có hết hạn không
     */
    public boolean isExpired(long maxAge) {
        return (System.currentTimeMillis() - timestamp) > maxAge;
    }

    @Override
    public String toString() {
        return String.format("DatabaseOperation{type=%s, player=%s, priority=%d, timestamp=%d}",
                type, playerName, priority, timestamp);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;

        DatabaseOperation that = (DatabaseOperation) obj;
        return type == that.type &&
                playerName != null && playerName.equals(that.playerName);
    }

    @Override
    public int hashCode() {
        int result = type.hashCode();
        result = 31 * result + (playerName != null ? playerName.hashCode() : 0);
        return result;
    }

    /**
     * Loại thao tác database
     */
    public enum OperationType {
        INSERT,  // Thêm dữ liệu mới
        UPDATE,  // Cập nhật dữ liệu
        DELETE   // Xóa dữ liệu
    }
}
