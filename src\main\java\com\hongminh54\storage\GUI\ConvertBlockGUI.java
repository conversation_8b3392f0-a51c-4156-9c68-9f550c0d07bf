package com.hongminh54.storage.GUI;

import com.hongminh54.storage.GUI.manager.IGUI;
import com.hongminh54.storage.GUI.manager.InteractiveItem;
import com.hongminh54.storage.Listeners.ChatListener;
import com.hongminh54.storage.Manager.ItemManager;
import com.hongminh54.storage.Manager.MineManager;
import com.hongminh54.storage.Manager.SoundManager;
import com.hongminh54.storage.Storage;
import com.hongminh54.storage.Utils.File;
import com.hongminh54.storage.Utils.GUIText;
import com.hongminh54.storage.Utils.Number;
import com.hongminh54.storage.compatibility.MaterialCompatibility;
import org.bukkit.Bukkit;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

public class ConvertBlockGUI implements IGUI {

    private final Player p;
    private final FileConfiguration config;

    public ConvertBlockGUI(Player p) {
        this.p = p;
        config = File.getGUIConfig("convert_block");

        // Validate config
        if (!validateConfig()) {
            Storage.getStorage().getLogger().warning("Config convert_block.yml có vấn đề cho ConvertBlockGUI");
        }

        // Phát âm thanh mở GUI
        playOpenSound();
    }

    @NotNull
    @Override
    public Inventory getInventory() {
        // Kiểm tra config trước khi sử dụng
        if (config == null) {
            com.hongminh54.storage.Storage.getStorage().getLogger().severe("Config cho ConvertBlockGUI là null!");
            // Tạo inventory mặc định
            return Bukkit.createInventory(p, 27, "§8Đổi Block - Lỗi Config");
        }

        // Lấy title với fallback
        String title = config.getString("title", "&8Đổi Block");
        if (title == null) {
            title = "&8Đổi Block";
        }
        title = title.replace("#player#", p.getName());

        // Lấy size với fallback
        int size = config.getInt("size", 3);
        if (size <= 0 || size > 6) {
            size = 3; // Fallback to 3 rows
        }

        Inventory inventory = Bukkit.createInventory(p, size * 9, GUIText.format(title));

        // Phát âm thanh khi mở giao diện
        playOpenSound();

        // Kiểm tra section items tồn tại
        if (config.getConfigurationSection("items") == null) {
            com.hongminh54.storage.Storage.getStorage().getLogger().severe("Không tìm thấy section 'items' trong config convert_block!");
            // Chỉ ghi log mà không hiển thị tin nhắn lỗi cho người chơi
            return inventory;
        }

        for (String item_tag : config.getConfigurationSection("items").getKeys(false)) {
            String slotConfig = config.getString("items." + item_tag + ".slot");
            if (slotConfig == null) {
                com.hongminh54.storage.Storage.getStorage().getLogger().warning("Không tìm thấy slot cho item: " + item_tag);
                continue;
            }
            String slot = slotConfig.replace(" ", "");
            if (item_tag.equalsIgnoreCase("convert_item")) {
                if (slot.contains(",")) {
                    List<String> slot_list = new ArrayList<>(Arrays.asList(slot.split(",")));
                    List<String> item_list = new ArrayList<>(MineManager.getPluginBlocks());

                    // Lọc ra các vật liệu có thể chuyển đổi (phôi hoặc block)
                    List<String> convertibleItems = new ArrayList<>();
                    List<Boolean> isBlockList = new ArrayList<>();

                    for (String itemId : item_list) {
                        String material = itemId.split(";")[0].toUpperCase();

                        // Kiểm tra xem là phôi hay block
                        boolean isIngot = isIngotMaterial(material);
                        boolean isBlock = isBlockMaterial(material);

                        if (isIngot || isBlock) {
                            convertibleItems.add(itemId);
                            isBlockList.add(isBlock);
                        }
                    }

                    // Hiển thị các vật liệu có thể chuyển đổi
                    for (int i = 0; i < convertibleItems.size() && i < slot_list.size(); i++) {
                        String itemId = convertibleItems.get(i);
                        boolean isBlock = isBlockList.get(i);

                        String material = MineManager.getMaterial(itemId);
                        String name = File.getConfig().getString("items." + itemId);

                        // Tạo item trong GUI
                        ItemStack itemStack = ItemManager.getItemConfig(p, material, name != null ? name : itemId.split(";")[0], config.getConfigurationSection("items.convert_item"));

                        // Cập nhật lore với thông tin phù hợp
                        ItemManager.updateItemLore(itemStack, lore -> {
                            List<String> updatedLore = new ArrayList<>();
                            for (String line : lore) {
                                // Thay thế placeholder dựa trên loại vật liệu
                                if (line.contains("#type_name#")) {
                                    line = line.replace("#type_name#", isBlock ? "Block khoáng sản" : "Phôi khoáng sản");
                                }
                                if (line.contains("#convert_left_click#")) {
                                    if (isBlock) {
                                        line = line.replace("#convert_left_click#", "&eClick chuột trái để đổi thành phôi");
                                    } else {
                                        line = line.replace("#convert_left_click#", "&eClick chuột trái để đổi thành block");
                                    }
                                }
                                if (line.contains("#convert_right_click#")) {
                                    if (isBlock) {
                                        line = line.replace("#convert_right_click#", "&eClick chuột phải để đổi tất cả thành phôi");
                                    } else {
                                        line = line.replace("#convert_right_click#", "&eClick chuột phải để đổi tất cả thành block");
                                    }
                                }
                                updatedLore.add(line);
                            }
                            return updatedLore;
                        });

                        InteractiveItem interactiveItem = new InteractiveItem(itemStack, Number.getInteger(slot_list.get(i)));

                        // Xử lý click trái - đổi số lượng tùy chỉnh
                        final String finalMaterial = itemId;
                        final boolean finalIsBlock = isBlock;

                        interactiveItem.onLeftClick(player -> {
                            ChatListener.chat_convert_block.put(p, finalMaterial);

                            // Thông báo nhập số lượng
                            if (finalIsBlock) {
                                p.sendMessage(com.hongminh54.storage.Utils.Chat.colorize(
                                        File.getMessage().getString("user.action.convert.chat_number_reverse", "&eNhập số lượng block muốn đổi thành phôi:")));
                            } else {
                                p.sendMessage(com.hongminh54.storage.Utils.Chat.colorize(
                                        File.getMessage().getString("user.action.convert.chat_number", "&eNhập số lượng phôi muốn đổi thành block:")));
                            }

                            // Thông báo có thể cancel
                            String cancelHint = File.getMessage().getString("user.action.transfer.cancel_hint");
                            if (cancelHint == null) {
                                cancelHint = "&8[&c&l✕&8] &cNhập 'cancel' hoặc 'huy' để hủy";
                            }
                            p.sendMessage(com.hongminh54.storage.Utils.Chat.colorize(cancelHint));

                            // Phát âm thanh khi click
                            try {
                                String clickSound = config.getString("click_sound", "UI_BUTTON_CLICK:0.5:1.0");
                                SoundManager.playSoundFromConfig(p, clickSound);
                            } catch (Exception e) {
                                // Bỏ qua lỗi âm thanh
                            }

                            p.closeInventory();
                        });

                        // Xử lý click phải - đổi tất cả với xác nhận
                        interactiveItem.onRightClick(player -> {
                            // Phát âm thanh khi click
                            try {
                                String clickSound = config.getString("click_sound", "UI_BUTTON_CLICK:0.5:1.0");
                                SoundManager.playSoundFromConfig(p, clickSound);
                            } catch (Exception e) {
                                // Bỏ qua lỗi âm thanh
                            }

                            // Mở GUI xác nhận thay vì thực hiện chuyển đổi trực tiếp
                            p.closeInventory();
                            Bukkit.getScheduler().runTaskLater(com.hongminh54.storage.Storage.getStorage(), () -> {
                                p.openInventory(new ConvertConfirmationGUI(p, finalMaterial, -1, finalIsBlock).getInventory());
                            }, 1L);
                        });

                        inventory.setItem(interactiveItem.getSlot(), interactiveItem);
                    }
                }
            } else if (item_tag.equalsIgnoreCase("back")) {
                if (slot.contains(",")) {
                    for (String slot_string : slot.split(",")) {
                        InteractiveItem item = new InteractiveItem(ItemManager.getItemConfig(Objects.requireNonNull(config.getConfigurationSection("items." + item_tag))), Number.getInteger(slot_string));
                        String type_left = config.getString("items." + item_tag + ".action.left.type");
                        String action_left = config.getString("items." + item_tag + ".action.left.action");
                        String type_right = config.getString("items." + item_tag + ".action.right.type");
                        String action_right = config.getString("items." + item_tag + ".action.right.action");

                        if (type_left != null && action_left != null) {
                            item.onLeftClick(player -> {
                                if (type_left.equalsIgnoreCase("command")) {
                                    Bukkit.getScheduler().runTask(com.hongminh54.storage.Storage.getStorage(), () -> {
                                        Bukkit.dispatchCommand(p, action_left);
                                    });
                                }
                            });
                        }

                        if (type_right != null && action_right != null) {
                            item.onRightClick(player -> {
                                if (type_right.equalsIgnoreCase("command")) {
                                    Bukkit.getScheduler().runTask(com.hongminh54.storage.Storage.getStorage(), () -> {
                                        Bukkit.dispatchCommand(p, action_right);
                                    });
                                }
                            });
                        }

                        inventory.setItem(item.getSlot(), item);
                    }
                } else {
                    InteractiveItem item = new InteractiveItem(ItemManager.getItemConfig(Objects.requireNonNull(config.getConfigurationSection("items." + item_tag))), Number.getInteger(slot));
                    String type_left = config.getString("items." + item_tag + ".action.left.type");
                    String action_left = config.getString("items." + item_tag + ".action.left.action");
                    String type_right = config.getString("items." + item_tag + ".action.right.type");
                    String action_right = config.getString("items." + item_tag + ".action.right.action");

                    if (type_left != null && action_left != null) {
                        item.onLeftClick(player -> {
                            if (type_left.equalsIgnoreCase("command")) {
                                Bukkit.getScheduler().runTask(com.hongminh54.storage.Storage.getStorage(), () -> {
                                    Bukkit.dispatchCommand(p, action_left);
                                });
                            }
                        });
                    }

                    if (type_right != null && action_right != null) {
                        item.onRightClick(player -> {
                            if (type_right.equalsIgnoreCase("command")) {
                                Bukkit.getScheduler().runTask(com.hongminh54.storage.Storage.getStorage(), () -> {
                                    Bukkit.dispatchCommand(p, action_right);
                                });
                            }
                        });
                    }

                    inventory.setItem(item.getSlot(), item);
                }
            } else {
                if (slot.contains(",")) {
                    for (String slot_string : slot.split(",")) {
                        InteractiveItem item = new InteractiveItem(ItemManager.getItemConfig(Objects.requireNonNull(config.getConfigurationSection("items." + item_tag))), Number.getInteger(slot_string));
                        inventory.setItem(item.getSlot(), item);
                    }
                } else {
                    InteractiveItem item = new InteractiveItem(ItemManager.getItemConfig(Objects.requireNonNull(config.getConfigurationSection("items." + item_tag))), Number.getInteger(slot));
                    inventory.setItem(item.getSlot(), item);
                }
            }
        }
        return inventory;
    }

    /**
     * Kiểm tra xem một vật liệu có phải là phôi không
     *
     * @param material Tên vật liệu
     * @return true nếu là phôi
     */
    private boolean isIngotMaterial(String material) {
        String[] ingotMaterials = {
                "IRON_INGOT", "GOLD_INGOT", "DIAMOND", "EMERALD", "COAL", "REDSTONE", "LAPIS_LAZULI",
                "COPPER_INGOT", "NETHERITE_INGOT", "QUARTZ", "AMETHYST_SHARD"
        };

        for (String ingot : ingotMaterials) {
            if (material.contains(ingot)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Kiểm tra xem một vật liệu có phải là block không
     *
     * @param material Tên vật liệu
     * @return true nếu là block
     */
    private boolean isBlockMaterial(String material) {
        String[] blockMaterials = {
                "IRON_BLOCK", "GOLD_BLOCK", "DIAMOND_BLOCK", "EMERALD_BLOCK", "COAL_BLOCK",
                "REDSTONE_BLOCK", "LAPIS_BLOCK", "COPPER_BLOCK", "NETHERITE_BLOCK",
                "QUARTZ_BLOCK", "AMETHYST_BLOCK"
        };

        for (String block : blockMaterials) {
            if (material.contains(block)) {
                return true;
            }
        }

        return false;
    }

    public Player getPlayer() {
        return p;
    }

    public FileConfiguration getConfig() {
        return config;
    }

    /**
     * Validate config file
     *
     * @return true nếu config hợp lệ
     */
    private boolean validateConfig() {
        try {
            if (config == null) {
                return false;
            }

            // Kiểm tra các trường bắt buộc
            if (!config.contains("title") || !config.contains("size") || !config.contains("items")) {
                return false;
            }

            // Kiểm tra size hợp lệ
            int size = config.getInt("size", 6);
            return size >= 1 && size <= 6;
        } catch (Exception e) {
            handleError("Error validating config: " + e.getMessage(), false);
            return false;
        }
    }

    /**
     * Phát âm thanh mở GUI từ config
     */
    private void playOpenSound() {
        try {
            if (File.getConfig().getBoolean("effects.enabled", true)) {
                String openSound = config.getString("sounds.open", "BLOCK_CHEST_OPEN:0.5:1.0");
                SoundManager.playSoundFromConfig(p, openSound);
            }
        } catch (Exception e) {
            // Bỏ qua lỗi âm thanh
        }
    }

    /**
     * Phát âm thanh click từ config
     */
    private void playClickSound() {
        try {
            if (File.getConfig().getBoolean("effects.enabled", true)) {
                String clickSound = config.getString("sounds.click", "UI_BUTTON_CLICK:0.5:1.0");
                SoundManager.playSoundFromConfig(p, clickSound);
            }
        } catch (Exception e) {
            // Bỏ qua lỗi âm thanh
        }
    }

    /**
     * Xử lý lỗi và hiển thị thông báo
     *
     * @param error        Thông điệp lỗi
     * @param showToPlayer Có hiển thị cho người chơi không
     */
    private void handleError(String error, boolean showToPlayer) {
        Storage.getStorage().getLogger().warning("ConvertBlockGUI Error: " + error);
        if (showToPlayer && p != null && p.isOnline()) {
            try {
                String failSound = config.getString("sounds.error", "ENTITY_VILLAGER_NO:0.5:1.0");
                SoundManager.playSoundFromConfig(p, failSound);
            } catch (Exception e) {
                // Bỏ qua lỗi âm thanh
            }
        }
    }

    /**
     * Tạo ItemStack tương thích đa phiên bản
     */
    private org.bukkit.inventory.ItemStack createCompatibleItemStack(org.bukkit.Material material, int amount) {
        try {
            if (MaterialCompatibility.isPre113()) {
                return new org.bukkit.inventory.ItemStack(material, amount, (short) 0);
            } else {
                return new org.bukkit.inventory.ItemStack(material, amount);
            }
        } catch (Exception e) {
            handleError("Error creating compatible ItemStack: " + e.getMessage(), false);
            return new org.bukkit.inventory.ItemStack(org.bukkit.Material.PAPER, amount);
        }
    }
}