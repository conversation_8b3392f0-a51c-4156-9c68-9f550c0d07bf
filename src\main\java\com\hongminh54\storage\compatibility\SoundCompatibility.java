package com.hongminh54.storage.compatibility;

import com.cryptomorin.xseries.XSound;
import com.hongminh54.storage.NMS.NMSAssistant;
import com.hongminh54.storage.Storage;
import com.hongminh54.storage.Wrapper.SoundWrapper;
import org.bukkit.Sound;
import org.bukkit.entity.Player;

import java.lang.reflect.Method;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Lớp hỗ trợ tương thích âm thanh cho Minecraft 1.12.2 - 1.21.x
 * X<PERSON> lý các vấn đề IncompatibleClassChangeError và Sound enum differences
 */
public class SoundCompatibility {

    private static final NMSAssistant nmsAssistant = new NMSAssistant();
    private static final boolean IS_PRE_113 = nmsAssistant.isVersionLessThan(13);
    private static final boolean IS_PRE_116 = nmsAssistant.isVersionLessThan(16);
    private static final boolean IS_1_20_OR_HIGHER = nmsAssistant.isVersionGreaterThanOrEqualTo(20);
    private static final boolean IS_1_20_5_OR_HIGHER = nmsAssistant.is1_20_5OrHigher();
    private static final boolean IS_1_21_4_OR_HIGHER = nmsAssistant.is1_21_4OrHigher();

    // Cache để tối ưu performance với giới hạn kích thước
    private static final ConcurrentHashMap<String, Sound> soundCache = new ConcurrentHashMap<>();
    private static final ConcurrentHashMap<String, String> soundNameCache = new ConcurrentHashMap<>();
    private static final int MAX_CACHE_SIZE = 200;

    /**
     * Phát âm thanh một cách an toàn cho player - Cải thiện cho 1.12.2
     *
     * @param player      Người chơi
     * @param modernSound Tên âm thanh phiên bản mới (1.13+)
     * @param legacySound Tên âm thanh phiên bản cũ (1.12.2)
     * @param volume      Âm lượng (0.0 - 1.0)
     * @param pitch       Cao độ (0.5 - 2.0)
     */
    public static void playSound(Player player, String modernSound, String legacySound, float volume, float pitch) {
        if (player == null) {
            return;
        }

        if (Storage.getStorage().isDebug()) {
            Storage.getStorage().getLogger().info("§e[SoundDebug] Attempting to play sound - Modern: " + modernSound + ", Legacy: " + legacySound);
        }

        // Ưu tiên sử dụng SoundWrapper cho 1.12.2
        if (IS_PRE_113) {
            String soundToUse = (legacySound != null && !legacySound.isEmpty()) ? legacySound : modernSound;
            if (SoundWrapper.playSound(player, soundToUse, volume, pitch)) {
                if (Storage.getStorage().isDebug()) {
                    Storage.getStorage().getLogger().info("§a[SoundDebug] Successfully played via SoundWrapper: " + soundToUse);
                }
                return;
            }
        }

        try {
            // Sử dụng method an toàn cho 1.12.2
            if (playSoundSafeFor112(player, modernSound, legacySound, volume, pitch)) {
                return; // Thành công
            }

            // Fallback cuối cùng với SoundWrapper
            if (SoundWrapper.playSound(player, SoundWrapper.getDefaultSoundName(), volume, pitch)) {
                if (Storage.getStorage().isDebug()) {
                    Storage.getStorage().getLogger().warning("§c[SoundDebug] Used SoundWrapper fallback for: " + modernSound + "/" + legacySound);
                }
                return;
            }

            // Fallback cuối cùng với method cũ
            playDefaultSound(player, volume, pitch);
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("§c[SoundDebug] Used legacy fallback for: " + modernSound + "/" + legacySound);
            }

        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("§c[SoundDebug] Exception playing sound: " + e.getMessage());
            }
            // Fallback cuối cùng với SoundWrapper
            try {
                SoundWrapper.playSound(player, SoundWrapper.getDefaultSoundName(), volume, pitch);
            } catch (Exception ignored) {
                // Bỏ qua nếu không thể phát sound nào
            }
        }
    }

    /**
     * Lấy Sound enum tương thích
     *
     * @param modernSound Tên âm thanh phiên bản mới (1.13+)
     * @param legacySound Tên âm thanh phiên bản cũ (1.12.2)
     * @return Sound enum tương thích hoặc null nếu không tìm thấy
     */
    public static Sound getCompatibleSound(String modernSound, String legacySound) {
        // Thử với sound phù hợp với phiên bản hiện tại trước
        String primarySound = (IS_PRE_113 && legacySound != null) ? legacySound : modernSound;
        String fallbackSound = (IS_PRE_113 && legacySound != null) ? modernSound : legacySound;

        Sound sound = getSoundSafely(primarySound);
        if (sound != null) {
            return sound;
        }

        // Thử với sound fallback
        if (fallbackSound != null) {
            sound = getSoundSafely(fallbackSound);
            if (sound != null) {
                return sound;
            }
        }

        // Cuối cùng sử dụng sound mặc định
        return getDefaultSound();
    }

    /**
     * Lấy âm thanh mặc định an toàn
     *
     * @return Sound mặc định
     */
    public static Sound getDefaultSound() {
        // Thử với sound mặc định phù hợp với phiên bản
        String defaultSoundName = IS_PRE_113 ? "CLICK" : "UI_BUTTON_CLICK";
        Sound sound = getSoundSafely(defaultSoundName);
        if (sound != null) {
            return sound;
        }

        // Thử với sound mặc định khác
        String alternateSoundName = IS_PRE_113 ? "UI_BUTTON_CLICK" : "CLICK";
        sound = getSoundSafely(alternateSoundName);
        if (sound != null) {
            return sound;
        }

        // Fallback cuối cùng - lấy sound đầu tiên có sẵn sử dụng reflection
        try {
            Method valuesMethod = Sound.class.getMethod("values");
            Sound[] sounds = (Sound[]) valuesMethod.invoke(null);
            if (sounds.length > 0) {
                return sounds[0];
            }
        } catch (Exception e) {
            // Không thể lấy sound nào
        }

        return null; // Không có sound nào khả dụng
    }

    /**
     * Phát âm thanh click UI
     *
     * @param player Người chơi
     */
    public static void playClickSound(Player player) {
        playSound(player, "UI_BUTTON_CLICK", "CLICK", 0.5f, 1.0f);
    }

    /**
     * Phát âm thanh thành công
     *
     * @param player Người chơi
     */
    public static void playSuccessSound(Player player) {
        playSound(player, "ENTITY_EXPERIENCE_ORB_PICKUP", "ORB_PICKUP", 0.7f, 1.2f);
    }

    /**
     * Phát âm thanh lỗi
     *
     * @param player Người chơi
     */
    public static void playErrorSound(Player player) {
        playSound(player, "ENTITY_VILLAGER_NO", "VILLAGER_NO", 0.8f, 0.8f);
    }

    /**
     * Phát âm thanh cảnh báo
     *
     * @param player Người chơi
     */
    public static void playWarningSound(Player player) {
        playSound(player, "BLOCK_NOTE_BLOCK_PLING", "NOTE_PLING", 0.6f, 0.5f);
    }

    /**
     * Phát âm thanh level up
     *
     * @param player Người chơi
     */
    public static void playLevelUpSound(Player player) {
        playSound(player, "ENTITY_PLAYER_LEVELUP", "LEVEL_UP", 1.0f, 1.0f);
    }

    /**
     * Phát âm thanh anvil
     *
     * @param player Người chơi
     */
    public static void playAnvilSound(Player player) {
        playSound(player, "BLOCK_ANVIL_USE", "ANVIL_USE", 0.8f, 1.0f);
    }

    /**
     * Phát âm thanh chest mở
     *
     * @param player Người chơi
     */
    public static void playChestOpenSound(Player player) {
        playSound(player, "BLOCK_CHEST_OPEN", "CHEST_OPEN", 0.7f, 1.0f);
    }

    /**
     * Phát âm thanh chest đóng
     *
     * @param player Người chơi
     */
    public static void playChestCloseSound(Player player) {
        playSound(player, "BLOCK_CHEST_CLOSE", "CHEST_CLOSE", 0.7f, 1.0f);
    }

    /**
     * Phát âm thanh item break
     *
     * @param player Người chơi
     */
    public static void playItemBreakSound(Player player) {
        playSound(player, "ENTITY_ITEM_BREAK", "ITEM_BREAK", 0.8f, 1.0f);
    }

    /**
     * Phát âm thanh pop
     *
     * @param player Người chơi
     */
    public static void playPopSound(Player player) {
        playSound(player, "ENTITY_ITEM_PICKUP", "ITEM_PICKUP", 0.5f, 1.5f);
    }

    /**
     * Phát âm thanh teleport
     *
     * @param player Người chơi
     */
    public static void playTeleportSound(Player player) {
        playSound(player, "ENTITY_ENDERMAN_TELEPORT", "ENDERMAN_TELEPORT", 1.0f, 1.0f);
    }

    /**
     * Phát âm thanh firework
     *
     * @param player Người chơi
     */
    public static void playFireworkSound(Player player) {
        playSound(player, "ENTITY_FIREWORK_ROCKET_BLAST", "FIREWORK_BLAST", 1.0f, 1.0f);
    }

    /**
     * Phát âm thanh explosion
     *
     * @param player Người chơi
     */
    public static void playExplosionSound(Player player) {
        playSound(player, "ENTITY_GENERIC_EXPLODE", "EXPLODE", 0.8f, 1.0f);
    }

    /**
     * Phát âm thanh splash
     *
     * @param player Người chơi
     */
    public static void playSplashSound(Player player) {
        playSound(player, "ENTITY_GENERIC_SPLASH", "SPLASH", 0.6f, 1.0f);
    }

    /**
     * Phát âm thanh eat
     *
     * @param player Người chơi
     */
    public static void playEatSound(Player player) {
        playSound(player, "ENTITY_GENERIC_EAT", "EAT", 0.7f, 1.0f);
    }

    /**
     * Phát âm thanh drink
     *
     * @param player Người chơi
     */
    public static void playDrinkSound(Player player) {
        playSound(player, "ENTITY_GENERIC_DRINK", "DRINK", 0.7f, 1.0f);
    }

    /**
     * Phát âm thanh tại vị trí cụ thể
     *
     * @param player      Người chơi
     * @param modernSound Tên âm thanh phiên bản mới
     * @param legacySound Tên âm thanh phiên bản cũ
     * @param volume      Âm lượng
     * @param pitch       Cao độ
     * @param x           Tọa độ X
     * @param y           Tọa độ Y
     * @param z           Tọa độ Z
     */
    public static void playSoundAtLocation(Player player, String modernSound, String legacySound,
                                           float volume, float pitch, double x, double y, double z) {
        if (player == null || player.getWorld() == null) {
            return;
        }

        try {
            Sound sound = getCompatibleSound(modernSound, legacySound);
            if (sound != null) {
                org.bukkit.Location location = new org.bukkit.Location(player.getWorld(), x, y, z);
                player.playSound(location, sound, volume, pitch);
            }
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể phát âm thanh tại vị trí: " + e.getMessage());
            }
        }
    }

    /**
     * Lấy Sound enum một cách an toàn sử dụng reflection với cache
     *
     * @param soundName Tên sound
     * @return Sound enum hoặc null nếu không tìm thấy
     */
    public static Sound getSoundSafely(String soundName) {
        if (soundName == null || soundName.isEmpty()) {
            return null;
        }

        // Kiểm tra cache trước
        Sound cachedSound = soundCache.get(soundName.toUpperCase());
        if (cachedSound != null) {
            return cachedSound;
        }

        try {
            // Sử dụng reflection để tránh IncompatibleClassChangeError
            Method valueOfMethod = Sound.class.getMethod("valueOf", String.class);
            Sound sound = (Sound) valueOfMethod.invoke(null, soundName.toUpperCase());
            if (sound != null) {
                // Kiểm tra cache size trước khi thêm
                if (soundCache.size() >= MAX_CACHE_SIZE) {
                    soundCache.clear();
                }
                soundCache.put(soundName.toUpperCase(), sound);
                return sound;
            }
        } catch (Exception e) {
            // Thử với các biến thể tên phổ biến
            String[] variants = getSoundVariants(soundName);
            for (String variant : variants) {
                try {
                    Method valueOfMethod = Sound.class.getMethod("valueOf", String.class);
                    Sound sound = (Sound) valueOfMethod.invoke(null, variant);
                    if (sound != null) {
                        // Kiểm tra cache size trước khi thêm
                        if (soundCache.size() >= MAX_CACHE_SIZE) {
                            soundCache.clear();
                        }
                        soundCache.put(soundName.toUpperCase(), sound);
                        return sound;
                    }
                } catch (Exception ignored) {
                    // Tiếp tục thử variant tiếp theo
                }
            }
        }

        // Không tìm thấy sound nào phù hợp
        return null;
    }

    /**
     * Lấy các biến thể tên sound cho tương thích đa phiên bản với cache
     *
     * @param soundName Tên sound gốc
     * @return Mảng các biến thể tên
     */
    private static String[] getSoundVariants(String soundName) {
        String upperName = soundName.toUpperCase();

        // Kiểm tra cache trước
        String cachedVariant = soundNameCache.get(upperName);
        if (cachedVariant != null) {
            return new String[]{cachedVariant};
        }

        // Mapping các sound phổ biến giữa các phiên bản
        String[] variants;
        switch (upperName) {
            case "UI_BUTTON_CLICK":
                variants = new String[]{"UI_BUTTON_CLICK", "CLICK"};
                break;
            case "CLICK":
                variants = new String[]{"CLICK", "UI_BUTTON_CLICK"};
                break;
            case "ENTITY_EXPERIENCE_ORB_PICKUP":
                variants = new String[]{"ENTITY_EXPERIENCE_ORB_PICKUP", "ORB_PICKUP"};
                break;
            case "ORB_PICKUP":
                variants = new String[]{"ORB_PICKUP", "ENTITY_EXPERIENCE_ORB_PICKUP"};
                break;
            case "ENTITY_VILLAGER_NO":
                variants = new String[]{"ENTITY_VILLAGER_NO", "VILLAGER_NO"};
                break;
            case "VILLAGER_NO":
                variants = new String[]{"VILLAGER_NO", "ENTITY_VILLAGER_NO"};
                break;
            case "BLOCK_NOTE_BLOCK_PLING":
                variants = new String[]{"BLOCK_NOTE_BLOCK_PLING", "NOTE_PLING"};
                break;
            case "NOTE_PLING":
                variants = new String[]{"NOTE_PLING", "BLOCK_NOTE_BLOCK_PLING"};
                break;
            case "ENTITY_ITEM_PICKUP":
                variants = new String[]{"ENTITY_ITEM_PICKUP", "ITEM_PICKUP"};
                break;
            case "ITEM_PICKUP":
                variants = new String[]{"ITEM_PICKUP", "ENTITY_ITEM_PICKUP"};
                break;
            case "BLOCK_CHEST_OPEN":
                variants = new String[]{"BLOCK_CHEST_OPEN", "CHEST_OPEN"};
                break;
            case "CHEST_OPEN":
                variants = new String[]{"CHEST_OPEN", "BLOCK_CHEST_OPEN"};
                break;
            case "BLOCK_CHEST_CLOSE":
                variants = new String[]{"BLOCK_CHEST_CLOSE", "CHEST_CLOSE"};
                break;
            case "CHEST_CLOSE":
                variants = new String[]{"CHEST_CLOSE", "BLOCK_CHEST_CLOSE"};
                break;
            case "ENTITY_PLAYER_LEVELUP":
                variants = new String[]{"ENTITY_PLAYER_LEVELUP", "LEVEL_UP", "PLAYER_LEVELUP"};
                break;
            case "LEVEL_UP":
                variants = new String[]{"LEVEL_UP", "ENTITY_PLAYER_LEVELUP", "PLAYER_LEVELUP"};
                break;
            default:
                variants = new String[]{upperName};
        }

        return variants;
    }

    /**
     * Kiểm tra xem sound có tồn tại không
     *
     * @param soundName Tên sound
     * @return true nếu sound tồn tại
     */
    public static boolean soundExists(String soundName) {
        return getSoundSafely(soundName) != null;
    }

    /**
     * Lấy tất cả sounds có sẵn
     *
     * @return Mảng tên sounds
     */
    public static String[] getAllSounds() {
        Sound[] sounds = Sound.values();
        String[] soundNames = new String[sounds.length];
        for (int i = 0; i < sounds.length; i++) {
            soundNames[i] = sounds[i].name();
        }
        return soundNames;
    }

    /**
     * Phát âm thanh sử dụng XSound trực tiếp (method mới cho cross-version compatibility)
     *
     * @param player    Người chơi
     * @param soundName Tên âm thanh
     * @param volume    Âm lượng
     * @param pitch     Cao độ
     * @return true nếu thành công
     */
    private static boolean playWithXSound(Player player, String soundName, float volume, float pitch) {
        try {
            // Thử parse XSound từ tên
            XSound xSound = null;

            // Sử dụng matchXSound thay vì valueOf
            xSound = XSound.matchXSound(soundName.toUpperCase()).orElse(null);
            if (xSound == null) {
                // Thử với mapping
                xSound = getXSoundFromMapping(soundName);
            }

            if (xSound != null) {
                xSound.play(player, volume, pitch);
                return true;
            }

        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("§c[SoundDebug] XSound failed: " + soundName + " - " + e.getMessage());
            }
        }

        return false;
    }

    /**
     * Lấy XSound từ sound name mapping
     */
    private static XSound getXSoundFromMapping(String soundName) {
        String upperName = soundName.toUpperCase();

        switch (upperName) {
            case "CLICK":
            case "WOOD_CLICK":
            case "STONE_BUTTON_CLICK_ON":
                return XSound.UI_BUTTON_CLICK;
            case "CHEST_OPEN":
                return XSound.BLOCK_CHEST_OPEN;
            case "CHEST_CLOSE":
                return XSound.BLOCK_CHEST_CLOSE;
            case "NOTE_PLING":
                return XSound.BLOCK_NOTE_BLOCK_PLING;
            case "ORB_PICKUP":
                return XSound.ENTITY_EXPERIENCE_ORB_PICKUP;
            case "ITEM_PICKUP":
                return XSound.ENTITY_ITEM_PICKUP;
            case "VILLAGER_NO":
                return XSound.ENTITY_VILLAGER_NO;
            case "VILLAGER_YES":
                return XSound.ENTITY_VILLAGER_YES;
            case "LEVEL_UP":
            case "PLAYER_LEVELUP":
                return XSound.ENTITY_PLAYER_LEVELUP;
            case "ANVIL_USE":
                return XSound.BLOCK_ANVIL_USE;
            default:
                return null;
        }
    }

    /**
     * Phát âm thanh với string sound name - Phiên bản cải thiện cho 1.12.2
     *
     * @param player    Người chơi
     * @param soundName Tên âm thanh
     * @param volume    Âm lượng
     * @param pitch     Cao độ
     * @return true nếu thành công
     */
    public static boolean playSoundByName(Player player, String soundName, float volume, float pitch) {
        if (player == null || soundName == null || soundName.isEmpty()) {
            return false;
        }

        // Ưu tiên sử dụng XSound trực tiếp cho cross-version compatibility
        if (playWithXSound(player, soundName, volume, pitch)) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().info("§a[SoundDebug] Successfully played via XSound: " + soundName);
            }
            return true;
        }

        // Fallback với SoundWrapper
        if (SoundWrapper.playSound(player, soundName, volume, pitch)) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().info("§a[SoundDebug] Successfully played via SoundWrapper: " + soundName);
            }
            return true;
        }

        // Fallback với logic cũ cho 1.12.2
        if (IS_PRE_113) {
            String legacySound = getVersionCompatibleSound(soundName);
            if (legacySound != null) {
                try {
                    player.playSound(player.getLocation(), legacySound, volume, pitch);
                    if (Storage.getStorage().isDebug()) {
                        Storage.getStorage().getLogger().info("§a[SoundDebug] Fallback success with legacy mapping: " + legacySound);
                    }
                    return true;
                } catch (Exception ignored) {
                    // Tiếp tục với logic khác
                }
            }
        }

        // Thử string-based với tên gốc
        if (playStringSoundSafely(player, soundName, volume, pitch)) {
            return true;
        }

        // Fallback với sound cơ bản cho từng phiên bản
        String[] basicSounds;
        if (IS_PRE_113) {
            basicSounds = new String[]{"CLICK", "NOTE_PLING", "CHEST_OPEN", "ORB_PICKUP"};
        } else {
            basicSounds = new String[]{"UI_BUTTON_CLICK", "BLOCK_NOTE_BLOCK_PLING", "BLOCK_CHEST_OPEN"};
        }

        for (String basicSound : basicSounds) {
            try {
                player.playSound(player.getLocation(), basicSound, volume, pitch);
                if (Storage.getStorage().isDebug()) {
                    Storage.getStorage().getLogger().info("§a[SoundDebug] Final fallback success: " + basicSound);
                }
                return true;
            } catch (Exception ignored) {
                // Thử sound tiếp theo
            }
        }

        return false; // Không thể phát sound nào
    }

    /**
     * Thử phát âm thanh bằng string một cách an toàn với cải thiện logic cho 1.12.2
     *
     * @param player    Người chơi
     * @param soundName Tên âm thanh
     * @param volume    Âm lượng
     * @param pitch     Cao độ
     * @return true nếu thành công
     */
    private static boolean playStringSoundSafely(Player player, String soundName, float volume, float pitch) {
        if (player == null || soundName == null || soundName.isEmpty()) {
            return false;
        }

        // Đối với 1.12.2, ưu tiên chuyển đổi sound name trước
        if (IS_PRE_113) {
            String legacySound = getVersionCompatibleSound(soundName);
            if (legacySound != null) {
                try {
                    player.playSound(player.getLocation(), legacySound, volume, pitch);
                    soundNameCache.put(soundName.toUpperCase(), legacySound);
                    return true;
                } catch (Exception ignored) {
                    // Tiếp tục với logic khác
                }
            }
        }

        // Thử với tên gốc
        try {
            player.playSound(player.getLocation(), soundName, volume, pitch);
            soundNameCache.put(soundName.toUpperCase(), soundName);
            return true;
        } catch (Exception ignored) {
            // Bỏ qua
        }

        // Thử với các biến thể
        String[] variants = getSoundVariants(soundName);
        for (String variant : variants) {
            try {
                player.playSound(player.getLocation(), variant, volume, pitch);
                soundNameCache.put(soundName.toUpperCase(), variant);
                return true;
            } catch (Exception ignored) {
                // Bỏ qua
            }
        }

        return false;
    }

    /**
     * Lấy sound tương thích theo phiên bản với mapping đầy đủ cho 1.12.2
     *
     * @param soundName Tên sound gốc
     * @return Tên sound tương thích hoặc null
     */
    private static String getVersionCompatibleSound(String soundName) {
        if (soundName == null || soundName.isEmpty()) {
            return null;
        }

        String upperName = soundName.toUpperCase();

        if (IS_PRE_113) {
            // Chuyển từ tên mới sang tên cũ cho 1.12.2
            switch (upperName) {
                case "BLOCK_NOTE_BLOCK_PLING":
                    return "NOTE_PLING";
                case "BLOCK_CHEST_OPEN":
                    return "CHEST_OPEN";
                case "BLOCK_CHEST_CLOSE":
                    return "CHEST_CLOSE";
                case "UI_BUTTON_CLICK":
                    return "CLICK";
                case "ENTITY_VILLAGER_NO":
                    return "VILLAGER_NO";
                case "ENTITY_EXPERIENCE_ORB_PICKUP":
                    return "ORB_PICKUP";
                case "ENTITY_ITEM_PICKUP":
                    return "ITEM_PICKUP";
                case "ENTITY_PLAYER_LEVELUP":
                    return "LEVEL_UP";
                case "ENTITY_PLAYER_LEVEL_UP":
                    return "LEVEL_UP";
                case "BLOCK_STONE_BUTTON_CLICK_ON":
                    return "CLICK";
                case "BLOCK_WOOD_BUTTON_CLICK_ON":
                    return "CLICK";
                // Thêm các mapping khác cho 1.12.2
                case "ENTITY_GENERIC_HURT":
                    return "HURT_FLESH";
                case "ENTITY_GENERIC_DEATH":
                    return "HURT_FLESH";
                case "BLOCK_ANVIL_USE":
                    return "ANVIL_USE";
                case "BLOCK_ANVIL_BREAK":
                    return "ANVIL_BREAK";
                // Thêm mappings cho sounds mới trong 1.21.4
                case "BLOCK_TRIAL_SPAWNER_SPAWN_MOB":
                    return "BLOCK_SPAWNER_SPAWN";
                case "BLOCK_VAULT_OPEN_SHUTTER":
                    return "CHEST_OPEN";
                case "BLOCK_VAULT_CLOSE_SHUTTER":
                    return "CHEST_CLOSE";
                case "ITEM_MACE_SMASH_AIR":
                    return "ENTITY_GENERIC_HURT";
                case "ITEM_MACE_SMASH_GROUND":
                    return "ENTITY_GENERIC_HURT";
                default:
                    return null;
            }
        } else {
            // Chuyển từ tên cũ sang tên mới cho 1.13+
            switch (upperName) {
                case "NOTE_PLING":
                    return "BLOCK_NOTE_BLOCK_PLING";
                case "CHEST_OPEN":
                    return "BLOCK_CHEST_OPEN";
                case "CHEST_CLOSE":
                    return "BLOCK_CHEST_CLOSE";
                case "CLICK":
                    return "UI_BUTTON_CLICK";
                case "VILLAGER_NO":
                    return "ENTITY_VILLAGER_NO";
                case "ORB_PICKUP":
                    return "ENTITY_EXPERIENCE_ORB_PICKUP";
                case "ITEM_PICKUP":
                    return "ENTITY_ITEM_PICKUP";
                case "LEVEL_UP":
                    return "ENTITY_PLAYER_LEVELUP";
                case "HURT_FLESH":
                    return "ENTITY_GENERIC_HURT";
                case "ANVIL_USE":
                    return "BLOCK_ANVIL_USE";
                case "ANVIL_BREAK":
                    return "BLOCK_ANVIL_BREAK";
                // Mappings cho legacy sounds đến 1.21.4
                case "BLOCK_SPAWNER_SPAWN":
                    return IS_1_21_4_OR_HIGHER ? "BLOCK_TRIAL_SPAWNER_SPAWN_MOB" : "BLOCK_SPAWNER_SPAWN";
                default:
                    return null;
            }
        }
    }

    /**
     * Phát âm thanh từ config string với cải thiện xử lý lỗi cho 1.12.2
     *
     * @param player      Người chơi
     * @param soundConfig Config string dạng "SOUND:VOLUME:PITCH"
     */
    public static void playSoundFromConfig(Player player, String soundConfig) {
        if (player == null || soundConfig == null || soundConfig.isEmpty()) {
            return;
        }

        if (Storage.getStorage().isDebug()) {
            Storage.getStorage().getLogger().info("§e[SoundDebug] Playing sound from config: " + soundConfig);
        }

        // Ưu tiên sử dụng SoundWrapper cho config
        if (SoundWrapper.playSoundFromConfig(player, soundConfig)) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().info("§a[SoundDebug] Successfully played sound from config via SoundWrapper");
            }
            return;
        }

        // Fallback với logic cũ
        try {
            String[] parts = soundConfig.split(":");
            String soundName = parts[0].trim();
            float volume = parts.length > 1 ? Float.parseFloat(parts[1].trim()) : 1.0f;
            float pitch = parts.length > 2 ? Float.parseFloat(parts[2].trim()) : 1.0f;

            // Đảm bảo volume và pitch trong giới hạn hợp lý
            volume = Math.max(0.0f, Math.min(2.0f, volume));
            pitch = Math.max(0.5f, Math.min(2.0f, pitch));

            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().info("§e[SoundDebug] Fallback - Sound: " + soundName + ", Volume: " + volume + ", Pitch: " + pitch);
            }

            // Chuyển đổi sound name cho phiên bản 1.12.2
            String compatibleSoundName = getVersionCompatibleSound(soundName);
            if (compatibleSoundName != null) {
                if (Storage.getStorage().isDebug()) {
                    Storage.getStorage().getLogger().info("§e[SoundDebug] Converted sound: " + soundName + " -> " + compatibleSoundName);
                }
                soundName = compatibleSoundName;
            }

            if (!playSoundByName(player, soundName, volume, pitch)) {
                if (Storage.getStorage().isDebug()) {
                    Storage.getStorage().getLogger().warning("§c[SoundDebug] Failed to play sound, using final fallback");
                }
                // Fallback với SoundWrapper
                SoundWrapper.playSound(player, SoundWrapper.getDefaultSoundName(), volume, pitch);
            }
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("§c[SoundDebug] Exception in playSoundFromConfig: " + e.getMessage());
            }
            // Fallback với SoundWrapper
            SoundWrapper.playSound(player, SoundWrapper.getDefaultSoundName(), 0.5f, 1.0f);
        }
    }

    /**
     * Phát sound mặc định an toàn - sử dụng string thay vì Sound enum
     *
     * @param player Người chơi
     * @param volume Âm lượng
     * @param pitch  Cao độ
     */
    private static void playDefaultSound(Player player, float volume, float pitch) {
        if (player == null) return;

        // Danh sách các sound mặc định theo thứ tự ưu tiên
        String[] defaultSounds;
        if (IS_PRE_113) {
            defaultSounds = new String[]{"CLICK", "NOTE_PLING", "CHEST_OPEN", "ORB_PICKUP"};
        } else {
            defaultSounds = new String[]{"UI_BUTTON_CLICK", "BLOCK_NOTE_BLOCK_PLING", "BLOCK_CHEST_OPEN", "ENTITY_EXPERIENCE_ORB_PICKUP"};
        }

        // Thử từng sound cho đến khi thành công
        for (String soundName : defaultSounds) {
            try {
                player.playSound(player.getLocation(), soundName, volume, pitch);
                if (Storage.getStorage().isDebug()) {
                    Storage.getStorage().getLogger().info("§a[SoundDebug] Used default sound: " + soundName);
                }
                return; // Thành công, thoát
            } catch (Exception ignored) {
                // Thử sound tiếp theo
            }
        }

        if (Storage.getStorage().isDebug()) {
            Storage.getStorage().getLogger().warning("§c[SoundDebug] Could not play any default sound");
        }
    }

    /**
     * Phát âm thanh đơn giản chỉ với string (không sử dụng Sound enum)
     *
     * @param player    Người chơi
     * @param soundName Tên âm thanh
     * @param volume    Âm lượng
     * @param pitch     Cao độ
     */
    public static void playSimpleStringSound(Player player, String soundName, float volume, float pitch) {
        if (player == null || soundName == null) {
            return;
        }

        // Debug logging cho 1.12.2
        if (Storage.getStorage().isDebug() && IS_PRE_113) {
            Storage.getStorage().getLogger().info("§e[SoundDebug] Attempting to play sound: " + soundName + " (1.12.2)");
        }

        try {
            // Đối với 1.12.2, thử chuyển đổi sound name trước
            if (IS_PRE_113) {
                String legacySound = getVersionCompatibleSound(soundName);
                if (legacySound != null) {
                    if (Storage.getStorage().isDebug()) {
                        Storage.getStorage().getLogger().info("§a[SoundDebug] Converted " + soundName + " -> " + legacySound);
                    }
                    player.playSound(player.getLocation(), legacySound, volume, pitch);
                    return;
                }
            }

            player.playSound(player.getLocation(), soundName, volume, pitch);

            if (Storage.getStorage().isDebug() && IS_PRE_113) {
                Storage.getStorage().getLogger().info("§a[SoundDebug] Successfully played: " + soundName);
            }
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("§c[SoundDebug] Failed to play sound: " + soundName + " - " + e.getMessage());
            }
        }
    }

    /**
     * Phát âm thanh an toàn cho 1.12.2 - tránh hoàn toàn Sound enum
     *
     * @param player      Người chơi
     * @param modernSound Tên âm thanh phiên bản mới (1.13+)
     * @param legacySound Tên âm thanh phiên bản cũ (1.12.2)
     * @param volume      Âm lượng
     * @param pitch       Cao độ
     * @return true nếu thành công
     */
    public static boolean playSoundSafeFor112(Player player, String modernSound, String legacySound, float volume, float pitch) {
        if (player == null) {
            return false;
        }

        if (Storage.getStorage().isDebug()) {
            Storage.getStorage().getLogger().info("§e[SoundDebug] Safe play - Modern: " + modernSound + ", Legacy: " + legacySound);
        }

        // Chọn sound phù hợp với phiên bản
        String soundToPlay = (IS_PRE_113 && legacySound != null) ? legacySound : modernSound;

        // Thử phát sound chính
        try {
            player.playSound(player.getLocation(), soundToPlay, volume, pitch);
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().info("§a[SoundDebug] Successfully played: " + soundToPlay);
            }
            return true;
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("§c[SoundDebug] Failed primary sound: " + soundToPlay + " - " + e.getMessage());
            }
        }

        // Thử sound fallback
        String fallbackSound = (IS_PRE_113 && legacySound != null) ? modernSound : legacySound;
        if (fallbackSound != null && !fallbackSound.equals(soundToPlay)) {
            try {
                player.playSound(player.getLocation(), fallbackSound, volume, pitch);
                if (Storage.getStorage().isDebug()) {
                    Storage.getStorage().getLogger().info("§a[SoundDebug] Successfully played fallback: " + fallbackSound);
                }
                return true;
            } catch (Exception e) {
                if (Storage.getStorage().isDebug()) {
                    Storage.getStorage().getLogger().warning("§c[SoundDebug] Failed fallback sound: " + fallbackSound + " - " + e.getMessage());
                }
            }
        }

        return false;
    }


    /**
     * Phát âm thanh với tên cụ thể (overload method)
     *
     * @param player    Người chơi
     * @param soundName Tên âm thanh
     * @param volume    Âm lượng
     * @param pitch     Cao độ
     */
    public static void playSound(Player player, String soundName, float volume, float pitch) {
        playSoundByName(player, soundName, volume, pitch);
    }

    /**
     * Xóa cache sound để tối ưu bộ nhớ
     */
    public static void clearSoundCache() {
        soundCache.clear();
        soundNameCache.clear();
    }

    /**
     * Lấy kích thước cache hiện tại
     *
     * @return Kích thước cache
     */
    public static int getCacheSize() {
        return soundCache.size() + soundNameCache.size();
    }
}
