package com.hongminh54.storage.Manager;

import com.cryptomorin.xseries.XMaterial;
import com.hongminh54.storage.Database.PlayerData;
import com.hongminh54.storage.Storage;
import com.hongminh54.storage.Utils.File;
import com.hongminh54.storage.Utils.Number;
import org.bukkit.Bukkit;
import org.bukkit.block.Block;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.jetbrains.annotations.Contract;
import org.jetbrains.annotations.NotNull;

import java.io.IOException;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Level;


public class MineManager {

    // Khóa đồng bộ hóa để đảm bảo an toàn khi lưu dữ liệu
    private static final Object saveDataLock = new Object();
    // Thêm biến để đánh dấu quá trình lưu đang diễn ra cho mỗi người chơi
    private static final Map<String, Boolean> savingInProgress = new ConcurrentHashMap<>();
    // Thời gian kiểm tra tính nhất quán cuối cùng cho mỗi người chơi
    private static final Map<String, Long> lastConsistencyCheck = new HashMap<>();
    // Khoảng thời gian tối thiểu giữa các lần kiểm tra tính nhất quán (ms)
    private static final long CONSISTENCY_CHECK_INTERVAL = 120000; // 1 phút
    public static HashMap<String, Integer> playerdata = new HashMap<>();
    public static HashMap<Player, Integer> playermaxdata = new HashMap<>();
    public static HashMap<String, String> blocksdata = new HashMap<>();
    public static HashMap<String, String> blocksdrop = new HashMap<>();
    public static HashMap<Player, Boolean> toggle = new HashMap<>();

    public static int getPlayerBlock(@NotNull Player p, String material) {
        if (!playerdata.containsKey(p.getName() + "_" + material)) {
            return 0; // Trả về 0 nếu không tìm thấy khóa trong map
        }
        return playerdata.get(p.getName() + "_" + material);
    }

    public static boolean hasPlayerBlock(@NotNull Player p, String material) {
        return playerdata.containsKey(p.getName() + "_" + material);
    }

    public static int getMaxBlock(Player p) {
        return playermaxdata.get(p);
    }

    @Contract(" -> new")
    public static @NotNull List<String> getPluginBlocks() {
        return new ArrayList<>(blocksdata.values());
    }

    public static void addPluginBlocks(String material) {
        blocksdata.put(material, material);

    }

    public static @NotNull PlayerData getPlayerDatabase(@NotNull Player player) {

        PlayerData playerStats = Storage.db.getData(player.getName());

        if (playerStats == null) {
            // Tạo mới với giá trị mặc định từ config
            boolean defaultAutoPickup = File.getConfig().getBoolean("settings.default_auto_pickup");
            playerStats = new PlayerData(player.getName(), createNewData(), File.getConfig().getInt("settings.default_max_storage"), "{}", defaultAutoPickup);
            Storage.db.createTable(playerStats);
            toggle.put(player, defaultAutoPickup);
            Storage.getStorage().getLogger().info("Tạo dữ liệu mới cho " + player.getName() + " với auto-pickup mặc định: " + defaultAutoPickup);
        } else {
            // Sử dụng giá trị từ database và cập nhật vào bộ nhớ
            boolean autoPickupFromDB = playerStats.isAutoPickup();
            toggle.put(player, autoPickupFromDB);
            Storage.getStorage().getLogger().info("Tải auto-pickup cho " + player.getName() + " từ DB: " + autoPickupFromDB);
        }

        return playerStats;
    }

    private static @NotNull String createNewData() {
        StringBuilder mapAsString = new StringBuilder("{");
        for (String block : getPluginBlocks()) {
            mapAsString.append(block).append("=").append(0).append(", ");
        }
        mapAsString.delete(mapAsString.length() - 2, mapAsString.length()).append("}");
        return mapAsString.toString();
    }

    public static @NotNull String convertOfflineData(Player p) {
        if (p == null) {
            Storage.getStorage().getLogger().severe("Lỗi nghiêm trọng: Cố gắng chuyển đổi dữ liệu với player null!");
            return "{}";
        }

        try {
            // Kiểm tra và đếm xem có dữ liệu thực nào không
            boolean hasRealData = false;
            int totalItems = 0;
            Map<String, Integer> playerItemData = new HashMap<>();

            for (String block : getPluginBlocks()) {
                String key = p.getName() + "_" + block;
                if (playerdata.containsKey(key)) {
                    int amount = playerdata.getOrDefault(key, 0);
                    if (amount > 0) {
                        hasRealData = true;
                        totalItems += amount;
                        playerItemData.put(block, amount); // Lưu trữ dữ liệu vào map tạm thời
                    }
                }
            }

            // Ghi log số lượng vật phẩm
            if (hasRealData) {
                Storage.getStorage().getLogger().info("Chuyển đổi dữ liệu kho của " + p.getName() + " với tổng số " + totalItems + " vật phẩm");
            }

            // Bắt đầu chuyển đổi
            StringBuilder mapAsString = new StringBuilder("{");
            boolean firstItem = true;

            // Duyệt qua tất cả các loại block đã đăng ký
            for (String block : getPluginBlocks()) {
                String key = p.getName() + "_" + block;
                int amount = playerdata.getOrDefault(key, 0);

                // Nếu block có trong playerItemData hoặc cần thiết phải thêm
                if (!firstItem) {
                    mapAsString.append(", ");
                }

                mapAsString.append(block).append("=").append(amount);
                firstItem = false;
            }

            // Đóng chuỗi JSON
            mapAsString.append("}");

            // Kiểm tra kết quả
            String result = mapAsString.toString();

            // Nếu kết quả là "{}" nhưng có dữ liệu thực
            if (result.equals("{}") && hasRealData) {
                Storage.getStorage().getLogger().severe("LỖI NGHIÊM TRỌNG: Chuyển đổi dữ liệu của " + p.getName() + " thành chuỗi rỗng mặc dù có " + totalItems + " vật phẩm!");

                // Tạo chuỗi JSON từ dữ liệu đã lưu trữ tạm thời
                StringBuilder backup = new StringBuilder("{");
                boolean first = true;

                for (Map.Entry<String, Integer> entry : playerItemData.entrySet()) {
                    if (!first) {
                        backup.append(", ");
                    }
                    backup.append(entry.getKey()).append("=").append(entry.getValue());
                    first = false;
                }

                backup.append("}");
                String backupResult = backup.toString();

                if (!backupResult.equals("{}")) {
                    Storage.getStorage().getLogger().info("Đã khôi phục dữ liệu từ bản sao lưu tạm thời: " + backupResult);
                    return backupResult;
                }
            }

            // Ghi log kết quả cuối cùng
            if (hasRealData) {
                Storage.getStorage().getLogger().info("Kết quả chuyển đổi: " + result.substring(0, Math.min(100, result.length())) +
                        (result.length() > 100 ? "..." : ""));
            }

            return result;
        } catch (Exception e) {
            Storage.getStorage().getLogger().severe("Lỗi nghiêm trọng khi chuyển đổi dữ liệu của " + p.getName() + ": " + e.getMessage());
            e.printStackTrace();

            // Tạo dữ liệu dự phòng trong trường hợp lỗi
            StringBuilder emergencyData = new StringBuilder("{");
            boolean firstItem = true;
            int recoveredItems = 0;

            for (String block : getPluginBlocks()) {
                String key = p.getName() + "_" + block;
                if (playerdata.containsKey(key)) {
                    int amount = playerdata.get(key);
                    if (amount > 0) {
                        if (!firstItem) {
                            emergencyData.append(", ");
                        }
                        emergencyData.append(block).append("=").append(amount);
                        firstItem = false;
                        recoveredItems++;
                    }
                }
            }

            emergencyData.append("}");
            String emergencyResult = emergencyData.toString();

            if (recoveredItems > 0) {
                Storage.getStorage().getLogger().info("Đã khôi phục " + recoveredItems + " vật phẩm trong trường hợp khẩn cấp: " + emergencyResult);
                return emergencyResult;
            }

            return "{}"; // Trả về chuỗi trống trong trường hợp lỗi nghiêm trọng không khôi phục được
        }
    }

    public static @NotNull List<String> convertOnlineData(@NotNull String data) {
        String data_1 = data.replace("{", "").replace("}", "").replace(" ", "");
        List<String> list = new ArrayList<>();
        List<String> testlist = new ArrayList<>();
        for (String blocklist : data_1.split(",")) {
            String[] block = blocklist.split("=");
            if (getPluginBlocks().contains(block[0])) {
                list.add(block[0] + ";" + block[1]);
                testlist.add(block[0]);
            }
        }
        for (String blocklist : getPluginBlocks()) {
            if (!testlist.contains(blocklist)) {
                list.add(blocklist + ";" + 0);
                testlist.add(blocklist);
            }
        }
        return list;
    }

    public static void setBlock(@NotNull Player p, String material, int amount) {
        playerdata.put(p.getName() + "_" + material, amount);
    }

    public static void setBlock(Player p, @NotNull List<String> list) {
        list.forEach(block -> {
            String[] block_data = block.split(";");
            String material = block_data[0] + ";" + block_data[1];
            int amount = Number.getInteger(block_data[2]);
            setBlock(p, material, amount);
        });
    }

    /**
     * Kiểm tra tính nhất quán dữ liệu trước khi thực hiện thao tác với kho
     *
     * @param player Người chơi
     * @return true nếu dữ liệu nhất quán hoặc đã được sửa chữa
     */
    public static boolean ensureDataConsistency(Player player) {
        if (player == null) {
            return false;
        }

        // Kiểm tra xem đã kiểm tra gần đây chưa để tránh làm chậm hệ thống
        long currentTime = System.currentTimeMillis();
        Long lastCheck = lastConsistencyCheck.get(player.getName());

        if (lastCheck != null && currentTime - lastCheck < CONSISTENCY_CHECK_INTERVAL) {
            return true; // Đã kiểm tra gần đây, bỏ qua
        }

        // Cập nhật thời gian kiểm tra
        lastConsistencyCheck.put(player.getName(), currentTime);

        // Kiểm tra tính nhất quán
        // Kiểm tra dữ liệu đơn giản
        boolean consistent = true;

        // Thực hiện đồng bộ hóa nếu dữ liệu không nhất quán
        if (!consistent) {
            Storage.getStorage().getLogger().warning("Phát hiện dữ liệu không nhất quán khi thực hiện thao tác với kho của " + player.getName() + "!");
            return false;
        }

        return true;
    }

    /**
     * Thêm số lượng khoáng sản cho người chơi
     *
     * @param p        Người chơi
     * @param material Loại khoáng sản
     * @param amount   Số lượng
     * @return Thành công hay không
     */
    public static boolean addBlockAmount(Player p, String material, int amount) {
        if (p == null || material == null || amount <= 0) {
            return false;
        }

        try {
            // Lấy dữ liệu hiện tại với synchronized để đảm bảo an toàn luồng
            synchronized (saveDataLock) {
                // Kiểm tra dữ liệu trước khi thêm
                String key = p.getName() + "_" + material;
                int currentAmount = getPlayerBlock(p, material);
                int maxStorage = getMaxBlock(p);

                // Kiểm tra xem có vượt quá giới hạn không
                if (currentAmount >= maxStorage) {
                    // Kho đã đầy hoàn toàn
                    try {
                        // Kiểm tra tính năng auto_store_when_full
                        boolean autoStoreWhenFull = File.getConfig().getBoolean("settings.auto_store_when_full", true);
                        if (autoStoreWhenFull) {
                            // Thực hiện auto-save khi kho đầy
                            if (Storage.getStorage().isDebug()) {
                                Storage.getStorage().getLogger().info("Kho đầy - Tự động lưu dữ liệu cho " + p.getName());
                            }
                            savePlayerData(p);
                        }

                        // Ghi nhận vào log để theo dõi
                        if (Storage.getStorage().isDebug()) {
                            Storage.getStorage().getLogger().info("Kho đầy: " + p.getName() + " không thể thêm " +
                                    amount + " " + material + ", hiện có " + currentAmount + "/" + maxStorage);
                        }
                    } catch (Exception e) {
                        // Bỏ qua lỗi nhỏ
                    }
                    return false;
                } else if (currentAmount + amount > maxStorage) {
                    // Kho không đủ chỗ cho toàn bộ số lượng, chỉ thêm đến mức tối đa
                    int allowedAmount = maxStorage - currentAmount;
                    if (allowedAmount <= 0) {
                        return false; // Phòng trường hợp đã đầy
                    }

                    // Cập nhật số lượng được phép thêm
                    amount = allowedAmount;

                    // Ghi log
                    if (Storage.getStorage().isDebug()) {
                        Storage.getStorage().getLogger().info("Kho gần đầy: " + p.getName() + " chỉ có thể thêm " +
                                allowedAmount + "/" + amount + " " + material + ", hiện có " + currentAmount + "/" + maxStorage);
                    }
                }

                // Kiểm tra tính nhất quán
                if (System.currentTimeMillis() - lastConsistencyCheck.getOrDefault(p.getName(), 0L) > CONSISTENCY_CHECK_INTERVAL) {
                    ensureDataConsistency(p);
                    lastConsistencyCheck.put(p.getName(), System.currentTimeMillis());
                }

                // Thực hiện thêm dữ liệu
                if (playerdata.containsKey(key)) {
                    playerdata.put(key, currentAmount + amount);
                } else {
                    setBlock(p, material, amount);
                }

                // Ghi nhận hoạt động gửi vào kho
                StatsManager.recordDeposit(p, amount);

                // Ghi log nếu cần
                if (Storage.getStorage().isDebug()) {
                    Storage.getStorage().getLogger().info("Đã thêm " + amount + " " + material + " cho " + p.getName() +
                            ", hiện có " + (currentAmount + amount));
                }

                return true;
            }
        } catch (Exception e) {
            Storage.getStorage().getLogger().severe("Lỗi khi thêm khoáng sản cho " + p.getName() + ": " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * Giảm số lượng khoáng sản của người chơi
     *
     * @param p        Người chơi
     * @param material Loại khoáng sản
     * @param amount   Số lượng
     * @return Thành công hay không
     */
    public static boolean removeBlockAmount(Player p, String material, int amount) {
        if (p == null || material == null || amount <= 0) {
            return false;
        }

        try {
            // Lấy dữ liệu hiện tại với synchronized để đảm bảo an toàn luồng
            synchronized (saveDataLock) {
                // Kiểm tra dữ liệu trước khi giảm
                String key = p.getName() + "_" + material;
                int currentAmount = getPlayerBlock(p, material);

                // Kiểm tra xem có đủ khoáng sản để giảm không
                if (currentAmount < amount) {
                    try {
                        // Ghi nhận vào log để theo dõi
                        if (Storage.getStorage().isDebug()) {
                            Storage.getStorage().getLogger().warning("Không đủ khoáng sản: " + p.getName() + " không thể giảm " +
                                    amount + " " + material + ", hiện chỉ có " + currentAmount);
                        }
                    } catch (Exception e) {
                        // Bỏ qua lỗi nhỏ
                    }
                    return false;
                }

                // Kiểm tra tính nhất quán
                if (System.currentTimeMillis() - lastConsistencyCheck.getOrDefault(p.getName(), 0L) > CONSISTENCY_CHECK_INTERVAL) {
                    ensureDataConsistency(p);
                    lastConsistencyCheck.put(p.getName(), System.currentTimeMillis());
                }

                // Thực hiện giảm dữ liệu
                playerdata.put(key, currentAmount - amount);

                // Ghi nhận hoạt động rút ra
                StatsManager.recordWithdraw(p, amount);

                // Ghi log nếu cần
                if (Storage.getStorage().isDebug()) {
                    Storage.getStorage().getLogger().info("Đã giảm " + amount + " " + material + " của " + p.getName() +
                            ", còn lại " + (currentAmount - amount));
                }

                return true;
            }
        } catch (Exception e) {
            Storage.getStorage().getLogger().severe("Lỗi khi giảm khoáng sản của " + p.getName() + ": " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * Load dữ liệu người chơi từ cơ sở dữ liệu
     *
     * @param p Người chơi
     */
    public static void loadPlayerData(@NotNull Player p) {
        try {
            String data = "";
            PlayerData playerData = getPlayerDatabase(p);

            if (playerData != null) {
                data = playerData.getData();
                Storage.getStorage().getLogger().info("Đã tải dữ liệu của " + p.getName() + " từ database: " + data.substring(0, Math.min(50, data.length())) + (data.length() > 50 ? "..." : ""));
            } else {
                // Tạo mới dữ liệu nếu không tìm thấy
                data = createNewData();
                Storage.getStorage().getLogger().info("Tạo mới dữ liệu cho " + p.getName());

                // Tạo dữ liệu mặc định trong database sử dụng sync method
                int max_amount = File.getConfig().getInt("settings.default_max_storage");
                boolean defaultAutoPickup = File.getConfig().getBoolean("settings.default_auto_pickup", true);
                PlayerData newData = new PlayerData(p.getName(), data, max_amount, "{}", defaultAutoPickup);
                Storage.db.createTableSync(newData); // Sử dụng sync method
                toggle.put(p, defaultAutoPickup);
            }

            // Kiểm tra tính hợp lệ của dữ liệu
            if (data.equals("{}")) {
                Storage.getStorage().getLogger().warning("Cảnh báo: Dữ liệu tải được cho " + p.getName() + " là rỗng. Kiểm tra xem có lỗi không.");

                // Kiểm tra có file backup khẩn cấp không
                java.io.File dataFolder = Storage.getStorage().getDataFolder();
                java.io.File backupFolder = new java.io.File(dataFolder, "emergency_backups");

                if (backupFolder.exists()) {
                    java.io.File[] backupFiles = backupFolder.listFiles((dir, name) -> name.startsWith(p.getName() + "_"));

                    if (backupFiles != null && backupFiles.length > 0) {
                        // Sắp xếp theo thứ tự thời gian giảm dần (mới nhất đầu tiên)
                        java.util.Arrays.sort(backupFiles, (f1, f2) -> Long.compare(f2.lastModified(), f1.lastModified()));

                        Storage.getStorage().getLogger().info("Tìm thấy " + backupFiles.length + " bản backup khẩn cấp cho " + p.getName() + ". Đang thử khôi phục từ file mới nhất.");

                        try {
                            java.util.Scanner scanner = new java.util.Scanner(backupFiles[0]);
                            String backupData = scanner.useDelimiter("\\A").next();
                            scanner.close();

                            // Xử lý dữ liệu backup
                            backupData = backupData.replace("{", "").replace("}", "").replace(" ", "");
                            String[] entries = backupData.split(",");

                            Map<String, Integer> restoredData = new HashMap<>();
                            for (String entry : entries) {
                                String[] parts = entry.split("=");
                                if (parts.length == 2) {
                                    String material = parts[0].trim();
                                    int amount = Integer.parseInt(parts[1].trim());
                                    if (amount > 0) {
                                        restoredData.put(material, amount);
                                    }
                                }
                            }

                            // Tạo chuỗi dữ liệu mới
                            if (!restoredData.isEmpty()) {
                                StringBuilder restoredJson = new StringBuilder("{");
                                boolean first = true;

                                for (String block : getPluginBlocks()) {
                                    if (!first) {
                                        restoredJson.append(", ");
                                    }
                                    int amount = restoredData.getOrDefault(block, 0);
                                    restoredJson.append(block).append("=").append(amount);
                                    first = false;
                                }

                                restoredJson.append("}");
                                data = restoredJson.toString();

                                Storage.getStorage().getLogger().info("Đã khôi phục dữ liệu từ backup: " + data.substring(0, Math.min(50, data.length())) + (data.length() > 50 ? "..." : ""));

                                // Cập nhật dữ liệu khôi phục vào database sử dụng urgent method
                                if (playerData != null) {
                                    PlayerData restoredPlayerData = new PlayerData(p.getName(), data, playerData.getMax(),
                                            playerData.getStatsData(), toggle.getOrDefault(p, true));
                                    Storage.db.updateTableUrgent(restoredPlayerData); // Urgent vì đây là khôi phục
                                }
                            }
                        } catch (Exception ex) {
                            Storage.getStorage().getLogger().warning("Không thể khôi phục từ file backup: " + ex.getMessage());
                        }
                    }
                }
            }

            // Chuyển đổi dữ liệu thành danh sách và tải vào bộ nhớ
            List<String> list = convertOnlineData(data);

            // Đặt giới hạn kho
            int maxStorage = (playerData != null) ? playerData.getMax() : File.getConfig().getInt("settings.default_max_storage");
            playermaxdata.put(p, maxStorage);

            // Tải dữ liệu vào bộ nhớ
            setBlock(p, list);

            // Gọi StatsManager để tải dữ liệu thống kê
            Storage.getStorage().getLogger().info("Đang tải dữ liệu thống kê cho " + p.getName());
            if (playerData != null) {
                StatsManager.loadPlayerStats(p);

                // Set trạng thái tự động nhặt
                toggle.put(p, playerData.isAutoPickup());
            } else {
                StatsManager.initPlayerStats(p);
            }

            // Kiểm tra sau khi tải dữ liệu
            int itemCount = 0;
            for (String material : getPluginBlocks()) {
                if (hasPlayerBlock(p, material)) {
                    int amount = getPlayerBlock(p, material);
                    if (amount > 0) {
                        itemCount += amount;
                    }
                }
            }
            Storage.getStorage().getLogger().info("Tổng số vật phẩm sau khi tải cho " + p.getName() + ": " + itemCount);

        } catch (Exception ex) {
            Storage.getStorage().getLogger().severe("Lỗi khi tải dữ liệu người chơi " + p.getName() + ": " + ex.getMessage());
            ex.printStackTrace();

            // Khởi tạo dữ liệu mặc định nếu có lỗi
            StatsManager.initPlayerStats(p);
            toggle.put(p, File.getConfig().getBoolean("settings.default_auto_pickup", true));
            playermaxdata.put(p, File.getConfig().getInt("settings.default_max_storage"));
        }
    }

    /**
     * Lưu dữ liệu người chơi vào cơ sở dữ liệu
     *
     * @param p Người chơi
     */
    public static void savePlayerData(@NotNull Player p) {
        // Kiểm tra và đánh dấu nếu đang lưu dữ liệu
        if (savingInProgress.getOrDefault(p.getName(), false)) {
            Storage.getStorage().getLogger().warning("Đang có một quá trình lưu dữ liệu cho " + p.getName() + " đang diễn ra. Bỏ qua yêu cầu lưu mới.");
            return;
        }

        // Đánh dấu đang lưu dữ liệu
        savingInProgress.put(p.getName(), true);

        // Sử dụng khóa đồng bộ để đảm bảo chỉ một luồng lưu dữ liệu cùng lúc
        synchronized (saveDataLock) {
            try {
                // Log dữ liệu gốc trong bộ nhớ để debug
                StringBuilder materialDebug = new StringBuilder();
                int totalItems = 0;
                Map<String, Integer> playerItems = new HashMap<>();

                for (String material : getPluginBlocks()) {
                    String key = p.getName() + "_" + material;
                    if (playerdata.containsKey(key)) {
                        int amount = playerdata.get(key);
                        if (amount > 0) {
                            materialDebug.append(material).append(": ").append(amount).append(", ");
                            totalItems += amount;
                            playerItems.put(material, amount);
                        }
                    }
                }

                // Kiểm tra số lượng vật phẩm
                if (totalItems <= 0 && playerItems.isEmpty()) {
                    // Kiểm tra xem người chơi có từng có dữ liệu không
                    PlayerData existingData = Storage.db.getData(p.getName());
                    if (existingData != null && !existingData.getData().equals("{}")) {
                        // Nếu người chơi từng có dữ liệu nhưng bây giờ không có, đây có thể là lỗi
                        Storage.getStorage().getLogger().warning("CẢNH BÁO: Dữ liệu của " + p.getName() +
                                " trong bộ nhớ đang trống, nhưng có dữ liệu trong DB! Có thể đã xảy ra lỗi, bỏ qua việc lưu.");
                        savingInProgress.put(p.getName(), false);
                        return;
                    }
                }

                Storage.getStorage().getLogger().info("Dữ liệu khoáng sản của " + p.getName() + " trước khi lưu: " + totalItems + " vật phẩm - " + materialDebug);

                // Kiểm tra xem tổng số vật phẩm có giảm đột ngột không
                PlayerData existingData = Storage.db.getData(p.getName());
                if (existingData != null && !existingData.getData().equals("{}")) {
                    Map<String, Integer> dbItems = parseDataString(existingData.getData());
                    int dbTotalItems = 0;
                    for (int amount : dbItems.values()) {
                        dbTotalItems += amount;
                    }

                    // Nếu số lượng giảm đột ngột hơn 30% và tổng không phải là 0
                    if (dbTotalItems > 0 && totalItems > 0 && (double) totalItems / dbTotalItems < 0.7) {
                        Storage.getStorage().getLogger().warning("Phát hiện giảm đột ngột: DB(" + dbTotalItems +
                                ") -> Memory(" + totalItems + ") cho " + p.getName() + ". Kiểm tra trước khi lưu...");

                        // Chỉ cho phép lưu khi giảm liên quan đến các hoạt động rút/bán/chuyển gần đây
                        boolean hasRecentActivity = false;
                        long recentTime = System.currentTimeMillis() - 60000; // 1 phút trước

                        if (StatsManager.hasRecentActivity(p, recentTime)) {
                            hasRecentActivity = true;
                            Storage.getStorage().getLogger().info("Phát hiện hoạt động rút/bán/chuyển gần đây, cho phép lưu.");
                        }

                        if (!hasRecentActivity) {
                            Storage.getStorage().getLogger().severe("Không tìm thấy hoạt động gần đây để giải thích sự giảm đột ngột, từ chối lưu!");
                            savingInProgress.put(p.getName(), false);
                            return;
                        }
                    }
                }

                // Tạo file backup trước khi lưu để phòng dữ liệu bị mất
                try {
                    createEmergencyBackup(p, playerItems);
                } catch (Exception backupEx) {
                    Storage.getStorage().getLogger().warning("Không thể tạo backup khẩn cấp cho " + p.getName() + ": " + backupEx.getMessage());
                }

                // Lấy dữ liệu người chơi từ cơ sở dữ liệu
                PlayerData playerData = Storage.db.getData(p.getName());

                // Chuyển đổi dữ liệu thành chuỗi JSON để lưu vào database
                String playerDataJson = convertOfflineData(p);

                // Nếu JSON rỗng và có dữ liệu thực, tạo JSON dự phòng
                if (playerDataJson.equals("{}") && totalItems > 0) {
                    Storage.getStorage().getLogger().warning("Cảnh báo: Dữ liệu JSON của " + p.getName() + " trống rỗng mặc dù có " + totalItems + " vật phẩm trong bộ nhớ!");

                    // Tạo JSON dự phòng từ Map playerItems
                    StringBuilder backupJson = new StringBuilder("{");
                    boolean first = true;

                    for (Map.Entry<String, Integer> entry : playerItems.entrySet()) {
                        if (!first) {
                            backupJson.append(", ");
                        }
                        backupJson.append(entry.getKey()).append("=").append(entry.getValue());
                        first = false;
                    }

                    backupJson.append("}");
                    playerDataJson = backupJson.toString();

                    Storage.getStorage().getLogger().info("Đã tạo JSON dự phòng: " + playerDataJson);
                }

                // Xác định thông tin của người chơi để lưu
                if (playerData == null) {
                    int max_amount = File.getConfig().getInt("settings.default_max_storage");
                    boolean defaultAutoPickup = File.getConfig().getBoolean("settings.default_auto_pickup", true);
                    playerData = new PlayerData(p.getName(), playerDataJson, max_amount, "{}", defaultAutoPickup);
                } else {
                    boolean currentToggleState = toggle.getOrDefault(p, true);
                    String statsData = playerData.getStatsData();
                    // Sử dụng dữ liệu thống kê hiện tại nếu có
                    if (statsData == null || statsData.isEmpty()) {
                        statsData = StatsManager.convertStatsToString(p);
                    }

                    // Kiểm tra xem dữ liệu trong database có ít hơn đáng kể so với bộ nhớ không
                    boolean isDataLossDetected = false;
                    if (!playerData.getData().equals("{}")) {
                        Map<String, Integer> dbItems = parseDataString(playerData.getData());
                        int dbTotal = dbItems.values().stream().mapToInt(Integer::intValue).sum();

                        // Nếu tổng số vật phẩm trong DB ít hơn đáng kể và bộ nhớ có dữ liệu
                        if (totalItems > dbTotal + 20 && dbTotal > 0) {
                            Storage.getStorage().getLogger().warning("Phát hiện khả năng mất dữ liệu: DB(" + dbTotal +
                                    ") < Memory(" + totalItems + ") cho " + p.getName());
                            isDataLossDetected = true;
                        }
                    }

                    playerData = new PlayerData(p.getName(), playerDataJson, playerData.getMax(), statsData, currentToggleState);
                }

                // Xác nhận JSON hợp lệ
                if (playerDataJson.isEmpty() || !playerDataJson.startsWith("{") || !playerDataJson.endsWith("}")) {
                    Storage.getStorage().getLogger().severe("JSON không hợp lệ cho " + p.getName() + ": " + playerDataJson);
                    return; // Không lưu dữ liệu không hợp lệ
                }

                // Tạo một kết nối riêng biệt để đảm bảo lưu thành công
                Connection conn = null;
                try {
                    // Lưu dữ liệu vào cơ sở dữ liệu sử dụng sync method
                    Storage.db.updateTableSync(playerData);
                    Storage.getStorage().getLogger().info("Đã lưu dữ liệu thành công của " + p.getName() + " với " + totalItems + " vật phẩm.");
                } catch (Exception sqlEx) {
                    Storage.getStorage().getLogger().severe("Lỗi khi lưu dữ liệu: " + sqlEx.getMessage());

                    // Thử lưu lại bằng cách sử dụng kết nối và SQL riêng biệt để tránh lồng transaction
                    PreparedStatement ps = null;
                    try {
                        if (Storage.db instanceof com.hongminh54.storage.Database.SQLite) {
                            conn = Storage.db.getConnection();
                            // Đảm bảo autoCommit = true để không tạo transaction mới
                            conn.setAutoCommit(true);

                            ps = conn.prepareStatement("UPDATE " + Storage.db.table +
                                    " SET data = ?, max = ?, statsData = ?, auto_pickup = ? WHERE player = ?");
                            ps.setString(1, playerDataJson);
                            ps.setInt(2, playerData.getMax());
                            ps.setString(3, playerData.getStatsData());
                            ps.setBoolean(4, playerData.isAutoPickup());
                            ps.setString(5, playerData.getPlayer());
                            int updated = ps.executeUpdate();

                            Storage.getStorage().getLogger().info("Đã lưu dữ liệu lại thành công sau lỗi, số hàng cập nhật: " + updated);
                        }
                    } catch (Exception retryEx) {
                        Storage.getStorage().getLogger().severe("Không thể lưu dữ liệu sau khi thử lại: " + retryEx.getMessage());
                    } finally {
                        try {
                            if (ps != null) ps.close();
                        } catch (Exception e) {
                        }

                        if (conn != null) {
                            try {
                                conn.close();
                            } catch (Exception e) {
                            }
                        }
                    }
                }

                // Kiểm tra lại dữ liệu sau khi lưu
                PlayerData checkData = Storage.db.getData(p.getName());
                if (checkData != null) {
                    if (checkData.getData().equals("{}") && totalItems > 0) {
                        Storage.getStorage().getLogger().severe("SAU KHI LƯU: Dữ liệu của " + p.getName() + " vẫn trống rỗng. Đang thử khắc phục...");

                        // Cập nhật lại với các biện pháp khắc phục
                        checkData = new PlayerData(p.getName(), playerDataJson, checkData.getMax(),
                                checkData.getStatsData(), toggle.getOrDefault(p, true));

                        // Thực hiện thủ công để đảm bảo dữ liệu được lưu
                        Connection fixConn = null;
                        PreparedStatement fixPs = null;
                        try {
                            fixConn = Storage.db.getConnection();
                            // Đảm bảo autoCommit = true để tránh các vấn đề transaction lồng nhau
                            fixConn.setAutoCommit(true);

                            fixPs = fixConn.prepareStatement("UPDATE " + Storage.db.table +
                                    " SET data = ? WHERE player = ?");
                            fixPs.setString(1, playerDataJson);
                            fixPs.setString(2, p.getName());
                            int updated = fixPs.executeUpdate();

                            Storage.getStorage().getLogger().info("Đã khắc phục dữ liệu thủ công, hàng cập nhật: " + updated);
                        } catch (Exception e) {
                            Storage.getStorage().getLogger().severe("Không thể khắc phục dữ liệu: " + e.getMessage());
                        } finally {
                            try {
                                if (fixPs != null) fixPs.close();
                            } catch (Exception e) {
                            }

                            if (fixConn != null) {
                                try {
                                    fixConn.close();
                                } catch (Exception e) {
                                }
                            }
                        }

                        // Kiểm tra lần cuối
                        PlayerData finalCheck = Storage.db.getData(p.getName());
                        if (finalCheck != null && !finalCheck.getData().equals("{}")) {
                            Storage.getStorage().getLogger().info("Đã khắc phục thành công, dữ liệu hiện tại: " +
                                    finalCheck.getData().substring(0, Math.min(50, finalCheck.getData().length())));
                        }
                    }
                }
            } catch (Exception e) {
                Storage.getStorage().getLogger().severe("Lỗi khi lưu dữ liệu người chơi " + p.getName() + ": " + e.getMessage());
                e.printStackTrace();

                // Thử backup khẩn cấp
                try {
                    java.io.File dataFolder = Storage.getStorage().getDataFolder();
                    java.io.File backupFolder = new java.io.File(dataFolder, "emergency_backups");
                    if (!backupFolder.exists()) {
                        backupFolder.mkdirs();
                    }

                    // Dọn dẹp các file backup cũ trước khi tạo cái mới
                    cleanupOldBackups(p.getName(), backupFolder);

                    java.io.File backupFile = new java.io.File(backupFolder, p.getName() + "_async_" + System.currentTimeMillis() + ".json");
                    try (java.io.FileWriter writer = new java.io.FileWriter(backupFile)) {
                        // Tạo JSON từ Map
                        StringBuilder json = new StringBuilder("{");
                        boolean first = true;

                        // Sử dụng emergencyBackup thay vì playerItems
                        Map<String, Integer> emergencyBackup = new HashMap<>();
                        for (String key : playerdata.keySet()) {
                            if (key.startsWith(p.getName() + "_")) {
                                String material = key.substring(p.getName().length() + 1);
                                int amount = playerdata.get(key);
                                if (amount > 0) {
                                    emergencyBackup.put(material, amount);
                                    if (!first) {
                                        json.append(", ");
                                    }
                                    json.append("\"").append(material).append("\": ").append(amount);
                                    first = false;
                                }
                            }
                        }
                        json.append("}");

                        writer.write(json.toString());
                        Storage.getStorage().getLogger().info("Đã tạo backup khẩn cấp bất đồng bộ cho " + p.getName() + " tại " + backupFile.getPath());
                    }
                } catch (Exception ex) {
                    Storage.getStorage().getLogger().severe("Không thể tạo backup khẩn cấp bất đồng bộ: " + ex.getMessage());
                }
            } finally {
                // Đánh dấu quá trình lưu đã kết thúc
                savingInProgress.put(p.getName(), false);
            }
        }
    }

    /**
     * Tạo backup khẩn cấp cho dữ liệu người chơi
     *
     * @param p    Người chơi
     * @param data Dữ liệu cần backup
     * @throws IOException Nếu có lỗi khi tạo file
     */
    private static void createEmergencyBackup(Player p, Map<String, Integer> data) throws IOException {
        // Lưu vào file riêng để khôi phục sau này - sử dụng config
        java.io.File dataFolder = Storage.getStorage().getDataFolder();
        String backupFolderName = File.getConfig().getString("backup.backup_folder", "emergency_backups");
        java.io.File backupFolder = new java.io.File(dataFolder, backupFolderName);
        if (!backupFolder.exists()) {
            backupFolder.mkdirs();
        }

        // Dọn dẹp các file backup cũ trước khi tạo cái mới
        cleanupOldBackups(p.getName(), backupFolder);

        // Sử dụng file format từ config
        String fileFormat = File.getConfig().getString("backup.file_format", "{player}_{timestamp}.json");
        String fileName = fileFormat
                .replace("{player}", p.getName())
                .replace("{timestamp}", String.valueOf(System.currentTimeMillis()));

        java.io.File backupFile = new java.io.File(backupFolder, fileName);
        try (java.io.FileWriter writer = new java.io.FileWriter(backupFile)) {
            // Tạo JSON từ Map
            StringBuilder json = new StringBuilder("{");
            boolean first = true;
            for (Map.Entry<String, Integer> entry : data.entrySet()) {
                if (!first) {
                    json.append(", ");
                }
                json.append("\"").append(entry.getKey()).append("\": ").append(entry.getValue());
                first = false;
            }
            json.append("}");

            writer.write(json.toString());

            // Thông báo backup nếu được bật trong config
            boolean notifyBackup = File.getConfig().getBoolean("backup.notify_backup", false);
            if (notifyBackup) {
                Storage.getStorage().getLogger().info("Đã tạo backup cho " + p.getName() + " tại " + backupFile.getPath());
                // Thông báo cho player nếu đang online
                if (p.isOnline()) {
                    p.sendMessage("§a[Backup] Đã tạo backup dữ liệu của bạn.");
                }
            }
        }
    }

    /**
     * Dọn dẹp các file backup cũ của người chơi
     *
     * @param playerName   Tên người chơi
     * @param backupFolder Thư mục chứa các file backup
     */
    private static void cleanupOldBackups(String playerName, java.io.File backupFolder) {
        try {
            if (!backupFolder.exists()) {
                return;
            }

            // Lấy cấu hình
            int maxBackupsPerPlayer = File.getConfig().getInt("backup.max_per_player", 5);
            int maxBackupAgeDays = File.getConfig().getInt("backup.max_age_days", 7);

            // Nếu cấu hình không có, thêm giá trị mặc định
            if (maxBackupsPerPlayer <= 0) maxBackupsPerPlayer = 5;
            if (maxBackupAgeDays <= 0) maxBackupAgeDays = 7;

            // Tìm tất cả các file backup của người chơi này
            java.io.File[] playerBackups = backupFolder.listFiles((dir, name) ->
                    name.startsWith(playerName + "_") && name.endsWith(".json"));

            if (playerBackups == null || playerBackups.length <= maxBackupsPerPlayer) {
                // Không đủ file để xóa
                return;
            }

            // Sắp xếp theo thời gian sửa đổi (mới nhất đầu tiên)
            java.util.Arrays.sort(playerBackups, (f1, f2) ->
                    Long.compare(f2.lastModified(), f1.lastModified()));

            // Lấy ngày hiện tại và tính toán thời gian tối đa
            long currentTime = System.currentTimeMillis();
            long maxAgeMillis = maxBackupAgeDays * 24 * 60 * 60 * 1000L; // Chuyển đổi ngày thành mili giây

            int removedFiles = 0;
            int oldFilesRemoved = 0;

            // Xóa các file cũ hơn số ngày quy định
            for (java.io.File file : playerBackups) {
                long fileAge = currentTime - file.lastModified();

                if (fileAge > maxAgeMillis) {
                    boolean deleted = file.delete();
                    if (deleted) {
                        oldFilesRemoved++;
                        Storage.getStorage().getLogger().fine("Đã xóa file backup cũ: " + file.getName());
                    }
                }
            }

            // Lấy lại danh sách sau khi xóa các file quá cũ
            playerBackups = backupFolder.listFiles((dir, name) ->
                    name.startsWith(playerName + "_") && name.endsWith(".json"));

            if (playerBackups == null || playerBackups.length <= maxBackupsPerPlayer) {
                // Không cần xóa thêm
                if (oldFilesRemoved > 0) {
                    Storage.getStorage().getLogger().info("Đã xóa " + oldFilesRemoved + " file backup cũ của " + playerName);
                }
                return;
            }

            // Sắp xếp lại để đảm bảo thứ tự đúng
            java.util.Arrays.sort(playerBackups, (f1, f2) ->
                    Long.compare(f2.lastModified(), f1.lastModified()));

            // Giữ lại maxBackupsPerPlayer file mới nhất, xóa còn lại
            for (int i = maxBackupsPerPlayer; i < playerBackups.length; i++) {
                boolean deleted = playerBackups[i].delete();
                if (deleted) {
                    removedFiles++;
                }
            }

            if (removedFiles > 0 || oldFilesRemoved > 0) {
                Storage.getStorage().getLogger().info("Đã xóa " + (removedFiles + oldFilesRemoved) +
                        " file backup của " + playerName + " (" + oldFilesRemoved + " file quá cũ, " +
                        removedFiles + " file vượt quá số lượng tối đa)");
            }

        } catch (Exception e) {
            Storage.getStorage().getLogger().warning("Lỗi khi dọn dẹp backup cũ: " + e.getMessage());
        }
    }

    /**
     * Dọn dẹp tất cả các file backup cũ
     * Phương thức này có thể được gọi định kỳ, ví dụ: khi server khởi động hoặc reload
     */
    public static void cleanupAllBackups() {
        // Dọn dẹp emergency backups
        cleanupEmergencyBackups();

        // Dọn dẹp database backups nếu được bật
        boolean databaseBackupEnabled = File.getConfig().getBoolean("backup.database", false);
        if (databaseBackupEnabled) {
            cleanupDatabaseBackups();
        }

        Storage.getStorage().getLogger().info("Đã hoàn thành dọn dẹp tất cả backup cũ");
    }

    /**
     * Dọn dẹp emergency backups
     */
    private static void cleanupEmergencyBackups() {
        java.io.File dataFolder = Storage.getStorage().getDataFolder();
        String backupFolderName = File.getConfig().getString("backup.backup_folder", "emergency_backups");
        java.io.File backupFolder = new java.io.File(dataFolder, backupFolderName);

        if (!backupFolder.exists()) {
            return;
        }

        java.io.File[] allBackups = backupFolder.listFiles((dir, name) -> name.endsWith(".json"));
        if (allBackups == null || allBackups.length == 0) {
            return;
        }

        // Tập hợp danh sách người chơi đã có backup
        java.util.Set<String> playerNames = new java.util.HashSet<>();

        for (java.io.File file : allBackups) {
            String fileName = file.getName();
            int underscoreIndex = fileName.indexOf('_');

            if (underscoreIndex > 0) {
                String playerName = fileName.substring(0, underscoreIndex);
                playerNames.add(playerName);
            }
        }

        // Dọn dẹp backup cho từng người chơi
        for (String playerName : playerNames) {
            cleanupOldBackups(playerName, backupFolder);
        }
    }

    /**
     * Dọn dẹp database backups - sử dụng thư mục "backups" như trong Storage.java
     */
    private static void cleanupDatabaseBackups() {
        java.io.File dataFolder = Storage.getStorage().getDataFolder();
        java.io.File dbBackupFolder = new java.io.File(dataFolder, "backups"); // Sử dụng thư mục "backups" như trong Storage.java

        if (!dbBackupFolder.exists()) {
            return;
        }

        java.io.File[] dbBackups = dbBackupFolder.listFiles((dir, name) ->
                name.endsWith(".db") || name.endsWith(".sql") || name.endsWith(".backup"));

        if (dbBackups == null || dbBackups.length == 0) {
            return;
        }

        int keepDbBackups = File.getConfig().getInt("backup.keep_db_backups", 5);
        if (keepDbBackups <= 0) keepDbBackups = 5;

        // Sắp xếp theo thời gian sửa đổi (mới nhất đầu tiên)
        java.util.Arrays.sort(dbBackups, (f1, f2) ->
                Long.compare(f2.lastModified(), f1.lastModified()));

        // Xóa các file backup database cũ, chỉ giữ lại số lượng theo config
        int removedDbBackups = 0;
        for (int i = keepDbBackups; i < dbBackups.length; i++) {
            if (dbBackups[i].delete()) {
                removedDbBackups++;
            }
        }

        if (removedDbBackups > 0) {
            Storage.getStorage().getLogger().info("Đã xóa " + removedDbBackups + " database backup cũ (sử dụng cấu hình keep_db_backups)");
        }
    }

    /**
     * Tạo database backup nếu được bật trong config
     * Sử dụng method có sẵn trong Storage.java
     */
    public static void createDatabaseBackup() {
        boolean databaseBackupEnabled = File.getConfig().getBoolean("backup.database", false);
        if (!databaseBackupEnabled) {
            return;
        }

        try {
            // Gọi method backup có sẵn trong Storage.java thông qua reflection
            // để tránh phụ thuộc trực tiếp vào private method
            java.lang.reflect.Method backupMethod = Storage.class.getDeclaredMethod("backupDatabase");
            backupMethod.setAccessible(true);
            backupMethod.invoke(Storage.getStorage());

            // Thông báo backup nếu được bật
            boolean notifyBackup = File.getConfig().getBoolean("backup.notify_backup", false);
            if (notifyBackup) {
                Storage.getStorage().getLogger().info("Đã hoàn thành database backup");
            }

        } catch (Exception e) {
            Storage.getStorage().getLogger().warning("Lỗi khi tạo database backup: " + e.getMessage());
        }
    }

    /**
     * Phân tích chuỗi dữ liệu thành Map
     *
     * @param data Chuỗi dữ liệu từ database
     * @return Map chứa dữ liệu
     */
    private static Map<String, Integer> parseDataString(String data) {
        Map<String, Integer> result = new HashMap<>();
        if (data == null || data.isEmpty() || data.equals("{}")) {
            return result;
        }

        try {
            // Loại bỏ dấu { } ở đầu và cuối
            String content = data.substring(1, data.length() - 1);
            if (!content.isEmpty()) {
                String[] pairs = content.split(", ");
                for (String pair : pairs) {
                    String[] keyValue = pair.split("=");
                    if (keyValue.length == 2) {
                        String material = keyValue[0];
                        int amount = Integer.parseInt(keyValue[1]);
                        if (amount > 0) {
                            result.put(material, amount);
                        }
                    }
                }
            }
        } catch (Exception e) {
            Storage.getStorage().getLogger().warning("Lỗi khi phân tích chuỗi dữ liệu: " + e.getMessage());
        }

        return result;
    }

    /**
     * Lưu dữ liệu người chơi bất đồng bộ vào cơ sở dữ liệu
     *
     * @param p Người chơi
     */
    public static void savePlayerDataAsync(@NotNull Player p) {
        // Lưu một bản sao của dữ liệu hiện tại trước khi bắt đầu quá trình bất đồng bộ
        final String playerName = p.getName();

        // Kiểm tra xem đã có quá trình lưu dữ liệu cho người chơi này không
        final String saveKey = "save_" + playerName.toLowerCase();
        if (savingInProgress.getOrDefault(saveKey, false)) {
            // Nếu đang lưu, ghi log ở mức fine và thoát
            if (Storage.getStorage().getLogger().isLoggable(Level.FINE)) {
                Storage.getStorage().getLogger().fine("Bỏ qua lưu dữ liệu cho " + playerName + " do đã có một tiến trình lưu khác đang chạy");
            }
            return;
        }

        // Đánh dấu đang lưu dữ liệu
        savingInProgress.put(saveKey, true);

        final Map<String, Integer> dataCopy = new HashMap<>();
        int totalItems = 0;

        // Tạo bản sao dữ liệu trong khối đồng bộ để đảm bảo tính nhất quán
        synchronized (saveDataLock) {
            for (String block : getPluginBlocks()) {
                String key = playerName + "_" + block;
                if (playerdata.containsKey(key)) {
                    int amount = playerdata.get(key);
                    if (amount > 0) {
                        dataCopy.put(block, amount);
                        totalItems += amount;
                    }
                }
            }
        }

        // Lưu trạng thái auto-pickup hiện tại
        final boolean autoPickupState = toggle.getOrDefault(p, true);
        final int finalTotalItems = totalItems;

        // Log thông tin trước khi lưu dữ liệu - chỉ log ở chế độ DEBUG hoặc mỗi 5 lần lưu
        if (Storage.getStorage().getLogger().isLoggable(Level.FINE) ||
                finalTotalItems > 10000000 || // Vẫn log khi số lượng vật phẩm rất lớn
                System.currentTimeMillis() % 5 == 0) {
            Storage.getStorage().getLogger().fine("Bắt đầu lưu dữ liệu bất đồng bộ cho " + playerName + " với " + totalItems + " vật phẩm");
        }

        // Nếu không có dữ liệu để lưu, không cần thực hiện thao tác async
        if (totalItems == 0 && !toggle.containsKey(p)) {
            // Chỉ log ở debug
            if (Storage.getStorage().getLogger().isLoggable(Level.FINE)) {
                Storage.getStorage().getLogger().fine("Bỏ qua lưu dữ liệu cho " + playerName + " do không có dữ liệu cần lưu.");
            }
            savingInProgress.remove(saveKey);
            return;
        }

        // Tạo backup dữ liệu trước khi lưu nếu có nhiều vật phẩm - sử dụng config
        int emergencyThreshold = File.getConfig().getInt("backup.emergency_threshold", 900000);
        boolean emergencyBackupsEnabled = File.getConfig().getBoolean("backup.emergency_backups", true);

        if (emergencyBackupsEnabled && totalItems > emergencyThreshold) {
            try {
                createEmergencyBackup(p, dataCopy);
            } catch (Exception e) {
                // Chỉ log lỗi, không dừng quá trình lưu
                Storage.getStorage().getLogger().warning("Không thể tạo backup trước khi lưu: " + e.getMessage());
            }
        }

        Bukkit.getScheduler().runTaskAsynchronously(Storage.getStorage(), () -> {
            try {
                // Lấy dữ liệu người chơi từ database với cơ chế thử lại
                PlayerData playerData = null;
                int retryAttempt = 0;
                Exception lastError = null;

                while (retryAttempt < 3 && playerData == null) {
                    try {
                        // Sử dụng phương thức đơn giản
                        playerData = Storage.db.getData(playerName);

                        if (playerData == null && retryAttempt < 2) {
                            // Nếu không lấy được dữ liệu, chờ và thử lại
                            Thread.sleep(500L * (retryAttempt + 1));
                            retryAttempt++;
                        }
                    } catch (Exception e) {
                        lastError = e;
                        // Lỗi khi truy vấn database, thử lại nếu có thể
                        if (retryAttempt < 2) {
                            retryAttempt++;
                            Storage.getStorage().getLogger().warning("Lỗi khi lấy dữ liệu từ DB cho " + playerName +
                                    ", thử lại lần " + retryAttempt + ": " + e.getMessage());
                            try {
                                Thread.sleep(500L * retryAttempt);
                            } catch (InterruptedException ie) {
                                Thread.currentThread().interrupt();
                                break;
                            }
                        } else {
                            // Đã hết số lần thử
                            Storage.getStorage().getLogger().severe("Không thể lấy dữ liệu từ DB sau nhiều lần thử: " + e.getMessage());
                            break;
                        }
                    }
                }

                // Nếu vẫn không lấy được dữ liệu sau khi thử lại nhiều lần và có lỗi
                if (playerData == null && lastError != null) {
                    Storage.getStorage().getLogger().severe("Không thể truy vấn dữ liệu từ database cho " + playerName + ": " + lastError.getMessage());
                    savingInProgress.remove(saveKey);
                    return;
                }

                // Chuyển đổi dữ liệu HashMap thành chuỗi JSON
                StringBuilder dataJson = new StringBuilder("{");
                boolean hasData = false;
                boolean first = true;

                // Ghi rõ tất cả các loại tài nguyên, kể cả 0
                for (String block : getPluginBlocks()) {
                    if (!first) {
                        dataJson.append(", ");
                    }
                    int amount = dataCopy.getOrDefault(block, 0);
                    dataJson.append(block).append("=").append(amount);
                    if (amount > 0) {
                        hasData = true;
                    }
                    first = false;
                }

                dataJson.append("}");
                String playerDataJson = dataJson.toString();

                // Kiểm tra tính hợp lệ của dữ liệu
                if (playerDataJson.equals("{}") && hasData) {
                    Storage.getStorage().getLogger().warning("Cảnh báo: Dữ liệu JSON của " + playerName + " trống rỗng nhưng có " + finalTotalItems + " vật phẩm trong bản sao!");

                    // Tạo lại chuỗi JSON từ dataCopy
                    StringBuilder backupJson = new StringBuilder("{");
                    boolean firstItem = true;
                    for (Map.Entry<String, Integer> entry : dataCopy.entrySet()) {
                        if (!firstItem) {
                            backupJson.append(", ");
                        }
                        backupJson.append(entry.getKey()).append("=").append(entry.getValue());
                        firstItem = false;
                    }
                    backupJson.append("}");
                    playerDataJson = backupJson.toString();

                    Storage.getStorage().getLogger().info("Đã tạo lại JSON: " + playerDataJson);
                }

                // Kiểm tra nếu dữ liệu cần tạo mới hay cập nhật
                boolean isUpdate = playerData != null;
                boolean success = false;
                int retryUpdateCount = 0;
                final int maxUpdateRetries = 3;

                // Sử dụng khối try-catch riêng cho việc lưu dữ liệu
                while (retryUpdateCount <= maxUpdateRetries) {
                    try {
                        if (!isUpdate) {
                            // Tạo mới dữ liệu
                            int max_amount = File.getConfig().getInt("settings.default_max_storage");
                            PlayerData newData = new PlayerData(playerName, playerDataJson, max_amount, "{}", autoPickupState);

                            Storage.db.createTable(newData);
                            success = true;

                            if (Storage.getStorage().getLogger().isLoggable(Level.FINE)) {
                                Storage.getStorage().getLogger().fine("Đã tạo mới dữ liệu cho " + playerName);
                            }
                        } else {
                            // Cập nhật dữ liệu hiện có
                            String statsData = playerData.getStatsData();
                            if (statsData == null) statsData = "{}";

                            PlayerData updatedData = new PlayerData(playerName, playerDataJson, playerData.getMax(), statsData, autoPickupState);

                            // Kiểm tra kết nối trước khi cập nhật để tránh lỗi transaction
                            Connection testConn = Storage.db.getConnection();
                            if (testConn == null) {
                                Storage.getStorage().getLogger().warning("Không thể lấy kết nối để cập nhật dữ liệu cho " + playerName + ", thử lại sau");
                                Thread.sleep(500);
                                retryUpdateCount++;
                                continue;
                            }

                            try {
                                testConn.isValid(1); // Kiểm tra kết nối vẫn hợp lệ
                                Storage.db.returnConnection(testConn);
                            } catch (Exception e) {
                                // Kết nối không hợp lệ
                                Storage.getStorage().getLogger().warning("Kết nối không hợp lệ: " + e.getMessage() + ", thử lại sau");
                                try {
                                    if (testConn != null && !testConn.isClosed()) {
                                        testConn.close();
                                    }
                                } catch (SQLException ex) {
                                    // Bỏ qua
                                }
                                retryUpdateCount++;
                                continue;
                            }

                            // Cập nhật bảng sử dụng sync method
                            try {
                                // Sử dụng phương thức cập nhật đồng bộ
                                Storage.db.updateTableSync(updatedData);
                                success = true;

                                if (Storage.getStorage().getLogger().isLoggable(Level.FINE)) {
                                    Storage.getStorage().getLogger().fine("Đã cập nhật dữ liệu cho " + playerName);
                                }
                                break; // Thoát khỏi vòng lặp
                            } catch (Exception e) {
                                Storage.getStorage().getLogger().warning("Lỗi khi cập nhật dữ liệu: " + e.getMessage());
                                break;
                            }
                        }

                        // Nếu đã xử lý thành công, thoát khỏi vòng lặp
                        break;
                    } catch (Exception e) {
                        String errorMessage = e.getMessage();

                        // Phân tích lỗi để cung cấp thông báo rõ ràng hơn
                        String detailedError = "Lỗi khi " + (isUpdate ? "cập nhật" : "tạo mới") + " dữ liệu: ";

                        if (errorMessage.contains("locked") || errorMessage.contains("busy")) {
                            detailedError += "Database đang bị khóa hoặc bận";
                        } else if (errorMessage.contains("disk I/O error")) {
                            detailedError += "Lỗi đĩa I/O - kiểm tra không gian lưu trữ";
                        } else if (errorMessage.contains("out of memory")) {
                            detailedError += "Hết bộ nhớ";
                        } else if (errorMessage.contains("no transaction is active")) {
                            detailedError += "Lỗi transaction - không có transaction nào đang hoạt động";
                            // Đây là lỗi đặc biệt cần xử lý
                            if (retryUpdateCount < maxUpdateRetries) {
                                retryUpdateCount++;
                                Storage.getStorage().getLogger().warning(detailedError + ", thử lại lần " + retryUpdateCount);
                                try {
                                    Thread.sleep(1000); // Chờ lâu hơn cho lỗi transaction
                                } catch (InterruptedException ie) {
                                    Thread.currentThread().interrupt();
                                }
                                continue; // Thử lại
                            }
                        } else {
                            detailedError += errorMessage;
                        }

                        if (retryUpdateCount < maxUpdateRetries) {
                            retryUpdateCount++;
                            Storage.getStorage().getLogger().warning(detailedError + ", thử lại lần " + retryUpdateCount);
                            try {
                                Thread.sleep(500L * retryUpdateCount); // Tăng thời gian chờ mỗi lần thử
                            } catch (InterruptedException ie) {
                                Thread.currentThread().interrupt();
                            }
                        } else {
                            // Đã hết số lần thử
                            Storage.getStorage().getLogger().severe(detailedError + " sau " + maxUpdateRetries + " lần thử");
                            throw e; // Ném ngoại lệ để xử lý trong khối catch ngoài
                        }
                    }
                }

                // Đánh dấu đã hoàn thành lưu
                savingInProgress.remove(saveKey);

                // Chỉ log ở chế độ DEBUG hoặc mỗi 5 lần lưu
                if (Storage.getStorage().getLogger().isLoggable(Level.FINE) ||
                        finalTotalItems > 100000000 || // Vẫn log khi số lượng vật phẩm rất lớn
                        System.currentTimeMillis() % 5 == 0) {
                    Storage.getStorage().getLogger().fine("Đã lưu dữ liệu bất đồng bộ thành công cho " + playerName + " với " + finalTotalItems + " vật phẩm");
                }

                // Kiểm tra lại để đảm bảo dữ liệu được lưu đúng (chỉ khi có nhiều dữ liệu)
                if (success && finalTotalItems > 100) {
                    try {
                        PlayerData verifyData = Storage.db.getData(playerName);
                        if (verifyData != null && verifyData.getData().equals("{}") && finalTotalItems > 0) {
                            Storage.getStorage().getLogger().warning("KIỂM TRA: Dữ liệu sau khi lưu vẫn trống. Đang khắc phục...");

                            // Tạo backup khẩn cấp khi phát hiện dữ liệu sai
                            try {
                                createEmergencyBackup(p, dataCopy);
                            } catch (Exception ignored) {
                            }

                            // Thử lưu lại một lần nữa sau khi tạo mới đối tượng PlayerData
                            PlayerData fixData = new PlayerData(playerName, playerDataJson,
                                    verifyData.getMax(), verifyData.getStatsData(), autoPickupState);
                            Storage.db.updateTableUrgent(fixData); // Urgent vì đây là fix data

                            // Kiểm tra lần cuối
                            PlayerData finalCheck = Storage.db.getData(playerName);
                            if (finalCheck != null && !finalCheck.getData().equals("{}")) {
                                Storage.getStorage().getLogger().info("Đã sửa chữa thành công dữ liệu cho " + playerName);
                            } else {
                                Storage.getStorage().getLogger().severe("Không thể sửa chữa dữ liệu cho " + playerName + " sau nhiều lần thử");

                                // Thử lại theo cách khác - lên lịch lưu dữ liệu đồng bộ
                                if (p.isOnline()) {
                                    Bukkit.getScheduler().runTask(Storage.getStorage(), () -> savePlayerData(p));
                                }
                            }
                        }
                    } catch (Exception e) {
                        Storage.getStorage().getLogger().warning("Lỗi khi kiểm tra lại dữ liệu sau khi lưu: " + e.getMessage());
                    }
                }

            } catch (Exception e) {
                String errorMsg = e.getMessage();
                boolean isDbError = e instanceof SQLException || (errorMsg != null &&
                        (errorMsg.contains("database") || errorMsg.contains("SQL") ||
                                errorMsg.contains("auto-commit") || errorMsg.contains("commit") ||
                                errorMsg.contains("rollback") || errorMsg.contains("transaction")));

                // Log lỗi
                if (isDbError) {
                    Storage.getStorage().getLogger().severe("Lỗi database khi lưu dữ liệu cho " + playerName + ": " + errorMsg);
                } else {
                    Storage.getStorage().getLogger().severe("Lỗi khi lưu dữ liệu bất đồng bộ cho " + playerName + ": " + errorMsg);

                    // Ghi log stack trace chi tiết hơn nếu không phải lỗi database
                    if (!(e instanceof SQLException)) {
                        e.printStackTrace();
                    }
                }

                // Thử backup khẩn cấp
                try {
                    // Sử dụng dataCopy đã tạo trước đó thay vì tạo mới từ playerdata
                    createEmergencyBackup(p, dataCopy);
                } catch (Exception ex) {
                    Storage.getStorage().getLogger().severe("Không thể tạo backup khẩn cấp: " + ex.getMessage());
                }

                // Thử lưu lại sau 5 giây nếu là lỗi database, hoặc 3 giây nếu lỗi khác
                long delayTicks = isDbError ? 100L : 60L; // 5 giây (100 ticks) hoặc 3 giây (60 ticks)

                Bukkit.getScheduler().runTaskLaterAsynchronously(Storage.getStorage(), () -> {
                    Storage.getStorage().getLogger().info("Đang thử lưu dữ liệu lại cho " + playerName + " sau lỗi");
                    savingInProgress.remove(saveKey); // Xóa cờ đánh dấu đang lưu

                    // Kiểm tra người chơi có online không trước khi lưu
                    Player onlinePlayer = Bukkit.getPlayer(playerName);
                    if (onlinePlayer != null && onlinePlayer.isOnline()) {
                        // Sử dụng phương thức đồng bộ thay vì bất đồng bộ để đảm bảo ổn định
                        Bukkit.getScheduler().runTask(Storage.getStorage(), () -> savePlayerData(onlinePlayer));
                    }
                }, delayTicks);
            } finally {
                // Đảm bảo cờ lưu dữ liệu được xóa khi hoàn thành hoặc có lỗi
                if (savingInProgress.getOrDefault(saveKey, false)) {
                    savingInProgress.remove(saveKey);
                }
            }
        });
    }

    public static String getDrop(@NotNull Block block) {
        // Phiên bản 1.16.5+ không có block.getData() và không cần data value
        String key = block.getType() + ";0";
        String result = blocksdrop.get(key);

        // Kiểm tra và log nếu không tìm thấy drop
        if (result == null) {
            Storage.getStorage().getLogger().fine("Không tìm thấy drop cho khối " + block.getType().name() + " với key: " + key);
            return "UNKNOWN_DROP"; // Trả về giá trị mặc định thay vì null
        }

        return result;
    }

    public static void loadBlocks() {
        if (!blocksdrop.isEmpty()) {
            blocksdrop.clear();
        }
        if (!blocksdata.isEmpty()) {
            blocksdata.clear();
        }
        for (String block_break : Objects.requireNonNull(File.getConfig().getConfigurationSection("blocks")).getKeys(false)) {
            String item_drop = File.getConfig().getString("blocks." + block_break + ".drop");
            if (item_drop != null) {
                if (!item_drop.contains(";")) {
                    // Thêm ";0" cho tất cả tên vật liệu để duy trì định dạng
                    addPluginBlocks(item_drop + ";0");
                    blocksdrop.put(block_break, item_drop + ";0");
                } else {
                    // Cho phiên bản 1.16.5+, luôn sử dụng data 0
                    String[] item_data = item_drop.split(";");
                    addPluginBlocks(item_data[0] + ";0");
                    blocksdrop.put(block_break, item_data[0] + ";0");
                }
            }
        }
    }

    public static boolean checkBreak(@NotNull Block block) {
        // Chặn các khối gỗ và lá cây không cho vào kho
        String blockType = block.getType().name().toUpperCase();
        if (blockType.contains("LOG") ||
                blockType.contains("WOOD") ||
                blockType.endsWith("_STEM") ||
                blockType.contains("STRIPPED") ||
                blockType.contains("LEAVES") ||
                blockType.equals("LEAVES") ||
                blockType.equals("LEAVES_2")) {
            return false; // Không cho phép đưa gỗ và lá cây vào kho
        }

        // Phiên bản 1.16.5+ không sử dụng data value
        if (File.getConfig().contains("blocks." + block.getType().name() + ";0.drop")) {
            return File.getConfig().getString("blocks." + block.getType().name() + ";0.drop") != null;
        } else if (File.getConfig().contains("blocks." + block.getType().name() + ".drop")) {
            return File.getConfig().getString("blocks." + block.getType().name() + ".drop") != null;
        }
        return false;
    }

    public static String getMaterial(String material) {
        String material_data = material.replace(":", ";");
        // Trong phiên bản 1.16.5+, luôn trả về vật liệu với data 0
        return material_data.split(";")[0] + ";0";
    }

    public static String getItemStackDrop(ItemStack item) {
        for (String drops : getPluginBlocks()) {
            if (drops != null) {
                // Xử lý cho các phiên bản 1.16.5+
                if (drops.contains(";")) {
                    // Trường hợp định dạng "MATERIAL;DATA" - chỉ lấy phần MATERIAL
                    Optional<XMaterial> xMaterial = XMaterial.matchXMaterial(drops.split(";")[0]);
                    if (xMaterial.isPresent()) {
                        ItemStack itemStack = xMaterial.get().parseItem();
                        if (itemStack != null && item.getType().equals(itemStack.getType())) {
                            return drops;
                        }
                    }
                } else {
                    // Trường hợp chỉ là MATERIAL
                    Optional<XMaterial> xMaterial = XMaterial.matchXMaterial(drops);
                    if (xMaterial.isPresent()) {
                        ItemStack itemStack = xMaterial.get().parseItem();
                        if (itemStack != null && item.getType().equals(itemStack.getType())) {
                            return drops;
                        }
                    }
                }
            }
        }
        return null;
    }

    /**
     * Kiểm tra trạng thái tự động nhặt vật phẩm của người chơi
     *
     * @param p Người chơi
     * @return true nếu đang bật, false nếu đang tắt
     */
    public static boolean isAutoPickup(@NotNull Player p) {
        return toggle.getOrDefault(p, File.getConfig().getBoolean("settings.default_auto_pickup"));
    }

    /**
     * Lấy số lượng khối mà người chơi có
     *
     * @param player      Người chơi
     * @param materialKey Mã khối (định dạng MATERIAL;DATA)
     * @return Số lượng khối
     */
    public static int getBlockAmount(Player player, String materialKey) {
        if (player == null || materialKey == null) {
            return 0;
        }

        if (!playerdata.containsKey(player.getName() + "_" + materialKey)) {
            return 0;
        }

        return playerdata.get(player.getName() + "_" + materialKey);
    }

    /**
     * Lấy tên hiển thị của vật liệu
     *
     * @param materialKey Mã vật liệu (định dạng MATERIAL;DATA)
     * @return Tên hiển thị của vật liệu
     */
    public static String getMaterialDisplayName(String materialKey) {
        if (materialKey == null) {
            return "Unknown";
        }

        String display = File.getConfig().getString("items." + materialKey);
        if (display != null) {
            return display;
        }

        // Nếu không có tên tùy chỉnh, trả về tên mặc định
        String[] parts = materialKey.split(";");
        String material = parts[0];

        // Chuyển đổi tên vật liệu thành tên hiển thị thân thiện
        String friendlyName = material.replace("_", " ").toLowerCase();
        StringBuilder result = new StringBuilder();
        boolean capitalizeNext = true;

        for (char c : friendlyName.toCharArray()) {
            if (c == ' ') {
                result.append(c);
                capitalizeNext = true;
            } else if (capitalizeNext) {
                result.append(Character.toUpperCase(c));
                capitalizeNext = false;
            } else {
                result.append(c);
            }
        }

        return result.toString();
    }


    /**
     * Chuyển tài nguyên từ người chơi này sang người chơi khác
     *
     * @param sender    Người gửi
     * @param receiver  Người nhận
     * @param materials Map chứa các vật liệu và số lượng cần chuyển
     * @return Map các vật liệu đã được chuyển thành công và số lượng
     */
    public static Map<String, Integer> transferResources(Player sender, Player receiver, Map<String, Integer> materials) {
        if (sender == null || receiver == null || materials == null || materials.isEmpty()) {
            return new HashMap<>();
        }

        Map<String, Integer> successfulTransfers = new HashMap<>();

        for (Map.Entry<String, Integer> entry : materials.entrySet()) {
            String material = entry.getKey();
            int amount = entry.getValue();

            if (amount <= 0) continue;

            // Kiểm tra xem người gửi có đủ tài nguyên không
            if (getBlockAmount(sender, material) >= amount) {
                // Kiểm tra không gian còn lại của người nhận
                int receiverAmount = getBlockAmount(receiver, material);
                int maxStorage = getMaxBlock(receiver);
                int availableSpace = maxStorage - receiverAmount;

                // Nếu không đủ không gian, chỉ chuyển số lượng có thể
                int transferAmount = Math.min(amount, availableSpace);

                if (transferAmount <= 0) {
                    continue; // Bỏ qua nếu không thể chuyển
                }

                // Trừ tài nguyên từ người gửi
                if (removeBlockAmount(sender, material, transferAmount)) {
                    // Thêm tài nguyên cho người nhận
                    if (addBlockAmount(receiver, material, transferAmount)) {
                        // Thêm vào danh sách chuyển thành công
                        successfulTransfers.put(material, transferAmount);

                        // Ghi lại lịch sử chuyển khoản (sử dụng phiên bản bất đồng bộ)
                        TransferManager.recordTransferAsync(sender.getName(), receiver.getName(), material, transferAmount);

                        if (Storage.getStorage().isDebug()) {
                            Storage.getStorage().getLogger().info(
                                    "Chuyển khoản: " + sender.getName() + " -> " + receiver.getName() +
                                            ", " + material + " x" + transferAmount
                            );
                        }
                    } else {
                        // Nếu không thể thêm cho người nhận, hoàn trả lại cho người gửi
                        addBlockAmount(sender, material, transferAmount);
                        if (Storage.getStorage().isDebug()) {
                            Storage.getStorage().getLogger().warning(
                                    "Không thể chuyển tài nguyên cho " + receiver.getName() + ", đã hoàn trả cho " + sender.getName()
                            );
                        }
                    }
                }
            }
        }

        return successfulTransfers;
    }


}
