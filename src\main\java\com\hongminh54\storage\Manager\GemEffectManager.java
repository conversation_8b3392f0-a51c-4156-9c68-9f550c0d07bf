package com.hongminh54.storage.Manager;

import com.hongminh54.storage.NMS.NMSAssistant;
import com.hongminh54.storage.Storage;
import com.hongminh54.storage.Utils.Chat;
import com.hongminh54.storage.Utils.ParticleEffect;
import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.Particle;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Quản lý hiệu ứng từ các k<PERSON> sản đặc biệt
 */
public class GemEffectManager {

    private static GemEffectManager instance;

    // Lưu trữ hiệu ứng <PERSON>c Quý <PERSON>ối <PERSON>n cho mỗi người chơi (UUID -> thời gian còn lại (giây))
    private final Map<UUID, Integer> weekendGemEffects = new HashMap<>();

    // Lưu trữ task cho hiệu ứng active particles
    private final Map<UUID, BukkitTask> activeEffectTasks = new HashMap<>();

    // Cấu hình
    private int gemDuration = 30 * 60; // 30 phút (đơn vị giây)
    private int miningSpeedBoost = 20; // Tăng tốc độ đào 20%
    private String activateSound = "ENTITY_ENDER_DRAGON_GROWL:0.5:1.2";
    private String activateParticle = "PORTAL:0.3:0.5:0.3:0.1:30";
    private String activeParticle = "SPELL_WITCH:0.1:0.1:0.1:0.01:1";
    private int activeParticleInterval = 60; // 3 giây
    private String expireSound = "ENTITY_PLAYER_LEVELUP:0.7:0.5";
    private String expireParticle = "SMOKE_NORMAL:0.3:0.5:0.3:0.05:20";

    // Tin nhắn từ cấu hình
    private String activateMessage = "&d&l✧ &dBạn đã kích hoạt &5Ngọc Quý Cuối Tuần&d! Tốc độ đào tăng &f+20% &dtrong &f30 phút&d!";
    private String expireMessage = "&d&l✧ &dHiệu ứng của &5Ngọc Quý Cuối Tuần &dđã hết tác dụng.";
    private String alreadyActiveMessage = "&d&l✧ &dBạn đã đang có hiệu ứng của &5Ngọc Quý Cuối Tuần&d!";
    private String refreshMessage = "&d&l✧ &dBạn đã làm mới thời gian hiệu ứng &5Ngọc Quý Cuối Tuần &f+30 phút&d!";

    // Task chính để giảm thời gian hiệu ứng
    private BukkitTask mainTask;

    private final NMSAssistant nmsAssistant = new NMSAssistant();

    private GemEffectManager() {
        loadConfig();
        startMainTask();
    }

    /**
     * Lấy instance của GemEffectManager
     *
     * @return instance
     */
    public static GemEffectManager getInstance() {
        if (instance == null) {
            instance = new GemEffectManager();
        }
        return instance;
    }

    /**
     * Tải cấu hình từ file
     */
    public void loadConfig() {
        try {
            FileConfiguration config = Storage.getStorage().getConfig();

            // Tải cấu hình hiệu ứng từ special_material.yml
            java.io.File specialOresFile = new java.io.File(Storage.getStorage().getDataFolder(), "special_ores.yml");
            if (specialOresFile.exists()) {
                FileConfiguration specialOresConfig = org.bukkit.configuration.file.YamlConfiguration.loadConfiguration(specialOresFile);

                // Tải cấu hình hiệu ứng
                ConfigurationSection gemEffects = specialOresConfig.getConfigurationSection("gem_effects.weekend_gem");
                if (gemEffects != null) {
                    gemDuration = gemEffects.getInt("duration", 30) * 60; // Chuyển từ phút sang giây
                    miningSpeedBoost = gemEffects.getInt("mining_speed_boost", 20);

                    // Tải hiệu ứng kích hoạt
                    ConfigurationSection activateEffects = gemEffects.getConfigurationSection("activate_effects");
                    if (activateEffects != null) {
                        activateSound = activateEffects.getString("sound", activateSound);
                        activateParticle = activateEffects.getString("particle", activateParticle);
                    }

                    // Tải hiệu ứng đang hoạt động
                    ConfigurationSection activeEffects = gemEffects.getConfigurationSection("active_effects");
                    if (activeEffects != null) {
                        activeParticle = activeEffects.getString("particle", activeParticle);
                        activeParticleInterval = activeEffects.getInt("particle_interval", activeParticleInterval);
                    }

                    // Tải hiệu ứng hết hạn
                    ConfigurationSection expireEffects = gemEffects.getConfigurationSection("expire_effects");
                    if (expireEffects != null) {
                        expireSound = expireEffects.getString("sound", expireSound);
                        expireParticle = expireEffects.getString("particle", expireParticle);
                    }
                }

                // Tải thông báo
                ConfigurationSection messages = specialOresConfig.getConfigurationSection("notifications.weekend_gem");
                if (messages != null) {
                    activateMessage = messages.getString("activate", activateMessage);
                    expireMessage = messages.getString("expire", expireMessage);
                    alreadyActiveMessage = messages.getString("already_active", alreadyActiveMessage);
                    refreshMessage = messages.getString("refresh", refreshMessage);
                }
            }

        } catch (Exception e) {
            Storage.getStorage().getLogger().warning("Lỗi khi tải cấu hình GemEffectManager: " + e.getMessage());
        }
    }

    /**
     * Kích hoạt hiệu ứng Ngọc Quý Cuối Tuần cho người chơi
     *
     * @param player Người chơi
     * @return true nếu kích hoạt thành công, false nếu đã có hiệu ứng và làm mới thời gian
     */
    public boolean activateWeekendGemEffect(Player player) {
        UUID uuid = player.getUniqueId();

        // Kiểm tra xem người chơi đã có hiệu ứng chưa
        if (weekendGemEffects.containsKey(uuid)) {
            // Làm mới thời gian hiệu ứng
            weekendGemEffects.put(uuid, gemDuration);
            player.sendMessage(Chat.colorize(refreshMessage));

            // Phát hiệu ứng làm mới
            playEffects(player, activateSound, activateParticle);

            return false;
        } else {
            // Kích hoạt hiệu ứng mới
            weekendGemEffects.put(uuid, gemDuration);

            // Áp dụng hiệu ứng haste (tốc độ đào)
            applyMiningSpeedEffect(player);

            // Phát hiệu ứng kích hoạt hoành tráng
            playActivationEffects(player);

            // Bắt đầu hiệu ứng hạt liên tục
            startActiveParticleEffect(player);

            // Thông báo kích hoạt
            player.sendMessage(Chat.colorize(activateMessage));

            return true;
        }
    }

    /**
     * Phát hiệu ứng kích hoạt hoành tráng cho Ngọc Quý Cuối Tuần
     *
     * @param player Người chơi
     */
    private void playActivationEffects(Player player) {
        try {
            // Phát âm thanh chính
            playEffects(player, activateSound, activateParticle);

            // Tạo hiệu ứng hạt nâng cao
            final Location loc = player.getLocation();
            final String particleName = activateParticle.split(":")[0];

            // Lên lịch hiệu ứng nâng cao (hiệu ứng đang dâng lên)
            new BukkitRunnable() {
                private final int maxSteps = 20;
                private final double radiusStart = 0.5;
                private final double radiusEnd = 2.0;
                private int step = 0;

                @Override
                public void run() {
                    if (step >= maxSteps) {
                        cancel();
                        return;
                    }

                    // Tính toán bán kính hiện tại (tăng dần)
                    double progress = (double) step / maxSteps;
                    double currentRadius = radiusStart + (radiusEnd - radiusStart) * progress;

                    // Tạo hiệu ứng hạt xung quanh người chơi
                    for (int i = 0; i < 12; i++) {
                        double angle = Math.PI * 2 * i / 12;
                        double x = Math.sin(angle) * currentRadius;
                        double z = Math.cos(angle) * currentRadius;

                        // Tạo hiệu ứng xoắn ốc đi lên
                        double y = 0.1 + (step * 0.15); // Bắt đầu từ chân và đi lên

                        ParticleEffect.spawnParticle(
                                loc.clone().add(x, y, z),
                                i == 0 ? particleName : "SPELL_WITCH", // Đường thứ 2 dùng một màu khác
                                1, // count
                                0.02, // offsetX
                                0.02, // offsetY
                                0.02, // offsetZ
                                0.01 // speed
                        );
                    }

                    step++;
                }
            }.runTaskTimer(Storage.getStorage(), 0L, 2L);

            // Thêm hiệu ứng nổ cuối cùng sau khi hoàn thành
            new BukkitRunnable() {
                @Override
                public void run() {
                    // Tạo vụ nổ hạt cuối cùng
                    for (int i = 0; i < 30; i++) {
                        double angle1 = Math.random() * Math.PI * 2;
                        double angle2 = Math.random() * Math.PI * 2;

                        double radius = 1.5 * Math.random();
                        double x = Math.sin(angle1) * Math.cos(angle2) * radius;
                        double y = Math.sin(angle1) * Math.sin(angle2) * radius + 1.5; // Tại độ cao trên người chơi
                        double z = Math.cos(angle1) * radius;

                        ParticleEffect.spawnParticle(
                                loc.clone().add(x, y, z),
                                particleName,
                                1, // count
                                0.05, // offsetX
                                0.05, // offsetY
                                0.05, // offsetZ
                                0.1 // speed
                        );
                    }

                    // Phát âm thanh bổ sung sử dụng SoundManager để tương thích đa phiên bản
                    try {
                        // Sử dụng SoundManager để xử lý tương thích âm thanh
                        SoundManager.playSound(player, "ENTITY_FIREWORK_ROCKET_TWINKLE", 0.7f, 1.0f);
                    } catch (Exception e) {
                        // Fallback với âm thanh an toàn cho tất cả phiên bản
                        try {
                            SoundManager.playSound(player, "ENTITY_EXPERIENCE_ORB_PICKUP", 0.5f, 1.2f);
                        } catch (Exception ex) {
                            // Bỏ qua lỗi âm thanh để không crash plugin
                        }
                    }
                }
            }.runTaskLater(Storage.getStorage(), 40L);
        } catch (Exception e) {
            // Fallback nếu có lỗi, sử dụng hiệu ứng cơ bản
            playEffects(player, activateSound, activateParticle);
        }
    }

    /**
     * Kết thúc hiệu ứng Ngọc Quý Cuối Tuần cho người chơi
     *
     * @param player Người chơi
     */
    public void deactivateWeekendGemEffect(Player player) {
        UUID uuid = player.getUniqueId();

        if (weekendGemEffects.containsKey(uuid)) {
            weekendGemEffects.remove(uuid);

            // Hủy hiệu ứng haste (tốc độ đào) - tương thích đa phiên bản
            PotionEffectType haste = com.hongminh54.storage.compatibility.MaterialCompatibility.getCompatiblePotionEffect("HASTE", "FAST_DIGGING");
            player.removePotionEffect(haste);

            // Hủy task hiệu ứng hạt liên tục và xóa các particles
            stopActiveParticleEffect(player);

            // Phát hiệu ứng kết thúc hoành tráng
            playExpireEffects(player);

            // Thông báo kết thúc
            player.sendMessage(Chat.colorize(expireMessage));
        }
    }

    /**
     * Kiểm tra xem người chơi có đang có hiệu ứng Ngọc Quý Cuối Tuần không
     *
     * @param player Người chơi
     * @return true nếu có hiệu ứng
     */
    public boolean hasWeekendGemEffect(Player player) {
        return weekendGemEffects.containsKey(player.getUniqueId());
    }

    /**
     * Lấy thời gian còn lại của hiệu ứng Ngọc Quý Cuối Tuần (giây)
     *
     * @param player Người chơi
     * @return thời gian còn lại (giây) hoặc 0 nếu không có hiệu ứng
     */
    public int getWeekendGemRemainingTime(Player player) {
        UUID uuid = player.getUniqueId();
        return weekendGemEffects.getOrDefault(uuid, 0);
    }

    /**
     * Áp dụng hiệu ứng tăng tốc độ đào cho người chơi
     *
     * @param player Người chơi
     */
    private void applyMiningSpeedEffect(Player player) {
        // Tính toán level của hiệu ứng Haste (tốc độ đào) dựa trên % tăng
        int hasteLevel = Math.max(1, miningSpeedBoost / 20); // 20% = level 1, 40% = level 2, ...

        // Mức tối đa là 2 để không quá mạnh
        hasteLevel = Math.min(2, hasteLevel);

        // Áp dụng hiệu ứng tốc độ đào (Haste) - tương thích đa phiên bản
        PotionEffectType haste = com.hongminh54.storage.compatibility.MaterialCompatibility.getCompatiblePotionEffect("HASTE", "FAST_DIGGING");
        player.addPotionEffect(new PotionEffect(haste, gemDuration * 20, hasteLevel - 1, false, true));
    }

    /**
     * Bắt đầu task chính để giảm thời gian hiệu ứng
     */
    private void startMainTask() {
        // Hủy task cũ nếu có
        if (mainTask != null && !mainTask.isCancelled()) {
            mainTask.cancel();
        }

        // Tạo task mới giảm thời gian hiệu ứng mỗi giây
        mainTask = new BukkitRunnable() {
            @Override
            public void run() {
                // Danh sách người chơi cần hủy hiệu ứng
                Map<UUID, Player> playersToDeactivate = new HashMap<>();

                // Duyệt qua tất cả người chơi có hiệu ứng
                for (Map.Entry<UUID, Integer> entry : weekendGemEffects.entrySet()) {
                    UUID uuid = entry.getKey();
                    int timeLeft = entry.getValue();

                    // Giảm thời gian còn lại
                    timeLeft--;

                    // Nếu hết thời gian hoặc người chơi offline, thêm vào danh sách hủy
                    if (timeLeft <= 0) {
                        Player player = Bukkit.getPlayer(uuid);
                        if (player != null && player.isOnline()) {
                            playersToDeactivate.put(uuid, player);
                        } else {
                            playersToDeactivate.put(uuid, null);
                        }
                    } else {
                        // Cập nhật thời gian còn lại
                        weekendGemEffects.put(uuid, timeLeft);
                    }
                }

                // Hủy hiệu ứng cho những người chơi đã hết thời gian
                for (Map.Entry<UUID, Player> entry : playersToDeactivate.entrySet()) {
                    UUID uuid = entry.getKey();
                    Player player = entry.getValue();

                    // Nếu người chơi online, thông báo và phát hiệu ứng
                    if (player != null) {
                        deactivateWeekendGemEffect(player);
                    } else {
                        // Người chơi offline, chỉ xóa khỏi danh sách
                        weekendGemEffects.remove(uuid);
                        stopActiveParticleEffect(uuid);
                    }
                }
            }
        }.runTaskTimer(Storage.getStorage(), 20L, 20L); // Mỗi giây (20 tick)
    }

    /**
     * Bắt đầu hiệu ứng hạt liên tục cho người chơi
     *
     * @param player Người chơi
     */
    private void startActiveParticleEffect(Player player) {
        UUID uuid = player.getUniqueId();

        // Hủy task cũ nếu có
        stopActiveParticleEffect(player);

        // Tạo task mới để phát hiệu ứng hạt liên tục
        BukkitTask task = new BukkitRunnable() {
            private final int PARTICLES_PER_CIRCLE = 16; // Số hạt trong một vòng tròn
            private int tick = 0;
            private final double radius = 0.7; // Bán kính vòng tròn
            private final double yOffset = 1.0; // Độ cao so với mặt đất
            private final double helixHeight = 2.5; // Chiều cao của hiệu ứng helix

            @Override
            public void run() {
                if (!player.isOnline() || !hasWeekendGemEffect(player)) {
                    cancel();
                    activeEffectTasks.remove(player.getUniqueId());
                    return;
                }

                // Tăng tick để tạo chuyển động
                tick++;

                // Phát âm thanh nhẹ mỗi 10 giây (200 ticks)
                if (tick % 200 == 0) {
                    try {
                        // Sử dụng SoundManager để xử lý tương thích âm thanh
                        SoundManager.playSound(player, "BLOCK_NOTE_BLOCK_CHIME", 0.3f, 1.5f);
                    } catch (Exception e) {
                        // Fallback với âm thanh an toàn
                        try {
                            SoundManager.playSound(player, "ENTITY_EXPERIENCE_ORB_PICKUP", 0.2f, 1.5f);
                        } catch (Exception ignored) {
                            // Bỏ qua lỗi âm thanh
                        }
                    }
                }

                Location loc = player.getLocation();

                // Phân tách thông tin hiệu ứng từ cấu hình
                String[] particleInfo = activeParticle.split(":");
                String particleName = particleInfo[0];

                try {
                    // HIỆU ỨNG 1: Double Helix xoắn ốc quanh người chơi
                    if (tick % 2 == 0) { // Chỉ hiển thị mỗi 2 tick để giảm tải
                        double heightStep = helixHeight / 20; // Chia chiều cao thành 20 bước

                        for (int i = 0; i < 2; i++) { // 2 đường xoắn ốc
                            for (int step = 0; step < 10; step++) { // 10 điểm trên mỗi đường xoắn
                                double angle = (tick * 0.1) + (Math.PI * i) + (step * 0.5);
                                double y = step * heightStep;
                                double x = Math.cos(angle) * radius;
                                double z = Math.sin(angle) * radius;

                                // Tạo hiệu ứng xoắn ốc với hai màu khác nhau
                                ParticleEffect.spawnParticle(
                                        loc.clone().add(x, y, z),
                                        i == 0 ? particleName : "SPELL_WITCH", // Đường thứ 2 dùng một màu khác
                                        1, // count
                                        0.02, // offsetX
                                        0.02, // offsetY
                                        0.02, // offsetZ
                                        0.0 // speed
                                );
                            }
                        }
                    }

                    // HIỆU ỨNG 2: Vòng tròn ở chân người chơi (hiện mỗi 1 giây)
                    if (tick % 20 == 0) {
                        for (int i = 0; i < PARTICLES_PER_CIRCLE; i++) {
                            double angle = Math.PI * 2 * i / PARTICLES_PER_CIRCLE;
                            double circleRadius = radius + 0.2; // Lớn hơn một chút
                            double x = Math.cos(angle) * circleRadius;
                            double z = Math.sin(angle) * circleRadius;

                            // Tạo một vòng tròn ở dưới chân
                            ParticleEffect.spawnParticle(
                                    loc.clone().add(x, 0.1, z),
                                    particleName,
                                    1, // count
                                    0.02, // offsetX
                                    0.02, // offsetY
                                    0.02, // offsetZ
                                    0.0 // speed
                            );
                        }
                    }

                    // HIỆU ỨNG 3: Hiệu ứng lấp lánh xung quanh người chơi (hiện ngẫu nhiên)
                    if (tick % 5 == 0) {
                        for (int i = 0; i < 2; i++) {
                            // Tạo vị trí ngẫu nhiên xung quanh người chơi
                            double randomAngle = Math.random() * Math.PI * 2;
                            double randomRadius = radius * (0.5 + Math.random() * 0.7); // 50-120% bán kính
                            double randomHeight = Math.random() * 2.0; // 0-2 blocks từ mặt đất

                            double x = Math.cos(randomAngle) * randomRadius;
                            double y = randomHeight;
                            double z = Math.sin(randomAngle) * randomRadius;

                            // Tạo hiệu ứng lấp lánh ngẫu nhiên
                            try {
                                // Chọn một hiệu ứng lấp lánh khác ngẫu nhiên
                                String sparkleEffect = (Math.random() < 0.3) ? "END_ROD" : "FIREWORK";
                                player.getWorld().spawnParticle(
                                        Particle.valueOf(sparkleEffect),
                                        loc.clone().add(x, y, z),
                                        1, 0.05, 0.05, 0.05, 0.02
                                );
                            } catch (Exception e) {
                                // Fallback
                                ParticleEffect.spawnParticle(
                                        loc.clone().add(x, y, z),
                                        particleName,
                                        1, // count
                                        0.02, // offsetX
                                        0.02, // offsetY
                                        0.02, // offsetZ
                                        0.0 // speed
                                );
                            }
                        }
                    }

                } catch (Exception e) {
                    Storage.getStorage().getLogger().warning("Lỗi khi phát hiệu ứng hạt: " + e.getMessage());
                    cancel();
                    activeEffectTasks.remove(player.getUniqueId());
                }
            }
        }.runTaskTimer(Storage.getStorage(), 0, activeParticleInterval / 4); // Chia 4 để hiệu ứng mượt hơn

        // Lưu task để có thể hủy sau này
        activeEffectTasks.put(uuid, task);
    }

    /**
     * Dừng hiệu ứng hạt liên tục cho người chơi
     *
     * @param player Người chơi
     */
    private void stopActiveParticleEffect(Player player) {
        stopActiveParticleEffect(player.getUniqueId());

        // Xóa các particles còn sót lại bằng cách phát hiệu ứng xóa
        try {
            // Sử dụng hiệu ứng hạt smoke để "phủ" lên các hạt còn sót
            Location loc = player.getLocation();

            // Phát 3 vòng particles để xóa hết các hạt cũ
            for (int y = 0; y < 3; y++) {
                final double yOffset = y * 0.5;

                // Tạo một vòng tròn particles lớn bao quanh để xóa
                for (int i = 0; i < 16; i++) {
                    double angle = Math.PI * 2 * i / 16;
                    double radius = 1.0;
                    double x = Math.sin(angle) * radius;
                    double z = Math.cos(angle) * radius;

                    // Sử dụng CLOUD hoặc SMOKE để xóa particles - tương thích đa phiên bản
                    try {
                        try {
                            player.getWorld().spawnParticle(
                                    Particle.SMOKE,
                                    loc.clone().add(x, yOffset, z),
                                    1, 0.1, 0.1, 0.1, 0.01
                            );
                        } catch (NoSuchFieldError e) {
                            player.getWorld().spawnParticle(
                                    Particle.valueOf("SMOKE_NORMAL"),
                                    loc.clone().add(x, yOffset, z),
                                    1, 0.1, 0.1, 0.1, 0.01
                            );
                        }
                    } catch (Exception e) {
                        // Fallback
                    }
                }
            }
        } catch (Exception e) {
            // Bỏ qua các lỗi khi xóa particles
        }
    }

    /**
     * Dừng hiệu ứng hạt liên tục theo UUID
     *
     * @param uuid UUID của người chơi
     */
    private void stopActiveParticleEffect(UUID uuid) {
        BukkitTask task = activeEffectTasks.remove(uuid);
        if (task != null && !task.isCancelled()) {
            task.cancel();
        }
    }

    /**
     * Phát hiệu ứng âm thanh và hạt
     *
     * @param player         Người chơi
     * @param soundConfig    Cấu hình âm thanh
     * @param particleConfig Cấu hình hạt
     */
    private void playEffects(Player player, String soundConfig, String particleConfig) {
        try {
            // Phát hiệu ứng âm thanh
            String[] soundParts = soundConfig.split(":");

            if (soundParts.length >= 1) {
                String soundName = soundParts[0];
                float volume = soundParts.length >= 2 ? Float.parseFloat(soundParts[1]) : 1.0f;
                float pitch = soundParts.length >= 3 ? Float.parseFloat(soundParts[2]) : 1.0f;

                // Sử dụng SoundManager để xử lý tương thích âm thanh
                SoundManager.playSound(player, soundName, volume, pitch);
            }

            // Phát hiệu ứng hạt
            ParticleEffect.spawnParticleFromConfig(player.getLocation(), particleConfig);

        } catch (Exception e) {
            Storage.getStorage().getLogger().warning("Lỗi khi phát hiệu ứng: " + e.getMessage());
        }
    }

    /**
     * Phát hiệu ứng kết thúc hoành tráng
     *
     * @param player Người chơi
     */
    private void playExpireEffects(Player player) {
        try {
            // Phát âm thanh và hiệu ứng cơ bản trước
            playEffects(player, expireSound, expireParticle);

            // Tạo hiệu ứng nâng cao khi kết thúc hiệu ứng
            final Location loc = player.getLocation();
            final String particleName = expireParticle.split(":")[0];

            // Hiệu ứng "tan biến" xoay tròn đi xuống
            new BukkitRunnable() {
                private final int maxSteps = 10;
                private int step = 0;

                @Override
                public void run() {
                    if (step >= maxSteps) {
                        cancel();
                        return;
                    }

                    // Số lượng hạt trong mỗi vòng tròn
                    int particles = 12;

                    // Bán kính vòng tròn giảm dần theo thời gian
                    double currentRadius = 1.5 * (1.0 - (double) step / maxSteps);

                    // Vị trí y giảm dần (đi xuống)
                    double y = 1.5 - (step * 0.15);

                    for (int i = 0; i < particles; i++) {
                        double angle = Math.PI * 2 * i / particles;
                        double x = Math.sin(angle) * currentRadius;
                        double z = Math.cos(angle) * currentRadius;

                        // Sử dụng hiệu ứng "smoke" hoặc hạt tắt được định nghĩa trong config
                        ParticleEffect.spawnParticle(
                                loc.clone().add(x, y, z),
                                particleName,
                                1, // count
                                0.05, // offsetX
                                0.05, // offsetY
                                0.05, // offsetZ
                                0.02 // speed
                        );
                    }

                    step++;
                }
            }.runTaskTimer(Storage.getStorage(), 0L, 2L);

        } catch (Exception e) {
            // Fallback nếu có lỗi, sử dụng hiệu ứng cơ bản
            playEffects(player, expireSound, expireParticle);
        }
    }

    /**
     * Hủy tất cả các task và dọn dẹp khi plugin tắt
     */
    public void cleanup() {
        // Hủy task chính
        if (mainTask != null && !mainTask.isCancelled()) {
            mainTask.cancel();
        }

        // Hủy tất cả task hiệu ứng liên tục
        for (BukkitTask task : activeEffectTasks.values()) {
            if (task != null && !task.isCancelled()) {
                task.cancel();
            }
        }

        // Xóa tất cả hiệu ứng haste cho người chơi đang online
        for (UUID uuid : weekendGemEffects.keySet()) {
            Player player = Bukkit.getPlayer(uuid);
            if (player != null && player.isOnline()) {
                // Hủy hiệu ứng haste - tương thích đa phiên bản
                PotionEffectType haste = com.hongminh54.storage.compatibility.MaterialCompatibility.getCompatiblePotionEffect("HASTE", "FAST_DIGGING");
                player.removePotionEffect(haste);
            }
        }

        // Xóa các danh sách
        weekendGemEffects.clear();
        activeEffectTasks.clear();
    }
} 