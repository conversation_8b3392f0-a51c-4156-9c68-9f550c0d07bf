# Storage [1.12.x - 1.21.4]

> Multi-purpose Virtual Resource Storage designed for SkyBlock and Survival servers, featuring many newly added features with ProGuard obfuscation support.

+ Author: VoChiDanh
+ Contributor: hongminh54, TYBZI

### Version 1.21.4 is currently being fixed and will be completed in the future.

## Soft Depends

[![placeholderapi](https://img.shields.io/badge/PlaceholderAPI-2.11.6-blue?style=badge)](https://www.spigotmc.org/resources/6245/) <br>
[![worldguard](https://img.shields.io/badge/WorldGuard-v6/v7-blue?style=badge)](https://dev.bukkit.org/projects/worldguard) <br>

## Development Setup

### Requirements
- Java 8+
- Eclipse IDE with Gradle Buildship plugin

### Import to Eclipse

1. **Install Gradle Buildship Plugin**
   ```
   Help → Eclipse Marketplace → Search "Gradle Buildship" → Install
   ```

2. **Import Project**
   ```
   File → Import → Gradle → Existing Gradle Project
   → Select Storage-V2 folder → Finish
   ```

3. **Wait for Gradle sync** (downloads dependencies automatically)

### Build Commands

```bash
# Standard build
./gradlew shadowJar

# Obfuscated build (with ProGuard)
./gradlew buildObfuscated

# Clean build
./gradlew clean
```

### Output Files
- Standard: `build/libs/Storage.jar` 
- There is also obfuscated Plugin: `build/libs/Storage-1.0.5-obfuscated.jar`

## If you have any questions, issues, or feature requests, please contact me at:
[![Facebook](https://img.shields.io/badge/Facebook-green?style=badge)](https://www.facebook.com/tybzione/) <br>

