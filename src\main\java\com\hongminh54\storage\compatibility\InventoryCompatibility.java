package com.hongminh54.storage.compatibility;

import com.hongminh54.storage.NMS.NMSAssistant;
import com.hongminh54.storage.Storage;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.InventoryCloseEvent;
import org.bukkit.event.inventory.InventoryDragEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.InventoryView;

/**
 * Lớp hỗ trợ tương thích InventoryView cho Minecraft 1.12.2 - 1.21.x
 * Xử lý vấn đề IncompatibleClassChangeError khi InventoryView thay đổi từ class sang interface
 */
public class InventoryCompatibility {

    private static final NMSAssistant nmsAssistant = new NMSAssistant();
    private static final boolean IS_1_21_OR_HIGHER = nmsAssistant.isVersionGreaterThanOrEqualTo(21);

    /**
     * Lấy top inventory một cách an toàn từ InventoryClickEvent
     * Xử lý tương thích gi<PERSON>a các phiên bản Minecraft
     *
     * @param event InventoryClickEvent
     * @return Top inventory hoặc null nếu có lỗi
     */
    public static Inventory getTopInventorySafely(InventoryClickEvent event) {
        if (event == null) {
            return null;
        }

        try {
            // Phương pháp trực tiếp - hoạt động với hầu hết phiên bản
            InventoryView view = event.getView();
            return view.getTopInventory();
        } catch (IncompatibleClassChangeError e) {
            // Xử lý lỗi tương thích cho phiên bản 1.21+
            // IncompatibleClassChangeError: InventoryView thay đổi từ class sang interface
            // NoSuchMethodError cũng được bắt vì nó là subclass của IncompatibleClassChangeError
            return getTopInventoryWithReflection(event);
        } catch (Exception e) {
            // Xử lý các lỗi khác
            Storage.getStorage().getLogger().warning("Lỗi không xác định khi lấy top inventory: " + e.getMessage());
            return null;
        }
    }

    /**
     * Lấy bottom inventory một cách an toàn từ InventoryClickEvent
     *
     * @param event InventoryClickEvent
     * @return Bottom inventory hoặc null nếu có lỗi
     */
    public static Inventory getBottomInventorySafely(InventoryClickEvent event) {
        if (event == null) {
            return null;
        }

        try {
            InventoryView view = event.getView();
            return view.getBottomInventory();
        } catch (IncompatibleClassChangeError e) {
            // Xử lý lỗi tương thích cho phiên bản 1.21+
            return getBottomInventoryWithReflection(event);
        } catch (Exception e) {
            Storage.getStorage().getLogger().warning("Lỗi không xác định khi lấy bottom inventory: " + e.getMessage());
            return null;
        }
    }

    /**
     * Lấy top inventory một cách an toàn từ InventoryDragEvent
     *
     * @param event InventoryDragEvent
     * @return Top inventory hoặc null nếu có lỗi
     */
    public static Inventory getTopInventorySafely(InventoryDragEvent event) {
        if (event == null) {
            return null;
        }

        try {
            InventoryView view = event.getView();
            return view.getTopInventory();
        } catch (IncompatibleClassChangeError e) {
            // Xử lý lỗi tương thích cho phiên bản 1.21+
            return getTopInventoryWithReflectionDrag(event);
        } catch (Exception e) {
            Storage.getStorage().getLogger().warning("Lỗi không xác định khi lấy top inventory từ drag event: " + e.getMessage());
            return null;
        }
    }

    /**
     * Lấy inventory một cách an toàn từ InventoryCloseEvent
     *
     * @param event InventoryCloseEvent
     * @return Inventory hoặc null nếu có lỗi
     */
    public static Inventory getInventorySafely(InventoryCloseEvent event) {
        if (event == null) {
            return null;
        }

        try {
            return event.getInventory();
        } catch (Exception e) {
            Storage.getStorage().getLogger().warning("Lỗi khi lấy inventory từ close event: " + e.getMessage());
            return null;
        }
    }

    /**
     * Lấy title của inventory một cách an toàn
     *
     * @param event InventoryClickEvent
     * @return Title của inventory hoặc empty string nếu có lỗi
     */
    public static String getInventoryTitleSafely(InventoryClickEvent event) {
        if (event == null) {
            return "";
        }

        try {
            InventoryView view = event.getView();
            return view.getTitle();
        } catch (IncompatibleClassChangeError e) {
            return getInventoryTitleWithReflection(event);
        } catch (Exception e) {
            Storage.getStorage().getLogger().warning("Lỗi khi lấy inventory title: " + e.getMessage());
            return "";
        }
    }

    /**
     * Kiểm tra xem click có phải trong top inventory không
     *
     * @param event InventoryClickEvent
     * @return true nếu click trong top inventory
     */
    public static boolean isClickInTopInventory(InventoryClickEvent event) {
        if (event == null) {
            return false;
        }

        try {
            Inventory clickedInventory = event.getClickedInventory();
            Inventory topInventory = getTopInventorySafely(event);

            return clickedInventory != null && clickedInventory.equals(topInventory);
        } catch (Exception e) {
            Storage.getStorage().getLogger().warning("Lỗi khi kiểm tra click trong top inventory: " + e.getMessage());
            return false;
        }
    }

    /**
     * Kiểm tra xem click có phải trong bottom inventory không
     *
     * @param event InventoryClickEvent
     * @return true nếu click trong bottom inventory
     */
    public static boolean isClickInBottomInventory(InventoryClickEvent event) {
        if (event == null) {
            return false;
        }

        try {
            Inventory clickedInventory = event.getClickedInventory();
            Inventory bottomInventory = getBottomInventorySafely(event);

            return clickedInventory != null && clickedInventory.equals(bottomInventory);
        } catch (Exception e) {
            Storage.getStorage().getLogger().warning("Lỗi khi kiểm tra click trong bottom inventory: " + e.getMessage());
            return false;
        }
    }

    /**
     * Lấy top inventory sử dụng reflection (fallback cho phiên bản 1.21+)
     */
    private static Inventory getTopInventoryWithReflection(InventoryClickEvent event) {
        try {
            // Sử dụng reflection để lấy InventoryView
            java.lang.reflect.Method getViewMethod = event.getClass().getMethod("getView");
            Object view = getViewMethod.invoke(event);

            // Lấy top inventory từ view
            java.lang.reflect.Method getTopInventoryMethod = view.getClass().getMethod("getTopInventory");
            return (Inventory) getTopInventoryMethod.invoke(view);
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Reflection fallback thất bại cho getTopInventory: " + e.getMessage());
            }
            return null;
        }
    }

    /**
     * Lấy bottom inventory sử dụng reflection (fallback cho phiên bản 1.21+)
     */
    private static Inventory getBottomInventoryWithReflection(InventoryClickEvent event) {
        try {
            // Sử dụng reflection để lấy InventoryView
            java.lang.reflect.Method getViewMethod = event.getClass().getMethod("getView");
            Object view = getViewMethod.invoke(event);

            // Lấy bottom inventory từ view
            java.lang.reflect.Method getBottomInventoryMethod = view.getClass().getMethod("getBottomInventory");
            return (Inventory) getBottomInventoryMethod.invoke(view);
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Reflection fallback thất bại cho getBottomInventory: " + e.getMessage());
            }
            return null;
        }
    }

    /**
     * Lấy top inventory từ drag event sử dụng reflection
     */
    private static Inventory getTopInventoryWithReflectionDrag(InventoryDragEvent event) {
        try {
            java.lang.reflect.Method getViewMethod = event.getClass().getMethod("getView");
            Object view = getViewMethod.invoke(event);

            java.lang.reflect.Method getTopInventoryMethod = view.getClass().getMethod("getTopInventory");
            return (Inventory) getTopInventoryMethod.invoke(view);
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Reflection fallback thất bại cho drag event: " + e.getMessage());
            }
            return null;
        }
    }

    /**
     * Lấy inventory title sử dụng reflection
     */
    private static String getInventoryTitleWithReflection(InventoryClickEvent event) {
        try {
            java.lang.reflect.Method getViewMethod = event.getClass().getMethod("getView");
            Object view = getViewMethod.invoke(event);

            java.lang.reflect.Method getTitleMethod = view.getClass().getMethod("getTitle");
            Object title = getTitleMethod.invoke(view);
            return title != null ? title.toString() : "";
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Reflection fallback thất bại cho getTitle: " + e.getMessage());
            }
            return "";
        }
    }

    /**
     * Đóng inventory một cách an toàn
     *
     * @param event InventoryClickEvent
     * @return true nếu đóng thành công
     */
    public static boolean closeInventorySafely(InventoryClickEvent event) {
        if (event == null || event.getWhoClicked() == null) {
            return false;
        }

        try {
            event.getWhoClicked().closeInventory();
            return true;
        } catch (Exception e) {
            Storage.getStorage().getLogger().warning("Lỗi khi đóng inventory: " + e.getMessage());
            return false;
        }
    }

    /**
     * Lấy size của inventory một cách an toàn
     *
     * @param inventory Inventory
     * @return Size của inventory hoặc 0 nếu có lỗi
     */
    public static int getInventorySize(Inventory inventory) {
        if (inventory == null) {
            return 0;
        }

        try {
            return inventory.getSize();
        } catch (Exception e) {
            Storage.getStorage().getLogger().warning("Lỗi khi lấy inventory size: " + e.getMessage());
            return 0;
        }
    }

    /**
     * Kiểm tra xem inventory có rỗng không
     *
     * @param inventory Inventory
     * @return true nếu inventory rỗng
     */
    public static boolean isInventoryEmpty(Inventory inventory) {
        if (inventory == null) {
            return true;
        }

        try {
            for (org.bukkit.inventory.ItemStack item : inventory.getContents()) {
                if (item != null && item.getType() != org.bukkit.Material.AIR) {
                    return false;
                }
            }
            return true;
        } catch (Exception e) {
            Storage.getStorage().getLogger().warning("Lỗi khi kiểm tra inventory empty: " + e.getMessage());
            return true;
        }
    }

    /**
     * Kiểm tra xem click có phải trong top inventory không (alias cho isClickInTopInventory)
     *
     * @param event InventoryClickEvent
     * @return true nếu click trong top inventory
     */
    public static boolean isClickedInventoryTop(InventoryClickEvent event) {
        return isClickInTopInventory(event);
    }
}
