package com.hongminh54.storage.compatibility;

import com.hongminh54.storage.NMS.NMSAssistant;
import com.hongminh54.storage.Storage;
import org.bukkit.Location;
import org.bukkit.World;
import org.bukkit.block.Biome;

/**
 * Lớp hỗ trợ tương thích Biome API cho Minecraft 1.12.2 - 1.21.x
 * Xử lý các vấn đề tương thích với biome system
 */
public class BiomeCompatibility {

    private static final NMSAssistant nmsAssistant = new NMSAssistant();
    private static final boolean IS_PRE_113 = nmsAssistant.isVersionLessThan(13);
    private static final boolean IS_PRE_116 = nmsAssistant.isVersionLessThan(16);
    private static final boolean IS_1_18_OR_HIGHER = nmsAssistant.isVersionGreaterThanOrEqualTo(18);

    /**
     * Lấy biome tại vị trí một cách an toàn
     *
     * @param location Vị trí
     * @return Biome hoặc null nếu có lỗi
     */
    public static Biome getBiomeSafely(Location location) {
        if (location == null || location.getWorld() == null) {
            return null;
        }

        try {
            if (IS_1_18_OR_HIGHER) {
                // Minecraft 1.18+: Sử dụng getBiome(location)
                return location.getWorld().getBiome(location);
            } else {
                // Minecraft cũ hơn: Sử dụng getBiome(x, z) hoặc getBiome(x, y, z)
                return getBiomeLegacy(location);
            }
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể lấy biome tại " + location + ": " + e.getMessage());
            }
            return null;
        }
    }

    /**
     * Lấy biome cho phiên bản cũ
     */
    private static Biome getBiomeLegacy(Location location) {
        try {
            World world = location.getWorld();
            int x = location.getBlockX();
            int z = location.getBlockZ();

            if (IS_PRE_116) {
                // Minecraft 1.12.2 - 1.15: Sử dụng getBiome(x, z)
                return world.getBiome(x, z);
            } else {
                // Minecraft 1.16 - 1.17: Sử dụng getBiome(x, y, z)
                int y = location.getBlockY();
                return world.getBiome(x, y, z);
            }
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Lỗi getBiomeLegacy: " + e.getMessage());
            }
            return null;
        }
    }

    /**
     * Set biome tại vị trí một cách an toàn
     *
     * @param location Vị trí
     * @param biome    Biome mới
     * @return true nếu set thành công
     */
    public static boolean setBiomeSafely(Location location, Biome biome) {
        if (location == null || location.getWorld() == null || biome == null) {
            return false;
        }

        try {
            if (IS_1_18_OR_HIGHER) {
                // Minecraft 1.18+: Sử dụng setBiome(location, biome)
                location.getWorld().setBiome(location, biome);
            } else {
                // Minecraft cũ hơn: Sử dụng setBiome(x, z, biome)
                setBiomeLegacy(location, biome);
            }
            return true;
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể set biome: " + e.getMessage());
            }
            return false;
        }
    }

    /**
     * Set biome cho phiên bản cũ
     */
    private static void setBiomeLegacy(Location location, Biome biome) {
        try {
            World world = location.getWorld();
            int x = location.getBlockX();
            int z = location.getBlockZ();

            if (IS_PRE_116) {
                // Minecraft 1.12.2 - 1.15: Sử dụng setBiome(x, z, biome)
                world.setBiome(x, z, biome);
            } else {
                // Minecraft 1.16 - 1.17: Sử dụng setBiome(x, y, z, biome)
                int y = location.getBlockY();
                world.setBiome(x, y, z, biome);
            }
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Lỗi setBiomeLegacy: " + e.getMessage());
            }
        }
    }

    /**
     * Lấy Biome tương thích đa phiên bản
     *
     * @param modernName Tên biome phiên bản mới (1.13+)
     * @param legacyName Tên biome phiên bản cũ (1.12.2)
     * @return Biome tương thích
     */
    public static Biome getCompatibleBiome(String modernName, String legacyName) {
        try {
            if (IS_PRE_113 && legacyName != null) {
                return Biome.valueOf(legacyName);
            } else {
                return Biome.valueOf(modernName);
            }
        } catch (IllegalArgumentException e) {
            // Thử với tên khác nếu không tìm thấy
            try {
                return Biome.valueOf(IS_PRE_113 ? modernName : legacyName);
            } catch (IllegalArgumentException ex) {
                if (Storage.getStorage().isDebug()) {
                    Storage.getStorage().getLogger().warning("Không tìm thấy Biome: " + modernName + "/" + legacyName);
                }
                return getDefaultBiome();
            }
        }
    }

    /**
     * Lấy biome mặc định an toàn
     *
     * @return Biome mặc định
     */
    public static Biome getDefaultBiome() {
        try {
            if (IS_PRE_113) {
                return Biome.valueOf("PLAINS");
            } else {
                return Biome.valueOf("PLAINS");
            }
        } catch (Exception e) {
            // Fallback cuối cùng
            return Biome.values()[0]; // Lấy biome đầu tiên có sẵn
        }
    }

    /**
     * Kiểm tra xem biome có tồn tại không
     *
     * @param biomeName Tên biome
     * @return true nếu biome tồn tại
     */
    public static boolean biomeExists(String biomeName) {
        if (biomeName == null || biomeName.isEmpty()) {
            return false;
        }

        try {
            Biome.valueOf(biomeName.toUpperCase());
            return true;
        } catch (IllegalArgumentException e) {
            return false;
        }
    }

    /**
     * Lấy tất cả biomes có sẵn
     *
     * @return Mảng tên biomes
     */
    public static String[] getAllBiomes() {
        Biome[] biomes = Biome.values();
        String[] biomeNames = new String[biomes.length];
        for (int i = 0; i < biomes.length; i++) {
            biomeNames[i] = biomes[i].name();
        }
        return biomeNames;
    }

    /**
     * Kiểm tra xem biome có phải là ocean không
     *
     * @param biome Biome cần kiểm tra
     * @return true nếu là ocean biome
     */
    public static boolean isOceanBiome(Biome biome) {
        if (biome == null) {
            return false;
        }

        String biomeName = biome.name().toUpperCase();
        return biomeName.contains("OCEAN") || biomeName.contains("DEEP");
    }

    /**
     * Kiểm tra xem biome có phải là desert không
     *
     * @param biome Biome cần kiểm tra
     * @return true nếu là desert biome
     */
    public static boolean isDesertBiome(Biome biome) {
        if (biome == null) {
            return false;
        }

        String biomeName = biome.name().toUpperCase();
        return biomeName.contains("DESERT");
    }

    /**
     * Kiểm tra xem biome có phải là forest không
     *
     * @param biome Biome cần kiểm tra
     * @return true nếu là forest biome
     */
    public static boolean isForestBiome(Biome biome) {
        if (biome == null) {
            return false;
        }

        String biomeName = biome.name().toUpperCase();
        return biomeName.contains("FOREST") || biomeName.contains("TAIGA") || biomeName.contains("JUNGLE");
    }

    /**
     * Kiểm tra xem biome có phải là mountain không
     *
     * @param biome Biome cần kiểm tra
     * @return true nếu là mountain biome
     */
    public static boolean isMountainBiome(Biome biome) {
        if (biome == null) {
            return false;
        }

        String biomeName = biome.name().toUpperCase();
        return biomeName.contains("MOUNTAIN") || biomeName.contains("HILLS") || biomeName.contains("PEAK");
    }

    /**
     * Kiểm tra xem biome có phải là nether không
     *
     * @param biome Biome cần kiểm tra
     * @return true nếu là nether biome
     */
    public static boolean isNetherBiome(Biome biome) {
        if (biome == null) {
            return false;
        }

        String biomeName = biome.name().toUpperCase();
        return biomeName.contains("NETHER") || biomeName.contains("HELL");
    }

    /**
     * Kiểm tra xem biome có phải là end không
     *
     * @param biome Biome cần kiểm tra
     * @return true nếu là end biome
     */
    public static boolean isEndBiome(Biome biome) {
        if (biome == null) {
            return false;
        }

        String biomeName = biome.name().toUpperCase();
        return biomeName.contains("END") || biomeName.contains("THE_END");
    }

    /**
     * Lấy nhiệt độ của biome một cách an toàn
     *
     * @param biome Biome
     * @return Nhiệt độ hoặc 0.5 nếu có lỗi
     */
    public static float getBiomeTemperature(Biome biome) {
        if (biome == null) {
            return 0.5f;
        }

        try {
            // Phương pháp ước tính dựa trên tên biome
            String biomeName = biome.name().toUpperCase();

            if (biomeName.contains("DESERT") || biomeName.contains("NETHER")) {
                return 2.0f; // Nóng
            } else if (biomeName.contains("SNOWY") || biomeName.contains("ICE") || biomeName.contains("FROZEN")) {
                return 0.0f; // Lạnh
            } else if (biomeName.contains("JUNGLE") || biomeName.contains("SWAMP")) {
                return 0.95f; // Ấm và ẩm
            } else if (biomeName.contains("TAIGA") || biomeName.contains("MOUNTAIN")) {
                return 0.25f; // Mát
            } else {
                return 0.8f; // Ôn hòa
            }
        } catch (Exception e) {
            return 0.5f; // Default
        }
    }

    /**
     * Set biome cho một khu vực
     *
     * @param world World
     * @param x1    Tọa độ X bắt đầu
     * @param z1    Tọa độ Z bắt đầu
     * @param x2    Tọa độ X kết thúc
     * @param z2    Tọa độ Z kết thúc
     * @param biome Biome mới
     * @return true nếu set thành công
     */
    public static boolean setBiomeRegion(World world, int x1, int z1, int x2, int z2, Biome biome) {
        if (world == null || biome == null) {
            return false;
        }

        try {
            int minX = Math.min(x1, x2);
            int maxX = Math.max(x1, x2);
            int minZ = Math.min(z1, z2);
            int maxZ = Math.max(z1, z2);

            for (int x = minX; x <= maxX; x++) {
                for (int z = minZ; z <= maxZ; z++) {
                    Location location = new Location(world, x, 64, z);
                    setBiomeSafely(location, biome);
                }
            }
            return true;
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể set biome region: " + e.getMessage());
            }
            return false;
        }
    }
}
