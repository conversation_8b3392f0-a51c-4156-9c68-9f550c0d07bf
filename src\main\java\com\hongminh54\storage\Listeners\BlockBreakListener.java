package com.hongminh54.storage.Listeners;

/**
 * Lớ<PERSON> này xử lý sự kiện khi người chơi đào khối trong trường hợp toggle của người chơi là TRUE.
 * Lớp BlockBreakEvent_.java xử lý sự kiện khi toggle của người chơi là FALSE.
 * <p>
 * Chú ý: Cả hai lớp này KHÔNG được xử lý cùng một sự kiện đào khối vì sẽ gây ra việc
 * tài nguyên được thêm hai lần vào kho của người chơi. Kiểm tra MineManager.toggle
 * đã được thêm trong BlockBreakEvent_ để tránh xung đột này.
 */

import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

import org.bukkit.Bukkit;
import org.bukkit.block.Block;
import org.bukkit.enchantments.Enchantment;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.block.BlockBreakEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.PlayerInventory;
import org.bukkit.metadata.MetadataValue;
import org.bukkit.plugin.Plugin;
import org.jetbrains.annotations.NotNull;

import com.cryptomorin.xseries.XEnchantment;
import com.hongminh54.storage.Events.MiningEvent;
import com.hongminh54.storage.Manager.MineManager;
import com.hongminh54.storage.Manager.StatsManager;
import com.hongminh54.storage.Storage;
import com.hongminh54.storage.Utils.File;
import com.hongminh54.storage.Utils.Number;
import com.hongminh54.storage.WorldGuard.WorldGuard;
import com.hongminh54.storage.compatibility.MaterialCompatibility;
import com.hongminh54.storage.compatibility.PlayerCompatibility;

public class BlockBreakListener implements Listener {

    // Thêm biến để đếm số lượng block đã đào và kích hoạt lưu dữ liệu
    private static final ConcurrentMap<UUID, Integer> mineCountSinceLastSave = new ConcurrentHashMap<>();
    // Số lượng block đào trước khi lưu dữ liệu
    private static final int SAVE_THRESHOLD = 250; // Tăng ngưỡng lưu để giảm số lần I/O

    // Cache tối ưu cho blacklisted worlds
    private static final Set<String> blacklistedWorlds = ConcurrentHashMap.newKeySet();

    // Cooldown tracking để tối ưu performance
    private static final ConcurrentHashMap<UUID, Long> lastBlockBreakTime = new ConcurrentHashMap<>();
    private static final ConcurrentHashMap<UUID, Integer> playerBreakCount = new ConcurrentHashMap<>();
    private static final ConcurrentHashMap<UUID, Long> playerResetTime = new ConcurrentHashMap<>();

    // Cache config values để tránh đọc config liên tục
    private boolean limitEnabled;
    private int maxBreaks;
    private int resetInterval;
    private static final long WORLD_CACHE_DURATION = 30000; // 30 seconds
    private static long lastWorldCacheUpdate = 0;

    static {
        // Initialize blacklisted worlds cache
        updateBlacklistedWorldsCache();
    }

    // Constructor để khởi tạo config
    public BlockBreakListener() {
        loadConfig();
    }

    // Tải cấu hình cooldown và limit từ config.yml
    public void loadConfig() {
        limitEnabled = File.getConfig().getBoolean("cooldown.limit_enabled", true);
        maxBreaks = File.getConfig().getInt("cooldown.max_breaks", 60);
        resetInterval = File.getConfig().getInt("cooldown.reset_interval", 15);
    }

    // Kiểm tra giới hạn đập block đơn giản và hiệu quả
    private boolean checkSimpleBreakLimit(UUID playerUUID, long now) {
        if (!limitEnabled) {
            return true;
        }

        // Kiểm tra thời gian reset
        Long lastReset = playerResetTime.get(playerUUID);
        if (lastReset == null || now - lastReset >= resetInterval * 1000L) {
            playerBreakCount.put(playerUUID, 1);
            playerResetTime.put(playerUUID, now);
            return true;
        }

        // Kiểm tra số lần đập
        int currentCount = playerBreakCount.getOrDefault(playerUUID, 0);
        if (currentCount >= maxBreaks) {
            return false;
        }

        playerBreakCount.put(playerUUID, currentCount + 1);
        return true;
    }

    /**
     * Cập nhật cache blacklisted worlds
     */
    private static void updateBlacklistedWorldsCache() {
        long now = System.currentTimeMillis();
        if (now - lastWorldCacheUpdate < WORLD_CACHE_DURATION) {
            return; // Cache vẫn còn hiệu lực
        }

        blacklistedWorlds.clear();
        if (File.getConfig().contains("blacklist_world")) {
            blacklistedWorlds.addAll(File.getConfig().getStringList("blacklist_world"));
        }
        lastWorldCacheUpdate = now;
    }

    // Đăng ký listener để xử lý việc chặn drop item
    public static void registerNoDropsListener(Plugin plugin) {
        Bukkit.getPluginManager().registerEvents(new Listener() {
            @EventHandler(priority = EventPriority.HIGHEST, ignoreCancelled = true)
            public void onBlockBreak(BlockBreakEvent event) {
                Block block = event.getBlock();
                if (block.hasMetadata("NoDrops")) {
                    event.setDropItems(false);
                }
            }
        }, plugin);
    }

    @EventHandler(ignoreCancelled = true, priority = EventPriority.HIGHEST)
    public void onBreak(@NotNull BlockBreakEvent e) {
        Player p = e.getPlayer();

        // Kiểm tra nhanh toggle trước - early return để giảm overhead
        if (!MineManager.toggle.getOrDefault(p, false)) {
            return;
        }

        Block block = e.getBlock();
        UUID playerUUID = p.getUniqueId();
        long now = System.currentTimeMillis();

        // Kiểm tra cooldown để tối ưu performance và tránh spam
        Long lastBreakTime = lastBlockBreakTime.get(playerUUID);
        long cooldownTime = File.getConfig().getLong("cooldown.block_break", 50);
        if (lastBreakTime != null && now - lastBreakTime < cooldownTime) {
            return; // Cooldown chưa hết
        }

        lastBlockBreakTime.put(playerUUID, now);

        // Kiểm tra giới hạn đập block để tránh lag server
        if (!checkSimpleBreakLimit(playerUUID, now)) {
            return; // Vượt quá giới hạn
        }

        // Kiểm tra điều kiện cơ bản nhanh nhất có thể
        if (!isValidBreakConditions(p, block)) {
            return;
        }

        // Kiểm tra block có thể đào được - truy cập trực tiếp
        if (!MineManager.checkBreak(block)) {
            return;
        }

        // Xử lý auto-pickup nếu được bật
        if (MineManager.isAutoPickup(p)) {
            String drop = MineManager.getDrop(block);
            if (drop == null || "UNKNOWN_DROP".equals(drop)) {
                return;
            }

            int amount = calculateDropAmount(p, block, drop);

            // Xử lý trực tiếp và nhanh nhất có thể
            if (MineManager.addBlockAmount(p, drop, amount)) {
                // Track location cho ItemSpawnListener để ngăn chặn item drop
                ItemSpawnListener.trackAutoPickupLocation(p, block.getLocation());

                // Đánh dấu block không drop items ngay lập tức
                e.setDropItems(false);

                // Cập nhật mine counter
                updateMineCounter(p);

                // Hiển thị title/actionbar - chỉ khi toggle = true
                showMiningNotification(p, drop, amount);
            }
        }
    }

    /**
     * Kiểm tra tất cả điều kiện break trong một method để tối ưu performance
     */
    private boolean isValidBreakConditions(Player p, Block block) {
        // Cache world name để tránh multiple calls
        String worldName = p.getWorld().getName();

        // Cập nhật cache blacklisted worlds nếu cần
        updateBlacklistedWorldsCache();

        // Kiểm tra blacklist world trước để early return nhanh nhất
        if (blacklistedWorlds.contains(worldName)) {
            return false;
        }

        // Kiểm tra WorldGuard nếu có
        if (Storage.isWorldGuardInstalled() && !WorldGuard.handleForLocation(p, block.getLocation())) {
            return false;
        }

        // Kiểm tra prevent_rebreak
        if (File.getConfig().getBoolean("prevent_rebreak") && isPlacedBlock(block)) {
            return false;
        }

        return true;
    }

    /**
     * Tính toán số lượng drop với tối ưu performance
     */
    private int calculateDropAmount(Player p, Block block, String drop) {
        ItemStack hand = p.getInventory().getItemInMainHand();
        // Lấy enchantment Fortune với tương thích đa phiên bản
        Enchantment fortune = XEnchantment.FORTUNE.get() != null
                ? XEnchantment.FORTUNE.get()
                : getFortuneEnchantment();

        int baseAmount = getDropAmount(block);

        // Kiểm tra fortune enchantment
        if (hand.containsEnchantment(fortune) &&
                File.getConfig().getStringList("whitelist_fortune").contains(block.getType().name())) {
            int fortuneLevel = hand.getEnchantmentLevel(fortune);
            baseAmount = Number.getRandomInteger(baseAmount, baseAmount + fortuneLevel + 2);
        }

        // Áp dụng mining event nếu có
        MiningEvent miningEvent = MiningEvent.getInstance();
        if (miningEvent.isActive()) {
            baseAmount = miningEvent.processBlockBreak(p, drop, baseAmount);
        }

        return baseAmount;
    }

    private void processFullInventory(Player p) {
        for (ItemStack itemStack : p.getInventory().getContents()) {
            if (itemStack == null) continue;

            String drop = MineManager.getItemStackDrop(itemStack);
            if (drop == null) continue;

            int old_data = MineManager.getPlayerBlock(p, drop);
            int max_storage = MineManager.getMaxBlock(p);
            int count = max_storage - old_data;
            int amount = itemStack.getAmount();

            int new_data = old_data + amount;
            int min = Math.min(count, amount);
            int replacement = new_data >= max_storage ? min : amount;

            if (MineManager.addBlockAmount(p, drop, replacement)) {
                removeItems(p, itemStack, replacement);
            }
        }
    }

    private void processBlockBreak(Player p, Block block, String drop) {
        ItemStack hand = p.getInventory().getItemInMainHand();
        // Lấy enchantment Fortune với tương thích đa phiên bản
        Enchantment fortune = XEnchantment.FORTUNE.get() != null
                ? XEnchantment.FORTUNE.get()
                : getFortuneEnchantment();

        // Tính toán số lượng vật phẩm rơi ra
        int amount;
        if (!hand.containsEnchantment(fortune)) {
            amount = getDropAmount(block);
        } else {
            if (File.getConfig().getStringList("whitelist_fortune").contains(block.getType().name())) {
                amount = Number.getRandomInteger(getDropAmount(block),
                        getDropAmount(block) + hand.getEnchantmentLevel(fortune) + 2);
            } else {
                amount = getDropAmount(block);
            }
        }

        // Áp dụng hiệu ứng sự kiện khai thác nếu có
        MiningEvent miningEvent = MiningEvent.getInstance();
        if (miningEvent.isActive()) {
            amount = miningEvent.processBlockBreak(p, drop, amount);
        }

        // Thêm tài nguyên vào kho
        if (addResourceToStorage(p, drop, amount)) {
            // Không bắt buộc phải lưu mỗi khi đào - xử lý theo ngưỡng
            updateMineCounter(p);

            // Hủy drop từ block - không thể truy cập e ở đây
            try {
                // Thêm metadata để đánh dấu block không nên drop vật phẩm
                block.setMetadata("NoDrops", new org.bukkit.metadata.FixedMetadataValue(Storage.getStorage(), true));
            } catch (Exception ex) {
                Storage.getStorage().getLogger().warning("Không thể đánh dấu block không drop: " + ex.getMessage());
            }
        }
    }

    private boolean addResourceToStorage(Player p, String drop, int amount) {
        if (MineManager.addBlockAmount(p, drop, amount)) {

            return true;
        } else {
            // Thông báo kho đã đầy
            StatsManager.sendStorageFullNotification(p, drop,
                    MineManager.getPlayerBlock(p, drop), MineManager.getMaxBlock(p));
            return false;
        }
    }

    private void updateMineCounter(Player p) {
        UUID playerUUID = p.getUniqueId();
        int mineCount = mineCountSinceLastSave.getOrDefault(playerUUID, 0) + 1;
        mineCountSinceLastSave.put(playerUUID, mineCount);

        // Lưu dữ liệu nếu đạt ngưỡng
        if (mineCount >= SAVE_THRESHOLD) {
            mineCountSinceLastSave.put(playerUUID, 0);
            // Sử dụng độ trễ lớn hơn và xử lý bất đồng bộ
            Bukkit.getScheduler().runTaskLaterAsynchronously(Storage.getStorage(), () -> {
                try {
                    MineManager.savePlayerDataAsync(p);
                } catch (Exception ex) {
                    Storage.getStorage().getLogger().warning(
                            "Lỗi khi lưu dữ liệu sau khi đào nhiều khối: " + ex.getMessage());
                }
            }, 10L); // Độ trễ 10 tick (0.5s)
        }
    }

    public void removeItems(Player player, ItemStack itemStack, long amount) {
        final PlayerInventory inv = player.getInventory();
        final ItemStack[] items = inv.getContents();
        int c = 0;
        for (int i = 0; i < items.length; ++i) {
            final ItemStack is = items[i];
            if (is != null) {
                if (itemStack != null) {
                    if (is.isSimilar(itemStack)) {
                        if (c + is.getAmount() > amount) {
                            final long canDelete = amount - c;
                            is.setAmount((int) (is.getAmount() - canDelete));
                            items[i] = is;
                            break;
                        }
                        c += is.getAmount();
                        items[i] = null;
                    }
                }
            }
        }
        inv.setContents(items);
        player.updateInventory();
    }

    private int getDropAmount(Block block) {
        int amount = 0;
        if (block != null) for (ItemStack itemStack : block.getDrops())
            if (itemStack != null) amount += itemStack.getAmount();
        return Math.max(1, amount); // Đảm bảo luôn trả về ít nhất 1
    }

    public boolean isPlacedBlock(Block b) {
        List<MetadataValue> metaDataValues = b.getMetadata("PlacedBlock");
        for (MetadataValue value : metaDataValues) {
            return value.asBoolean();
        }
        return false;
    }

    /**
     * Lấy Fortune enchantment với tương thích đa phiên bản
     * @return Fortune enchantment
     */
    private Enchantment getFortuneEnchantment() {
        return MaterialCompatibility.getCompatibleEnchantment("FORTUNE", "LOOT_BONUS_BLOCKS");
    }

    /**
     * Hiển thị thông báo title và actionbar khi đào khối
     * @param player Người chơi
     * @param drop Loại tài nguyên
     * @param amount Số lượng
     */
    private void showMiningNotification(Player player, String drop, int amount) {
        try {
            // Hiển thị actionbar nếu được bật
            if (File.getConfig().getBoolean("mine.actionbar.enable", false)) {
                try {
                    String actionBarTemplate = File.getConfig().getString("mine.actionbar.action");
                    if (actionBarTemplate != null && !actionBarTemplate.isEmpty()) {
                        String itemName = File.getConfig().getString("items." + drop);
                        String displayName = itemName != null ? itemName : drop.replace("_", " ");

                        String actionBarMessage = actionBarTemplate
                                .replace("#item#", displayName)
                                .replace("#amount#", String.valueOf(amount))
                                .replace("#storage#", String.valueOf(MineManager.getPlayerBlock(player, drop)))
                                .replace("#max#", String.valueOf(MineManager.getMaxBlock(player)));

                        // Gửi actionbar an toàn
                        PlayerCompatibility.sendActionBar(player, actionBarMessage);
                    }
                } catch (Exception ex) {
                    // Log lỗi nhưng không làm gián đoạn gameplay
                    if (Storage.getStorage().isDebug()) {
                        Storage.getStorage().getLogger().warning("Lỗi khi hiển thị actionbar: " + ex.getMessage());
                    }
                }
            }

            // Hiển thị title nếu được bật
            if (File.getConfig().getBoolean("mine.title.enable", false)) {
                try {
                    String titleTemplate = File.getConfig().getString("mine.title.title");
                    String subtitleTemplate = File.getConfig().getString("mine.title.subtitle");

                    if (titleTemplate != null && !titleTemplate.isEmpty()) {
                        String itemName = File.getConfig().getString("items." + drop);
                        String displayName = itemName != null ? itemName : drop.replace("_", " ");

                        // Xử lý title
                        String title = titleTemplate
                                .replace("#item#", displayName)
                                .replace("#amount#", String.valueOf(amount))
                                .replace("#storage#", String.valueOf(MineManager.getPlayerBlock(player, drop)))
                                .replace("#max#", String.valueOf(MineManager.getMaxBlock(player)));

                        // Xử lý subtitle
                        String subtitle = "";
                        if (subtitleTemplate != null && !subtitleTemplate.isEmpty()) {
                            subtitle = subtitleTemplate
                                    .replace("#item#", displayName)
                                    .replace("#amount#", String.valueOf(amount))
                                    .replace("#storage#", String.valueOf(MineManager.getPlayerBlock(player, drop)))
                                    .replace("#max#", String.valueOf(MineManager.getMaxBlock(player)));
                        }

                        // Gửi title an toàn với thời gian hiển thị tối ưu
                        PlayerCompatibility.sendTitle(player, title, subtitle, 10, 70, 20);
                    }
                } catch (Exception ex) {
                    // Log lỗi nhưng không làm gián đoạn gameplay
                    if (Storage.getStorage().isDebug()) {
                        Storage.getStorage().getLogger().warning("Lỗi khi hiển thị title: " + ex.getMessage());
                    }
                }
            }
        } catch (Exception ex) {
            // Log lỗi tổng quát
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Lỗi khi hiển thị mining notification: " + ex.getMessage());
            }
        }
    }
}
