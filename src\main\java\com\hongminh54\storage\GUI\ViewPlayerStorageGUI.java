package com.hongminh54.storage.GUI;

import com.cryptomorin.xseries.XMaterial;
import com.hongminh54.storage.Database.PlayerData;
import com.hongminh54.storage.GUI.manager.IGUI;
import com.hongminh54.storage.Manager.MineManager;
import com.hongminh54.storage.Manager.SoundManager;
import com.hongminh54.storage.Storage;
import com.hongminh54.storage.Utils.Chat;
import com.hongminh54.storage.Utils.File;
import com.hongminh54.storage.Utils.GUIText;
import com.hongminh54.storage.compatibility.AdvancedCompatibility;
import com.hongminh54.storage.compatibility.InventoryCompatibility;
import com.hongminh54.storage.compatibility.MaterialCompatibility;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.HandlerList;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.InventoryCloseEvent;
import org.bukkit.event.inventory.InventoryDragEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.inventory.meta.SkullMeta;
import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.List;

/**
 * GUI để xem kho khoáng sản của người chơi khác (cả online và offline)
 */
public class ViewPlayerStorageGUI implements IGUI, Listener {

    private final Player viewer; // Người xem GUI
    private final String targetName; // Tên người chơi được xem kho
    private final List<String> resources = new ArrayList<>();
    private final boolean isOnline; // Kiểm tra nếu người chơi đang online
    private final PlayerData targetData; // Dữ liệu kho của người chơi mục tiêu
    private final FileConfiguration config; // Cấu hình GUI từ YAML
    private Inventory inventory;
    private boolean listenerRegistered = false;

    /**
     * Khởi tạo GUI xem kho của người chơi khác
     *
     * @param viewer     Người chơi đang xem kho
     * @param targetName Tên người chơi được xem kho
     */
    public ViewPlayerStorageGUI(Player viewer, String targetName) {
        this.viewer = viewer;
        this.targetName = targetName;
        this.config = File.getGUIConfig("view_player_storage");

        // Validate config
        if (!validateConfig()) {
            Storage.getStorage().getLogger().warning("Config view_player_storage.yml có vấn đề cho ViewPlayerStorageGUI");
        }

        // Kiểm tra xem người chơi mục tiêu có online không
        Player target = Bukkit.getPlayer(targetName);
        this.isOnline = (target != null && target.isOnline());

        // Lấy dữ liệu kho từ database nếu người chơi offline, hoặc từ cache nếu online
        if (isOnline) {
            // Người chơi online, lấy dữ liệu từ cache
            this.targetData = null; // Không cần dữ liệu từ database vì sẽ dùng cache
        } else {
            // Người chơi offline, lấy dữ liệu từ database
            this.targetData = Storage.db.getData(targetName);

            // Nếu không tìm thấy dữ liệu
            if (this.targetData == null) {
                String errorMsg = config.getString("error_messages.player_not_found", "&cKhông tìm thấy dữ liệu kho của người chơi &e{player}")
                        .replace("{player}", targetName);
                viewer.sendMessage(Chat.colorizewp(errorMsg));
                return;
            }
        }

        // Lấy danh sách tài nguyên từ cấu hình
        fetchResources();

        // Đăng ký listener
        registerListener();
    }

    /**
     * Lấy danh sách tài nguyên từ cấu hình
     */
    private void fetchResources() {
        FileConfiguration config = File.getConfig();
        if (config.contains("items")) {
            resources.addAll(config.getConfigurationSection("items").getKeys(false));
        }
    }

    /**
     * Đăng ký listener cho GUI
     */
    private void registerListener() {
        if (!listenerRegistered) {
            Bukkit.getPluginManager().registerEvents(this, Storage.getStorage());
            listenerRegistered = true;
        }
    }

    /**
     * Hủy đăng ký listener khi đóng GUI
     */
    private void unregisterListener() {
        if (listenerRegistered) {
            HandlerList.unregisterAll(this);
            listenerRegistered = false;
        }
    }

    @NotNull
    @Override
    public Inventory getInventory() {
        // Nếu không tìm thấy dữ liệu người chơi offline
        if (!isOnline && targetData == null) {
            String errorMsg = config.getString("error_messages.no_data", "Không có dữ liệu để hiển thị");
            return Bukkit.createInventory(null, 9, GUIText.format(errorMsg));
        }

        // Tạo inventory với kích thước từ config
        int size = config.getInt("size", 6) * 9;
        String status = isOnline ? config.getString("status.online", "&2[Online]") : config.getString("status.offline", "&c[Offline]");
        String title = config.getString("title", "&8Kho của: &a{player} {status}")
                .replace("{player}", targetName)
                .replace("{status}", status);

        inventory = Bukkit.createInventory(null, size, GUIText.format(title));

        // Phát âm thanh mở GUI
        playOpenSound();

        // Thêm các item trang trí
        addDecorativeItems();

        // Thêm thông tin người chơi
        addPlayerInfo();

        // Thêm nút đóng
        addCloseButton();

        // Thêm các tài nguyên vào GUI
        addResources();

        return inventory;
    }

    /**
     * Validate config file
     */
    private boolean validateConfig() {
        try {
            if (config == null) {
                return false;
            }

            // Kiểm tra các trường bắt buộc
            if (!config.contains("title") || !config.contains("size") || !config.contains("items")) {
                return false;
            }

            // Kiểm tra size hợp lệ
            int size = config.getInt("size", 6);
            return size >= 1 && size <= 6;
        } catch (Exception e) {
            Storage.getStorage().getLogger().warning("Error validating config: " + e.getMessage());
            return false;
        }
    }

    /**
     * Phát âm thanh mở GUI từ config
     */
    private void playOpenSound() {
        try {
            if (File.getConfig().getBoolean("effects.enabled", true)) {
                String openSound = config.getString("sounds.open", "BLOCK_CHEST_OPEN:0.5:1.0");
                SoundManager.playSoundFromConfig(viewer, openSound);
            }
        } catch (Exception e) {
            // Bỏ qua lỗi âm thanh
        }
    }

    /**
     * Phát âm thanh click từ config
     */
    private void playClickSound() {
        try {
            if (File.getConfig().getBoolean("effects.enabled", true)) {
                String clickSound = config.getString("sounds.click", "UI_BUTTON_CLICK:0.5:1.0");
                SoundManager.playSoundFromConfig(viewer, clickSound);
            }
        } catch (Exception e) {
            // Bỏ qua lỗi âm thanh
        }
    }

    /**
     * Thêm các item trang trí vào GUI - Tối ưu với MaterialCompatibility
     */
    private void addDecorativeItems() {
        try {
            String decorateSlots = config.getString("items.decorates.slot", "");
            if (decorateSlots.isEmpty()) return;

            String[] slots = decorateSlots.split(",");
            String materialName = config.getString("items.decorates.material", "GRAY_STAINED_GLASS_PANE");

            // Sử dụng MaterialCompatibility để tạo item tương thích
            ItemStack decorateItem = MaterialCompatibility.createCompatibleItemStack(materialName);

            // Xử lý data value cho 1.12.2 nếu cần
            if (MaterialCompatibility.isPre113() && materialName.contains("STAINED_GLASS_PANE")) {
                // Áp dụng data value cho glass pane màu xám
                decorateItem.setDurability((short) 7); // GRAY = 7
            }

            ItemMeta meta = decorateItem.getItemMeta();
            if (meta != null) {
                String name = config.getString("items.decorates.name", "&7 ");
                meta.setDisplayName(Chat.colorizewp(name));

                // Đọc lore từ config đúng cách
                List<String> lore = new ArrayList<>();
                if (config.contains("items.decorates.lore")) {
                    if (config.isList("items.decorates.lore")) {
                        // Nếu lore là list
                        List<String> configLore = config.getStringList("items.decorates.lore");
                        for (String line : configLore) {
                            lore.add(Chat.colorizewp(line));
                        }
                    } else {
                        // Nếu lore là string
                        String loreText = config.getString("items.decorates.lore", "&7 ");
                        if (loreText.contains(",")) {
                            for (String line : loreText.split(",")) {
                                lore.add(Chat.colorizewp(line.trim()));
                            }
                        } else {
                            lore.add(Chat.colorizewp(loreText));
                        }
                    }
                }
                meta.setLore(lore);

                // Thêm enchant và flags nếu có
                if (config.getBoolean("items.decorates.flags.ALL", true)) {
                    try {
                        for (org.bukkit.inventory.ItemFlag flag : org.bukkit.inventory.ItemFlag.values()) {
                            meta.addItemFlags(flag);
                        }
                    } catch (Exception ignored) {
                    }
                }

                decorateItem.setItemMeta(meta);
            }

            // Đặt item vào các slot
            for (String slotStr : slots) {
                try {
                    int slot = Integer.parseInt(slotStr.trim());
                    if (slot >= 0 && slot < inventory.getSize()) {
                        inventory.setItem(slot, decorateItem);
                    }
                } catch (NumberFormatException ignored) {
                }
            }
        } catch (Exception e) {
            Storage.getStorage().getLogger().warning("Error adding decorative items: " + e.getMessage());
        }
    }

    /**
     * Thêm thông tin người chơi vào GUI
     */
    private void addPlayerInfo() {
        try {
            int slot = config.getInt("items.player_info.slot", 4);

            // Tạo đầu người chơi để hiển thị
            ItemStack playerHead;
            if (MaterialCompatibility.isPre113()) {
                // Trước 1.13 sử dụng SKULL_ITEM với data = 3 (đầu người chơi)
                playerHead = new ItemStack(Material.valueOf("SKULL_ITEM"), 1, (byte) 3);
            } else {
                // Từ 1.13 trở lên sử dụng PLAYER_HEAD hoặc XMaterial
                playerHead = XMaterial.PLAYER_HEAD.parseItem();
            }

            if (playerHead != null) {
                SkullMeta meta = (SkullMeta) playerHead.getItemMeta();
                if (meta != null) {
                    // Sử dụng AdvancedCompatibility để set player head an toàn
                    AdvancedCompatibility.setPlayerHead(meta, targetName);

                    String name = config.getString("items.player_info.name", "&a{player}")
                            .replace("{player}", targetName);
                    meta.setDisplayName(Chat.colorizewp(name));

                    List<String> lore = new ArrayList<>();
                    List<String> configLore = config.getStringList("items.player_info.lore");

                    // Thêm thông tin giới hạn kho
                    int maxStorage;
                    if (isOnline) {
                        Player target = Bukkit.getPlayer(targetName);
                        maxStorage = (target != null) ? MineManager.getMaxBlock(target) : 0;
                    } else {
                        maxStorage = targetData.getMax();
                    }

                    String status = isOnline ? config.getString("status.online", "&aOnline") : config.getString("status.offline", "&cOffline");

                    for (String line : configLore) {
                        String processedLine = line
                                .replace("{player}", targetName)
                                .replace("{status}", status)
                                .replace("{max_storage}", String.valueOf(maxStorage));
                        lore.add(Chat.colorizewp(processedLine));
                    }

                    meta.setLore(lore);

                    // Thêm enchant và flags nếu có
                    if (config.getBoolean("items.player_info.flags.ALL", true)) {
                        try {
                            for (org.bukkit.inventory.ItemFlag flag : org.bukkit.inventory.ItemFlag.values()) {
                                meta.addItemFlags(flag);
                            }
                        } catch (Exception ignored) {
                        }
                    }

                    playerHead.setItemMeta(meta);
                    inventory.setItem(slot, playerHead);
                }
            }
        } catch (Exception e) {
            Storage.getStorage().getLogger().warning("Error adding player info: " + e.getMessage());
        }
    }

    /**
     * Thêm nút đóng GUI - Tối ưu với MaterialCompatibility
     */
    private void addCloseButton() {
        try {
            int slot = config.getInt("items.close_button.slot", 8);
            String materialName = config.getString("items.close_button.material", "BARRIER");

            // Sử dụng MaterialCompatibility để tạo item tương thích
            ItemStack closeButton = MaterialCompatibility.createCompatibleItemStack(materialName);

            ItemMeta meta = closeButton.getItemMeta();
            if (meta != null) {
                String name = config.getString("items.close_button.name", "&c&lĐóng");
                meta.setDisplayName(Chat.colorizewp(name));

                List<String> lore = new ArrayList<>();
                if (config.contains("items.close_button.lore")) {
                    List<String> configLore = config.getStringList("items.close_button.lore");
                    for (String line : configLore) {
                        lore.add(Chat.colorizewp(line));
                    }
                }
                meta.setLore(lore);

                // Thêm flags nếu có
                if (config.getBoolean("items.close_button.flags.ALL", true)) {
                    try {
                        for (org.bukkit.inventory.ItemFlag flag : org.bukkit.inventory.ItemFlag.values()) {
                            meta.addItemFlags(flag);
                        }
                    } catch (Exception ignored) {
                    }
                }

                closeButton.setItemMeta(meta);
                inventory.setItem(slot, closeButton);
            }
        } catch (Exception e) {
            Storage.getStorage().getLogger().warning("Error adding close button: " + e.getMessage());
        }
    }

    /**
     * Thêm các tài nguyên vào GUI
     */
    private void addResources() {
        try {
            // Lấy danh sách slot từ config
            String slotsConfig = config.getString("items.resource_area.slots", "10,11,12,13,14,15,16,19,20,21,22,23,24,25,28,29,30,31,32,33,34,37,38,39,40,41,42,43");
            String[] slotStrings = slotsConfig.split(",");
            List<Integer> availableSlots = new ArrayList<>();

            for (String slotStr : slotStrings) {
                try {
                    int slot = Integer.parseInt(slotStr.trim());
                    if (slot >= 0 && slot < inventory.getSize()) {
                        availableSlots.add(slot);
                    }
                } catch (NumberFormatException ignored) {
                }
            }

            if (availableSlots.isEmpty()) {
                Storage.getStorage().getLogger().warning("No valid slots found for resources in ViewPlayerStorageGUI");
                return;
            }

            int slotIndex = 0;
            boolean hasResources = false;

            for (String material : resources) {
                if (slotIndex >= availableSlots.size()) break; // Tránh vượt quá số slot có sẵn

                // Lấy số lượng tài nguyên
                int amount;
                if (isOnline) {
                    Player target = Bukkit.getPlayer(targetName);
                    amount = (target != null) ? MineManager.getPlayerBlock(target, material) : 0;
                } else {
                    // Phân tích dữ liệu từ chuỗi JSON trong PlayerData
                    List<String> dataList = MineManager.convertOnlineData(targetData.getData());
                    amount = 0;

                    for (String data : dataList) {
                        String[] parts = data.split(";");
                        if (parts.length >= 2 && parts[0].equals(material)) {
                            try {
                                amount = Integer.parseInt(parts[1]);
                                break;
                            } catch (NumberFormatException ignored) {
                            }
                        }
                    }
                }

                // Chỉ hiển thị tài nguyên có số lượng > 0
                if (amount > 0) {
                    hasResources = true;

                    // Tạo item hiển thị với MaterialCompatibility
                    ItemStack resourceItem;
                    try {
                        // Sử dụng MaterialCompatibility để tạo item tương thích
                        resourceItem = MaterialCompatibility.createCompatibleItemStack(material);

                        // Xử lý data value cho 1.12.2 nếu material có format "MATERIAL;DATA"
                        if (MaterialCompatibility.isPre113() && material.contains(";")) {
                            String[] parts = material.split(";");
                            if (parts.length >= 2) {
                                try {
                                    short data = Short.parseShort(parts[1]);
                                    resourceItem.setDurability(data);
                                } catch (NumberFormatException ignored) {
                                }
                            }
                        }
                    } catch (Exception e) {
                        // Fallback với XMaterial
                        resourceItem = XMaterial.STONE.parseItem();
                        if (resourceItem == null) {
                            resourceItem = new ItemStack(Material.STONE);
                        }
                    }

                    if (resourceItem != null) {
                        ItemMeta meta = resourceItem.getItemMeta();
                        if (meta != null) {
                            // Tên hiển thị lấy từ cấu hình
                            String displayName = File.getConfig().getString("items." + material, material);
                            String name = config.getString("items.resource_area.resource_template.name", "&a{display_name}")
                                    .replace("{display_name}", displayName);
                            meta.setDisplayName(Chat.colorizewp(name));

                            List<String> lore = new ArrayList<>();
                            List<String> configLore = config.getStringList("items.resource_area.resource_template.lore");
                            for (String line : configLore) {
                                String processedLine = line
                                        .replace("{display_name}", displayName)
                                        .replace("{amount}", String.valueOf(amount));
                                lore.add(Chat.colorizewp(processedLine));
                            }
                            meta.setLore(lore);

                            // Thêm enchant và flags nếu có
                            if (config.getBoolean("items.resource_area.resource_template.flags.ALL", true)) {
                                try {
                                    for (org.bukkit.inventory.ItemFlag flag : org.bukkit.inventory.ItemFlag.values()) {
                                        meta.addItemFlags(flag);
                                    }
                                } catch (Exception ignored) {
                                }
                            }

                            resourceItem.setItemMeta(meta);
                            inventory.setItem(availableSlots.get(slotIndex), resourceItem);
                            slotIndex++;
                        }
                    }
                }
            }

            // Nếu không có tài nguyên nào, hiển thị item "không có tài nguyên"
            if (!hasResources) {
                addNoResourcesItem();
            }
        } catch (Exception e) {
            Storage.getStorage().getLogger().warning("Error adding resources: " + e.getMessage());
        }
    }

    /**
     * Thêm item "không có tài nguyên" - Tối ưu với MaterialCompatibility
     */
    private void addNoResourcesItem() {
        try {
            int slot = config.getInt("items.no_resources.slot", 22);
            String materialName = config.getString("items.no_resources.material", "BARRIER");

            // Sử dụng MaterialCompatibility để tạo item tương thích
            ItemStack noResourcesItem = MaterialCompatibility.createCompatibleItemStack(materialName);

            ItemMeta meta = noResourcesItem.getItemMeta();
            if (meta != null) {
                String name = config.getString("items.no_resources.name", "&cKhông có tài nguyên");
                meta.setDisplayName(Chat.colorizewp(name));

                List<String> lore = new ArrayList<>();
                if (config.contains("items.no_resources.lore")) {
                    List<String> configLore = config.getStringList("items.no_resources.lore");
                    for (String line : configLore) {
                        lore.add(Chat.colorizewp(line));
                    }
                }
                meta.setLore(lore);

                // Thêm flags nếu có
                if (config.getBoolean("items.no_resources.flags.ALL", true)) {
                    try {
                        for (org.bukkit.inventory.ItemFlag flag : org.bukkit.inventory.ItemFlag.values()) {
                            meta.addItemFlags(flag);
                        }
                    } catch (Exception ignored) {
                    }
                }

                noResourcesItem.setItemMeta(meta);
                inventory.setItem(slot, noResourcesItem);
            }
        } catch (Exception e) {
            Storage.getStorage().getLogger().warning("Error adding no resources item: " + e.getMessage());
        }
    }

    /**
     * Xử lý khi người chơi click vào GUI
     */
    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        // Sử dụng InventoryCompatibility để lấy top inventory một cách an toàn
        Inventory topInventory = InventoryCompatibility.getTopInventorySafely(event);
        if (topInventory == null || !topInventory.equals(inventory) || event.getWhoClicked() != viewer) {
            return;
        }

        // Luôn hủy tất cả các sự kiện click để ngăn người chơi lấy vật phẩm
        event.setCancelled(true);

        // Xử lý khi click vào nút đóng
        int closeButtonSlot = config.getInt("items.close_button.slot", 8);
        if (event.getRawSlot() == closeButtonSlot) {
            ItemStack clickedItem = event.getCurrentItem();
            if (clickedItem != null) {
                // Phát âm thanh đóng từ config
                try {
                    if (File.getConfig().getBoolean("effects.enabled", true)) {
                        String closeSound = config.getString("sounds.close", "BLOCK_CHEST_CLOSE:0.5:1.0");
                        SoundManager.playSoundFromConfig(viewer, closeSound);
                    }
                } catch (Exception e) {
                    // Bỏ qua lỗi âm thanh
                }
                // Đóng inventory
                viewer.closeInventory();
            }
        }
    }

    /**
     * Xử lý khi đóng GUI
     */
    @EventHandler
    public void onInventoryClose(InventoryCloseEvent event) {
        if (event.getInventory().equals(inventory) && event.getPlayer().equals(viewer)) {
            // Hủy đăng ký listener để tránh rò rỉ bộ nhớ
            Bukkit.getScheduler().runTaskLater(Storage.getStorage(), this::unregisterListener, 1L);
        }
    }

    /**
     * Ngăn người chơi kéo vật phẩm trong GUI
     */
    @EventHandler
    public void onInventoryDrag(InventoryDragEvent event) {
        // Sử dụng InventoryCompatibility để lấy top inventory một cách an toàn
        Inventory topInventory = InventoryCompatibility.getTopInventorySafely(event);
        if (topInventory != null && topInventory.equals(inventory)) {
            event.setCancelled(true);
        }
    }
} 