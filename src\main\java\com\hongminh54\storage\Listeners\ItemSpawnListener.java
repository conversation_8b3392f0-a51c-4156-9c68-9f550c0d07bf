package com.hongminh54.storage.Listeners;

import java.util.concurrent.ConcurrentHashMap;

import org.bukkit.Location;
import org.bukkit.block.Block;
import org.bukkit.entity.Item;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.ItemSpawnEvent;
import org.bukkit.metadata.MetadataValue;

import com.hongminh54.storage.Storage;

/**
 * Listener này theo dõi sự kiện ItemSpawnEvent để hủy các vật phẩm rơi ra từ auto pickup.
 * Sử dụng tracking system thay vì metadata để tránh timing issues.
 */
public class ItemSpawnListener implements Listener {


    private static final ConcurrentHashMap<String, Long> autoPickupLocations = new ConcurrentHashMap<>();
    private static final long TRACKING_DURATION = 100; // 200ms - giảm từ 1 giây để tránh delayed drops

    /**
     * Thêm location vào tracking khi auto pickup xảy ra
     */
    public static void trackAutoPickupLocation(Player player, Location location) {
        String key = getLocationKey(location);
        autoPickupLocations.put(key, System.currentTimeMillis());


        if (Storage.getStorage().isDebug()) {
            Storage.getStorage().getLogger().info("ItemSpawnListener: Tracking auto pickup location for " + player.getName() + " at " + key);
        }
    }

    /**
     * Tạo key từ location
     */
    private static String getLocationKey(Location loc) {
        return loc.getWorld().getName() + "_" + loc.getBlockX() + "_" + loc.getBlockY() + "_" + loc.getBlockZ();
    }

    @EventHandler(priority = EventPriority.HIGHEST, ignoreCancelled = true)
    public void onItemSpawn(ItemSpawnEvent event) {
        Item item = event.getEntity();
        Location itemLoc = item.getLocation();


        cleanupOldTrackingData();


        if (shouldCancelItemSpawn(itemLoc)) {
            event.setCancelled(true);
            return;
        }


        if (hasMetadataFallback(itemLoc)) {
            event.setCancelled(true);
        }
    }

    /**
     * Kiểm tra xem có nên cancel item spawn không dựa trên tracking
     */
    private boolean shouldCancelItemSpawn(Location itemLoc) {
        long now = System.currentTimeMillis();


        for (int x = -2; x <= 2; x++) {
            for (int y = -2; y <= 2; y++) {
                for (int z = -2; z <= 2; z++) {
                    Location checkLoc = itemLoc.clone().add(x, y, z);
                    String checkKey = getLocationKey(checkLoc);
                    if (autoPickupLocations.containsKey(checkKey)) {
                        long trackTime = autoPickupLocations.get(checkKey);
                        if (now - trackTime <= TRACKING_DURATION) {
                            return true;
                        }
                    }
                }
            }
        }
        return false;
    }

    /**
     * Cleanup old tracking data
     */
    private void cleanupOldTrackingData() {
        long now = System.currentTimeMillis();
        autoPickupLocations.entrySet().removeIf(entry -> now - entry.getValue() > TRACKING_DURATION);
    }

    /**
     * Force cleanup tất cả tracking data để tránh delayed drops
     */
    public static void forceCleanupTrackingData() {
        autoPickupLocations.clear();
    }

    /**
     * Cleanup tracking data cho player cụ thể khi disconnect
     */
    public static void cleanupPlayerTrackingData(String playerName) {
        autoPickupLocations.entrySet().removeIf(entry -> entry.getKey().startsWith(playerName + "_"));
    }

    /**
     * Metadata fallback cho các trường hợp đặc biệt
     */
    private boolean hasMetadataFallback(Location itemLoc) {
        for (int x = -1; x <= 1; x++) {
            for (int y = -1; y <= 1; y++) {
                for (int z = -1; z <= 1; z++) {
                    Block nearbyBlock = itemLoc.getBlock().getRelative(x, y, z);


                    if (hasLeafEnchantDropMetadata(nearbyBlock)) {
                        return false;
                    }


                    if (hasNoDropsMetadata(nearbyBlock)) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    /**
     * Kiểm tra xem block có metadata NoDrops không
     *
     * @param block Block cần kiểm tra
     * @return true nếu block có metadata NoDrops và giá trị là true
     */
    private boolean hasNoDropsMetadata(Block block) {
        if (block.hasMetadata("NoDrops")) {
            for (MetadataValue value : block.getMetadata("NoDrops")) {
                if (value.getOwningPlugin() == Storage.getStorage() && value.asBoolean()) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * Kiểm tra xem block có metadata LeafEnchantDrop không (từ leaf collector enchant)
     *
     * @param block Block cần kiểm tra
     * @return true nếu block có metadata LeafEnchantDrop và giá trị là true
     */
    private boolean hasLeafEnchantDropMetadata(Block block) {
        if (block.hasMetadata("LeafEnchantDrop")) {
            for (MetadataValue value : block.getMetadata("LeafEnchantDrop")) {
                if (value.getOwningPlugin() == Storage.getStorage() && value.asBoolean()) {
                    return true;
                }
            }
        }
        return false;
    }
} 