# C<PERSON><PERSON> hình GUI tìm kiếm người chơi

title: "&#4A90E2Tìm kiếm người chơi"
# <PERSON><PERSON><PERSON> thước của giao diện: 1,2,3,4,5,6
size: 6

# Cấu hình âm thanh
sounds:
  # Âm thanh khi mở giao diện
  open: "BLOCK_NOTE_BLOCK_PLING:0.5:1.2"
  # Âm thanh khi đóng giao diện
  close: "BLOCK_CHEST_CLOSE:0.5:1.0"
  # Âm thanh khi click vào nút
  click: "UI_BUTTON_CLICK:0.5:1.0"
  # Âm thanh khi chọn người chơi
  select_player: "ENTITY_EXPERIENCE_ORB_PICKUP:0.5:1.2"
  # Âm thanh khi chuyển trang
  page_change: "UI_BUTTON_CLICK:0.5:1.0"
  # Âm thanh khi tìm kiếm
  search: "ENTITY_EXPERIENCE_ORB_PICKUP:0.5:1.2"
  # Âm thanh khi làm mới
  refresh: "ENTITY_EXPERIENCE_ORB_PICKUP:0.5:1.2"
  # Âm thanh khi có lỗi
  error: "ENTITY_VILLAGER_NO:0.8:1.0"
# Âm thanh khi click vào nút
click_sound: UI_BUTTON_CLICK:0.5:1.0

items:
  # Vật phẩm trang trí
  decorates:
    # Các slot viền (hàng trên, dưới, trái, phải)
    slot: 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 17, 18, 26, 27, 35, 36, 44, 45, 46, 52, 53
    # Tên hiển thị
    name: "&7 "
    # Material (tương thích 1.12.2+)
    material: GRAY_STAINED_GLASS_PANE
    # Mô tả của vật phẩm
    lore:
      - "&7 "
    # Số lượng vật phẩm
    amount: 1
    # Dành cho 1.14+ nếu máy chủ của bạn có hỗ trợ rsp
    custom-model-data: 1
    # Vật phẩm sẽ không bị phá hủy?
    unbreakable: true
    # Enchant cho vật phẩm
    enchants:
      DURABILITY: 1
    # Flag cho vật phẩm | Nếu sử dụng ALL: true -> Tất cả flag sẽ được áp dụng cho vật phẩm
    flags:
      ALL: true

  # Khu vực hiển thị danh sách người chơi (slots 10-16, 19-25, 28-34, 37-43)
  player_list_area:
    # Các slot dành cho hiển thị người chơi
    slots: 10, 11, 12, 13, 14, 15, 16, 19, 20, 21, 22, 23, 24, 25, 28, 29, 30, 31, 32, 33, 34, 37, 38, 39, 40, 41, 42, 43
    # Cấu hình cho đầu người chơi
    player_head:
      # Tên hiển thị (sẽ được thay thế bằng tên người chơi)
      name: "&a&l{player_name}"
      # Vật liệu cho đầu người chơi
      material: PLAYER_HEAD
      # Mô tả cho đầu người chơi
      lore:
        - "&7"
        - "&e&oClick vào đây để chọn người chơi này"
      amount: 1
      # Dành cho 1.14+ nếu máy chủ của bạn có hỗ trợ rsp
      custom-model-data: 1
      unbreakable: true
      # Enchant cho vật phẩm
      enchants:
        DURABILITY: 1
      flags:
        ALL: true

  # Vật phẩm hiển thị khi không tìm thấy người chơi
  no_player_found:
    # Slot hiển thị (giữa GUI)
    slot: 22
    name: "&cKhông tìm thấy người chơi nào"
    material: BARRIER
    lore:
      - "&7Không có người chơi nào phù hợp"
      - "&7với từ khóa tìm kiếm của bạn"
      - "&7 "
      - "&eThử tìm kiếm với từ khóa khác"
    amount: 1
    # Dành cho 1.14+ nếu máy chủ của bạn có hỗ trợ rsp
    custom-model-data: 1
    unbreakable: true
    # Enchant cho vật phẩm
    enchants:
      DURABILITY: 1
    flags:
      ALL: true

  # Nút trang trước
  previous_page:
    slot: 47
    name: "&c&l◀ Trang trước"
    material: ARROW
    lore:
      - "&7Nhấp để chuyển về trang trước"
      - "&7 "
      - "&eTrang hiện tại: &f{current_page}"
    amount: 1
    # Dành cho 1.14+ nếu máy chủ của bạn có hỗ trợ rsp
    custom-model-data: 1
    unbreakable: true
    # Enchant cho vật phẩm
    enchants:
      DURABILITY: 1
    flags:
      ALL: true

  # Nút trang tiếp theo
  next_page:
    slot: 51
    name: "&a&l▶ Trang tiếp theo"
    material: ARROW
    lore:
      - "&7Nhấp để chuyển đến trang tiếp theo"
      - "&7 "
      - "&eTrang hiện tại: &f{current_page}"
    amount: 1
    # Dành cho 1.14+ nếu máy chủ của bạn có hỗ trợ rsp
    custom-model-data: 1
    unbreakable: true
    # Enchant cho vật phẩm
    enchants:
      DURABILITY: 1
    flags:
      ALL: true

  # Nút tìm kiếm
  search_button:
    slot: 48
    name: "&b&l🔍 Tìm kiếm"
    material: OAK_SIGN
    lore:
      - "&7Nhấp để nhập tên người chơi"
      - "&7cần tìm kiếm"
      - "&7 "
      - "&eHiện tại: &f{search_text}"
      - "&7 "
      - "&6Mẹo:"
      - "&7• Nhập tên đầy đủ hoặc một phần"
      - "&7• Sử dụng * trước tên để tìm chính xác"
      - "&7• Để trống để hiển thị tất cả"
    amount: 1
    # Dành cho 1.14+ nếu máy chủ của bạn có hỗ trợ rsp
    custom-model-data: 1
    unbreakable: true
    # Enchant cho vật phẩm
    enchants:
      DURABILITY: 1
    flags:
      ALL: true

  # Nút làm mới
  refresh_button:
    slot: 49
    name: "&e&l⟲ Làm mới"
    material: SUNFLOWER
    lore:
      - "&7Nhấp để làm mới danh sách"
      - "&7người chơi hiện tại"
      - "&7 "
      - "&eCooldown: &f{cooldown_time}s"
    amount: 1
    # Dành cho 1.14+ nếu máy chủ của bạn có hỗ trợ rsp
    custom-model-data: 1
    unbreakable: true
    # Enchant cho vật phẩm
    enchants:
      DURABILITY: 1
    flags:
      ALL: true

  # Nút xóa tìm kiếm
  clear_search:
    slot: 50
    name: "&c&l✕ Xóa tìm kiếm"
    material: REDSTONE
    lore:
      - "&7Nhấp để xóa từ khóa tìm kiếm"
      - "&7và hiển thị tất cả người chơi"
      - "&7 "
      - "&eHiện tại: &f{search_text}"
    amount: 1
    # Dành cho 1.14+ nếu máy chủ của bạn có hỗ trợ rsp
    custom-model-data: 1
    unbreakable: true
    # Enchant cho vật phẩm
    enchants:
      DURABILITY: 1
    flags:
      ALL: true

# Cấu hình hiển thị
display:
  # Số người chơi tối đa trên một trang (tối đa 28 do giới hạn slot)
  max_players_per_page: 28
  # Thời gian cooldown giữa các lần làm mới (mili giây)
  refresh_cooldown: 1500
  # Thời gian chờ tối đa khi tìm kiếm (giây)
  search_timeout: 30
  # Hiển thị thông tin debug
  debug_info: false
