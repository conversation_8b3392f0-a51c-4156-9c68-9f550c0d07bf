package com.hongminh54.storage.CMD;

import com.hongminh54.storage.API.CMDBase;
import com.hongminh54.storage.GUI.ConvertBlockGUI;
import com.hongminh54.storage.Utils.Chat;
import com.hongminh54.storage.Utils.File;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.List;

public class ConvertBlockCMD extends CMDBase {

    public ConvertBlockCMD(String name) {
        super(name);
    }

    @Override
    public void execute(@NotNull CommandSender c, String[] args) {
        if (!(c instanceof Player)) {
            c.sendMessage(Chat.colorize("&cLệnh này chỉ có thể được sử dụng bởi người chơi."));
            return;
        }

        Player player = (Player) c;

        // Kiểm tra quyền
        if (!player.hasPermission("storage.doiblock")) {
            String noPermMessage = File.getMessage().getString("admin.no_permission");
            if (noPermMessage == null) {
                noPermMessage = "&cBạn không có quyền thực hiện lệnh này!";
            }
            player.sendMessage(Chat.colorize(noPermMessage));
            return;
        }

        try {
            // Mở giao diện đổi block
            player.openInventory(new ConvertBlockGUI(player).getInventory());
        } catch (Exception e) {
            // Xử lý ngoại lệ
            player.sendMessage(Chat.colorize("&cĐã xảy ra lỗi khi mở giao diện đổi block: " + e.getMessage()));
            e.printStackTrace();
        }
    }

    @Override
    public List<String> TabComplete(@NotNull CommandSender sender, String[] args) {
        // Không cần tab complete cho lệnh này
        return new ArrayList<>();
    }
} 