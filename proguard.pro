# Basic obfuscation settings
-dontskipnonpubliclibraryclasses
-dontskipnonpubliclibraryclassmembers
-overloadaggressively
-repackageclasses 'a'
-allowaccessmodification
-mergeinterfacesaggressively

# Java version compatibility
-target 11

# Keep main plugin class and essential methods
-keep public class com.hongminh54.storage.Storage {
    public void onEnable();
    public void onDisable();
    public void onLoad();
}

# Keep all Bukkit/Spigot event handlers
-keepclassmembers class * extends org.bukkit.event.Listener {
    @org.bukkit.event.EventHandler <methods>;
}

# Keep command executors
-keep class * implements org.bukkit.command.CommandExecutor {
    public boolean onCommand(org.bukkit.command.CommandSender, org.bukkit.command.Command, java.lang.String, java.lang.String[]);
}

# Keep tab completers
-keep class * implements org.bukkit.command.TabCompleter {
    public java.util.List onTabComplete(org.bukkit.command.CommandSender, org.bukkit.command.Command, java.lang.String, java.lang.String[]);
}

# Keep PlaceholderAPI expansion
-keep class * extends me.clip.placeholderapi.expansion.PlaceholderExpansion {
    public *;
}

# Keep API classes (if any external plugins use them)
-keep class com.hongminh54.storage.API.** {
    public *;
}

# Keep database-related classes to prevent SQL errors
-keep class com.hongminh54.storage.Database.** {
    public *;
    private *;
}

# Keep configuration classes
-keepclassmembers class * {
    @com.github.initsync.xconfig.bukkit.annotation.ConfigField *;
}

# Keep enum classes (important for compatibility)
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

# Keep serialization methods
-keepclassmembers class * implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}

# Keep reflection-based access
-keepclassmembers class * {
    @java.lang.reflect.* *;
}

# Keep native methods
-keepclasseswithmembernames class * {
    native <methods>;
}

# Keep classes with main methods
-keepclasseswithmembers public class * {
    public static void main(java.lang.String[]);
}

# Don't obfuscate external libraries
-keep class org.bukkit.** { *; }
-keep class org.spigotmc.** { *; }
-keep class net.minecraft.** { *; }
-keep class com.cryptomorin.xseries.** { *; }
-keep class de.tr7zw.changeme.nbtapi.** { *; }
-keep class me.clip.placeholderapi.** { *; }
-keep class com.sk89q.worldguard.** { *; }
-keep class org.sqlite.** { *; }
-keep class com.mysql.** { *; }

# Keep annotation classes
-keep @interface * { *; }
-keepclassmembers class * {
    @* *;
}

# Don't warn about missing classes (for multi-version compatibility)
-dontwarn org.bukkit.**
-dontwarn net.minecraft.**
-dontwarn com.sk89q.worldguard.**
-dontwarn me.clip.placeholderapi.**
-dontwarn java.lang.instrument.**
-dontwarn sun.misc.**
-dontwarn javax.**
-dontwarn kotlin.**
-dontwarn org.jetbrains.**
-dontwarn org.slf4j.**
-dontwarn ch.qos.logback.**
-dontwarn org.apache.**
-dontwarn com.google.**
-dontwarn org.json.**
-dontwarn com.zaxxer.**
-dontwarn org.yaml.**
-dontwarn com.tchristofferson.**
-dontwarn net.xconfig.**
-dontwarn org.codemc.**

# Ignore warnings about library dependencies
-ignorewarnings

# Optimization settings
-optimizations !code/simplification/arithmetic,!code/simplification/cast,!field/*,!class/merging/*
-optimizationpasses 3

# Keep line numbers for debugging (optional - remove for maximum obfuscation)
-keepattributes SourceFile,LineNumberTable

# Keep inner classes
-keepattributes InnerClasses,EnclosingMethod

# Keep generic signatures
-keepattributes Signature

# Keep runtime annotations
-keepattributes RuntimeVisibleAnnotations,RuntimeInvisibleAnnotations

# Additional rules for Spigot compatibility
-keep class * extends org.bukkit.plugin.java.JavaPlugin {
    public *;
}

# Keep GUI-related classes to prevent inventory issues
-keep class com.hongminh54.storage.GUI.** {
    public *;
}

# Keep manager classes
-keep class com.hongminh54.storage.Manager.** {
    public *;
}

# Keep utility classes that might be accessed via reflection
-keep class com.hongminh54.storage.Utils.** {
    public *;
}

# Keep NMS compatibility classes
-keep class com.hongminh54.storage.NMS.** { *; }
-keep class com.hongminh54.storage.compatibility.** { *; }

# Don't obfuscate exception classes
-keep public class * extends java.lang.Exception

# Keep WorldGuard integration
-keep class com.hongminh54.storage.WorldGuard.** { *; }

# Additional safety rules
-dontusemixedcaseclassnames
-dontpreverify
-verbose
