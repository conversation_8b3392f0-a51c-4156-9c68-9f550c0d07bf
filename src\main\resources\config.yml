config_version: 2

#Thêm world bạn muốn cho vào đây nếu không muốn plugin hoạt động trong world đó
blacklist_world:
  - defaultworld
  - exampleworld

#Nhận thông báo update
check_update: true

# Bật chế độ debug
debug: false

#Prefix plugin
prefix: ""

settings:
  #Bật/tắt kiểm tra tính toàn vẹn dữ liệu định kỳ (có thể gây lag nếu bật)
  enable_integrity_check: false
  #Số lượng tối đa mặc định cho tất cả vật phẩm
  default_max_storage: 1000000
  #Tự động nhặt mặc định khi tham gia lần đầu
  default_auto_pickup: true
  #Thời gian cache bảng xếp hạng (phút)
  leaderboard_cache_duration: 5
  #Thời gian cooldown giữa các lần cập nhật bảng xếp hạng (giây)
  leaderboard_update_cooldown: 60
  #<PERSON><PERSON> lượng cập nhật tối đa trong một lần xử lý
  leaderboard_max_updates_per_batch: 2
  #Tốc độ tối đa các hiệu ứng hạt (particles) để tránh gây lag
  max_particle_count: 15
  #Giới hạn phần trăm chuyển tài nguyên khi xác nhận
  transfer_percentage: 25
  #Giới hạn số lượng tìm kiếm người chơi trên mỗi trang
  players_per_page: 45
  #Thời gian chờ tối đa khi tìm kiếm người chơi (giây)
  search_timeout: 30
  #Giới hạn kết quả giao dịch hiển thị trong lịch sử
  max_history_display: 50
  #Số lượng giao dịch hiển thị trên mỗi trang trong giao diện lịch sử
  history_items_per_page: 45
  #Số mục hiển thị mỗi trang khi xem lịch sử trong chat
  history_items_per_page_chat: 20
  # Bật/tắt hiệu ứng âm thanh và hạt khi đào
  effects_enabled: true
  # Bật/tắt tự động nhặt tài nguyên khi đào
  auto_pickup: true
  # Ngưỡng số lượng để coi là giao dịch lớn
  large_transfer_threshold: 100
  #Cài đặt khi đập block - Tối ưu performance
  block_break:
    #Hủy drop của block khi đập
    cancel_drop: true
    #Gửi thông báo khi nhặt tài nguyên - Tắt để giảm lag
    send_messages: false

# Cài đặt cooldown để tránh lag server
cooldown:
  # Thời gian làm mới các lần đập block (ms)
  block_break: 50
  # Bật/tắt giới hạn số lần đập trong một khoảng thời gian
  limit_enabled: true
  # Số lần đập tối đa trong khoảng thời gian
  max_breaks: 60
  # Khoảng thời gian reset bộ đếm (giây)
  reset_interval: 15

# Sao lưu dữ liệu
backup:
  # Bật/tắt tính năng sao lưu tự động
  enabled: true
  # Thời gian giữa các lần sao lưu (giây)
  interval: 3600
  # Bật/tắt sao lưu cơ sở dữ liệu
  database: false
  # Số lượng bản sao lưu database giữ lại tối đa
  keep_db_backups: 5
  # Số lượng bản sao lưu khẩn cấp tối đa cho mỗi người chơi
  max_per_player: 15
  # Số ngày tối đa giữ bản sao lưu khẩn cấp trước khi tự động xóa
  max_age_days: 7
  # Bật/tắt thông báo khi tạo sao lưu
  notify_backup: false
  # Bật/tắt tạo emergency backup (backup khẩn cấp khi có lỗi)
  emergency_backups: true
  # Ngưỡng số lượng vật phẩm để tạo emergency backup
  emergency_threshold: 10000000
  # Đường dẫn thư mục backup (relative to plugin folder)
  backup_folder: "emergency_backups"
  # Định dạng tên file backup (sử dụng {player}, {timestamp})
  file_format: "{player}_{timestamp}.json"
  # Bật/tắt nén file backup
  compression: false

# Cài đặt đơn giản cho database
database:
  # Bật/tắt auto-save định kỳ
  auto_save: true
  # Khoảng thời gian auto-save (giây)
  auto_save_interval: 1200
  # Bật/tắt tự động lưu dữ liệu plugin khi dùng lệnh /save hoặc /save-all
  save_on_server_save: true
  # Bật/tắt lưu dữ liệu thống kê trong auto-save (có thể tắt nếu gặp lỗi PRAGMA)
  save_stats_on_server_save: true

  # Cài đặt SQLite
  sqlite:
    # Kích thước pool kết nối tối đa (giảm xuống để tránh xung đột)
    max_pool_size: 1
    # Timeout cho các truy vấn (giây)
    query_timeout: 30
    # Timeout khi database bận (ms)
    busy_timeout: 60000
    # Bật/tắt WAL mode (Write-Ahead Logging) cho thread safety
    wal_mode: true
    # Kích thước cache (số trang)
    cache_size: 2000
    # Số lần thử lại
    max_retries: 5
    # Thời gian chờ giữa các lần thử lại (ms)
    retry_delay: 200

  # Cài đặt batch processing để tối ưu hiệu suất SQL
  batch:
    # Bật/tắt batch processing
    enabled: true
    # Kích thước batch tối đa (số operations xử lý cùng lúc) 
    max_size: 5
    # Timeout cho batch processing (milliseconds) 
    timeout: 80000
    # Interval giữa các lần xử lý batch (milliseconds)
    process_interval: 800
    # Tự động flush khi server shutdown
    auto_flush_on_shutdown: true
    # Delay khi retry (milliseconds)
    retry_delay: 30000

# Cài đặt chuyển tài nguyên
transfer:
  # Bật/tắt ghi lịch sử chuyển tài nguyên
  log_history: true
  # Giới hạn số lượng mỗi lần chuyển (0 = không giới hạn)
  max_per_transfer: 0
  # Thời gian chờ giữa các lần chuyển (giây)
  cooldown: 5
  # Bật/tắt hiệu ứng chuyển ore
  effects_enabled: true
  # Thời gian chờ khi đang xử lý chuyển ore (giây)
  # Giúp đảm bảo tính nhất quán dữ liệu và tránh lỗi khi chuyển liên tục
  processing_delay: 2

# Lệnh bán (có thể tùy chỉnh)
sell:
  - "eco give #player# #money#"

#Định dạng số của giá trị
number_format: "#.##"

#Ngăn chặn đặt lại và phá lại khối để lấy thêm vật phẩm
prevent_rebreak: false

#Phù phép Gia Tài (Fortune) có thể áp dụng cho các khoáng sản bên dưới
whitelist_fortune:
  - COAL_ORE
  - IRON_ORE
  - GOLD_ORE
  - REDSTONE_ORE
  - LAPIS_ORE
  - DIAMOND_ORE
  - EMERALD_ORE

#Cài đặt hiệu ứng âm thanh và hạt hiệu ứng
effects:
  # Cài đặt chung cho hiệu ứng
  enabled: true

  # Khi xem lịch sử giao dịch
  history_view:
    sound: "BLOCK_NOTE_BLOCK_PLING:0.5:1.2"

  # Hiệu ứng khi thu thập tài nguyên
  collect:
    # Âm thanh khi thu thập (dạng: SOUND:VOLUME:PITCH)
    sound: "ENTITY_ITEM_PICKUP:0.2:0.8"
    # Hiệu ứng hạt khi thu thập (dạng: PARTICLE:OFFSET_X:OFFSET_Y:OFFSET_Z:SPEED:COUNT)
    particle: "VILLAGER_HAPPY:0.3:0.3:0.3:0.05:8"

  # Hiệu ứng khi chuyển tài nguyên thành công
  transfer_success:
    # Âm thanh cho người gửi
    sender_sound: "ENTITY_PLAYER_LEVELUP:0.5:1.0"
    # Âm thanh cho người nhận
    receiver_sound: "ENTITY_EXPERIENCE_ORB_PICKUP:0.5:1.0"
    # Hiệu ứng hạt cho người gửi
    sender_particle: "VILLAGER_HAPPY:0.3:0.3:0.3:0.05:10"
    # Hiệu ứng hạt cho người nhận
    receiver_particle: "COMPOSTER:0.3:0.3:0.3:0.05:10"
    # Âm thanh chung cho transfer success (fallback)
    sound: "ENTITY_PLAYER_LEVELUP:0.5:1.0"

  # Hiệu ứng khi chuyển thất bại
  transfer_fail:
    sound: "ENTITY_VILLAGER_NO:1.0:1.0"

  # Hiệu ứng cho số lượng lớn (>32)
  large_transfer:
    sender_particle: "SPELL_WITCH:0.2:0.2:0.2:0.05:10"
    receiver_particle: "TOTEM:0.5:0.5:0.5:0.1:10"
    sound: "ENTITY_PLAYER_LEVELUP:0.7:1.2"

  # Hiệu ứng khi tìm kiếm thành công
  search_success:
    sound: "ENTITY_EXPERIENCE_ORB_PICKUP:0.5:1.0"
    particle: "VILLAGER_HAPPY:0.5:0.5:0.5:0.1:10"

  # Hiệu ứng khi tìm kiếm thất bại
  search_fail:
    sound: "ENTITY_VILLAGER_NO:1.0:1.0"

  # Hiệu ứng khi mở giao diện tìm kiếm
  search_open:
    sound: "BLOCK_NOTE_BLOCK_PLING:0.5:1.2"

  # Hiệu ứng khi convert block thành công
  convert_success:
    sound: "BLOCK_ANVIL_USE:1.0:1.2"

  # Hiệu ứng khi convert block thất bại
  convert_fail:
    sound: "ENTITY_VILLAGER_NO:1.0:1.0"

#Giá trị của mỗi vật phẩm
worth:
  COBBLESTONE;0: 1
  COAL;0: 2
  COAL_BLOCK;0: 10
  IRON_INGOT;0: 9
  IRON_BLOCK;0: 81
  GOLD_INGOT;0: 10
  GOLD_BLOCK;0: 86
  REDSTONE;0: 2
  REDSTONE_BLOCK;0: 16
  LAPIS_LAZULI;0: 2
  LAPIS_BLOCK;0: 21
  DIAMOND;0: 30
  DIAMOND_BLOCK;0: 540
  EMERALD;0: 90
  EMERALD_BLOCK;0: 810

#Người chơi có thể lưu trữ danh sách các khối bên dưới đây
blocks:
  #HÃY NHỚ: ĐỪNG CẤU HÌNH 2 MATERIAL_1 GIỐNG NHAU DƯỚI ĐÂY!
  #MATERIAL_1;DATA_NUMBER:
  # drop: MATERIAL_2;DATA_NUMBER
  #Cho 1.12.2 và thấp hơn, MATERIAL;0 | 0 -> dữ liệu vật phẩm
  #Ví dụ, Nếu muốn LAPIS_LAZULI cho 1.12.2 và thấp hơn -> INK_SACK;4
  #Nếu muốn kim cương cho tất cả các phiên bản -> DIAMOND;0
  #Đừng xóa ;0 nếu không muốn bị lỗi plugin 
  COBBLESTONE;0:
    drop: COBBLESTONE;0
  STONE;0:
    drop: COBBLESTONE;0
  COAL_ORE;0:
    drop: COAL;0
  COAL_BLOCK;0:
    drop: COAL_BLOCK;0
  IRON_ORE;0:
    drop: IRON_INGOT;0
  IRON_BLOCK;0:
    drop: IRON_BLOCK;0
  GOLD_ORE;0:
    drop: GOLD_INGOT;0
  GOLD_BLOCK;0:
    drop: GOLD_BLOCK;0
  REDSTONE_ORE;0:
    drop: REDSTONE;0
  REDSTONE_BLOCK;0:
    drop: REDSTONE_BLOCK;0
  LAPIS_ORE;0:
    drop: LAPIS_LAZULI;0
  LAPIS_BLOCK;0:
    drop: LAPIS_BLOCK;0
  DIAMOND_ORE;0:
    drop: DIAMOND;0
  DIAMOND_BLOCK;0:
    drop: DIAMOND_BLOCK;0
  EMERALD_ORE;0:
    drop: EMERALD;0
  EMERALD_BLOCK;0:
    drop: EMERALD_BLOCK;0
items:
  COBBLESTONE;0: "&7Đá Cuội"
  COAL;0: "&8Than"
  COAL_BLOCK;0: "&8Khối Than"
  IRON_INGOT;0: "&fThỏi Sắt"
  IRON_BLOCK;0: "&fKhối Sắt"
  GOLD_INGOT;0: "&eThỏi Vàng"
  GOLD_BLOCK;0: "&eKhối Vàng"
  REDSTONE;0: "&cĐá Đỏ"
  REDSTONE_BLOCK;0: "&cKhối Đá Đỏ"
  LAPIS_LAZULI;0: "&1Lưu Ly"
  LAPIS_BLOCK;0: "&1Khối Lưu Ly"
  DIAMOND;0: "&bKim Cương"
  DIAMOND_BLOCK;0: "&bKhối Kim Cương"
  EMERALD;0: "&aNgọc Lục Bảo"
  EMERALD_BLOCK;0: "&aKhối Ngọc Lục Bảo"
mine:
  title:
    enable: false
    title: "&e+#amount# #item#"
    subtitle: "&b#storage#/#max#"
  actionbar:
    enable: true
    action: "&6+#amount# #item# [#storage#/#max#]"

#Số lượng tối đa log hiển thị mặc định trong lệnh /kho log
default_log_display_limit: 100
#Thời gian hiệu lực của lệnh logall (giây) - sau thời gian này sẽ trở về chế độ mặc định
logall_expiry_time: 600

