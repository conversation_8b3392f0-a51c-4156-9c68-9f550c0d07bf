package com.hongminh54.storage.CMD.commands;

import com.hongminh54.storage.GUI.MultiTransferGUI;
import com.hongminh54.storage.GUI.TransferGUI;
import com.hongminh54.storage.GUI.TransferStorageGUI;
import com.hongminh54.storage.Manager.MineManager;
import com.hongminh54.storage.Manager.SoundManager;
import com.hongminh54.storage.Storage;
import com.hongminh54.storage.Utils.Chat;
import com.hongminh54.storage.Utils.File;
import org.bukkit.Bukkit;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.bukkit.util.StringUtil;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

public class TransferCommand implements IStorageCommand {

    @Override
    public boolean execute(CommandSender sender, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(Chat.colorize("&8[&4&l✕&8] &c<PERSON><PERSON><PERSON> này chỉ có thể được sử dụng bởi người chơi!"));
            return true;
        }

        Player player = (Player) sender;

        if (args.length < 2) {
            player.sendMessage(Chat.colorize("&8[&e&l!&8] &eSử dụng: &f/kho transfer <người_chơi> [loại_tài_nguyên|multi]"));
            return true;
        }

        // Tìm người chơi nhận
        Player receiver = Bukkit.getPlayer(args[1]);
        if (receiver == null) {
            player.sendMessage(Chat.colorize("&8[&4&l✕&8] &cNgười chơi &f" + args[1] + " &ckhông trực tuyến!"));
            String failSoundConfig = File.getConfig().getString("effects.player_not_found.sound", "ENTITY_VILLAGER_NO:1.0:1.0");
            SoundManager.playSoundFromConfig(player, failSoundConfig);
            return true;
        }

        // Kiểm tra không thể chuyển cho chính mình
        if (player.equals(receiver)) {
            player.sendMessage(Chat.colorize("&8[&4&l✕&8] &cBạn không thể chuyển tài nguyên cho chính mình!"));
            String failSoundConfig = File.getConfig().getString("effects.self_transfer.sound", "ENTITY_VILLAGER_NO:1.0:1.0");
            SoundManager.playSoundFromConfig(player, failSoundConfig);
            return true;
        }

        if (args.length == 2) {
            // Mở GUI chuyên dụng cho chế độ transfer
            try {
                player.openInventory(new TransferStorageGUI(player, receiver).getInventory());

                // Tin nhắn rõ ràng và khác biệt với tin nhắn tìm kiếm player
                player.sendMessage(Chat.colorize("&8[&c&l⚡&8] &c&lCHẾ ĐỘ CHUYỂN KHOÁNG SẢN"));
                player.sendMessage(Chat.colorize("&8[&a&l✓&8] &aĐã mở giao diện chuyển khoáng sản! &fClick vào &ekhoáng sản &fmuốn chuyển cho &e" + receiver.getName()));
                player.sendMessage(Chat.colorize("&8[&e&l!&8] &eHoặc sử dụng: &f/kho transfer " + receiver.getName() + " <tên_khoáng_sản>"));
                player.sendMessage(Chat.colorize("&8[&6&l⚡&8] &6Chuyển nhiều loại: &f/kho transfer " + receiver.getName() + " multi"));

                // Phát âm thanh khi mở giao diện
                String clickSoundConfig = File.getConfig().getString("effects.gui_click.sound", "UI_BUTTON_CLICK:0.5:1.0");
                SoundManager.playSoundFromConfig(player, clickSoundConfig);
            } catch (Exception e) {
                player.sendMessage(Chat.colorize("&8[&4&l✕&8] &cLỗi khi mở giao diện chuyển tài nguyên: " + e.getMessage()));
                Storage.getStorage().getLogger().warning("Lỗi khi mở giao diện chuyển tài nguyên: " + e.getMessage());
                e.printStackTrace();
            }
            return true;
        }

        if (args.length >= 3) {
            try {
                String option = args[2].toLowerCase();

                if (option.equals("multiple") || option.equals("multi")) {
                    // Mở giao diện chuyển nhiều tài nguyên
                    try {
                        player.openInventory(new MultiTransferGUI(player, receiver).getInventory());
                        // Phát âm thanh khi mở giao diện chuyển nhiều tài nguyên
                        String clickSoundConfig = File.getConfig().getString("effects.gui_click.sound", "UI_BUTTON_CLICK:0.5:1.0");
                        SoundManager.playSoundFromConfig(player, clickSoundConfig);
                    } catch (Exception e) {
                        player.sendMessage(Chat.colorize("&8[&4&l✕&8] &cLỗi khi mở giao diện chuyển nhiều tài nguyên: " + e.getMessage()));
                        Storage.getStorage().getLogger().warning("Lỗi khi mở giao diện chuyển nhiều tài nguyên: " + e.getMessage());
                        e.printStackTrace();
                    }
                } else if (MineManager.getPluginBlocks().contains(args[2])) {
                    // Mở GUI chuyển tài nguyên cụ thể
                    try {
                        player.openInventory(new TransferGUI(player, receiver, args[2]).getInventory());
                        // Phát âm thanh khi mở giao diện chuyển tài nguyên
                        String clickSoundConfig = File.getConfig().getString("effects.gui_click.sound", "UI_BUTTON_CLICK:0.5:1.0");
                        SoundManager.playSoundFromConfig(player, clickSoundConfig);
                    } catch (Exception e) {
                        player.sendMessage(Chat.colorize("&8[&4&l✕&8] &cLỗi khi mở giao diện chuyển tài nguyên: " + e.getMessage()));
                        Storage.getStorage().getLogger().warning("Lỗi khi mở giao diện chuyển tài nguyên: " + e.getMessage());
                        e.printStackTrace();
                    }
                } else {
                    // Tài nguyên không hợp lệ - hiển thị gợi ý rõ ràng
                    player.sendMessage(Chat.colorize("&8[&4&l✕&8] &cKhoáng sản không tồn tại: &f" + args[2]));
                    player.sendMessage(Chat.colorize("&8[&e&l💡&8] &eGợi ý sử dụng:"));
                    player.sendMessage(Chat.colorize("&8  • &f/kho transfer " + receiver.getName() + " &7- Mở kho để chọn khoáng sản"));
                    player.sendMessage(Chat.colorize("&8  • &f/kho transfer " + receiver.getName() + " multi &7- Chuyển nhiều loại"));

                    // Hiển thị một số khoáng sản phổ biến
                    List<String> commonMinerals = MineManager.getPluginBlocks().stream()
                            .limit(5)
                            .collect(Collectors.toList());
                    if (!commonMinerals.isEmpty()) {
                        player.sendMessage(Chat.colorize("&8[&a&l⛏&8] &aKhoáng sản có sẵn: &f" + String.join("&7, &f", commonMinerals)));
                    }

                    // Phát âm thanh thất bại
                    String failSoundConfig = File.getConfig().getString("effects.invalid_material.sound", "ENTITY_VILLAGER_NO:1.0:1.0");
                    SoundManager.playSoundFromConfig(player, failSoundConfig);
                }
            } catch (Exception e) {
                // Xử lý lỗi nếu có vấn đề khi truy cập args[2]
                Storage.getStorage().getLogger().warning("Lỗi khi xử lý lệnh chuyenore: " + e.getMessage());
                player.sendMessage(Chat.colorize("&8[&4&l✕&8] &cĐã xảy ra lỗi khi xử lý lệnh: " + e.getMessage()));
                player.sendMessage(Chat.colorize("&8[&e&l!&8] &eSử dụng: &f/kho chuyenore <người_chơi> [loại_tài_nguyên|multi]"));

                // Phát âm thanh thất bại
                String failSoundConfig = File.getConfig().getString("effects.transfer_fail.sound", "ENTITY_VILLAGER_NO:1.0:1.0");
                SoundManager.playSoundFromConfig(player, failSoundConfig);
            }
        }

        return true;
    }

    @Override
    public List<String> getTabComplete(CommandSender sender, String[] args) {
        List<String> completions = new ArrayList<>();

        if (args.length == 2) {
            // Tab completion cho người chơi
            List<String> playerNames = new ArrayList<>();
            Bukkit.getServer().getOnlinePlayers().forEach(player -> {
                // Không hiển thị tên người gửi trong danh sách
                if (!player.getName().equals(sender.getName())) {
                    playerNames.add(player.getName());
                }
            });
            StringUtil.copyPartialMatches(args[1], playerNames, completions);
        } else if (args.length == 3) {
            // Tab completion cho tài nguyên
            List<String> options = new ArrayList<>(MineManager.getPluginBlocks());
            options.add("multi");
            options.add("multiple");
            StringUtil.copyPartialMatches(args[2], options, completions);
        }

        return completions;
    }

    @Override
    public String getCommandName() {
        return "transfer";
    }

    @Override
    public List<String> getAliases() {
        return Collections.singletonList("chuyentainguyen");
    }

    @Override
    public boolean hasPermission(CommandSender sender) {
        return sender.hasPermission("storage.transfer");
    }
}
