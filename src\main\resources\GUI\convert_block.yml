title: "&#FF6347Đổi Block &8| &#4A90E2#player#"
# <PERSON><PERSON><PERSON> thước của giao diện: 1,2,3,4,5,6
size: 6

# Cấu hình âm thanh
sounds:
  # Âm thanh khi mở giao diện
  open: "BLOCK_CHEST_OPEN:0.5:1.0"
  # Âm thanh khi đóng giao diện
  close: "BLOCK_CHEST_CLOSE:0.5:1.0"
  # Âm thanh khi click vào nút
  click: "UI_BUTTON_CLICK:0.5:1.0"
  # Âm thanh khi đổi thành công
  convert_success: "BLOCK_ANVIL_USE:0.8:1.2"
  # Âm thanh khi đổi thất bại
  convert_fail: "ENTITY_VILLAGER_NO:0.8:1.0"

items:
  # Vật phẩm trang trí
  decorates:
    # <PERSON><PERSON><PERSON> bạn muốn set 1 slot | slot: 1 | nếu bạn muốn đặt nó vào nhi<PERSON><PERSON>, h<PERSON><PERSON> l<PERSON><PERSON> nh<PERSON> thế này
    slot: 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 17, 18, 26, 27, 35, 36, 44, 45, 46, 47, 48,
      49, 50, 51, 52, 53
    # Tên hiển thị của vật phẩm
    name: "&7 "
    # Vật liệu cho 1.12.2+
    material: BLACK_STAINED_GLASS_PANE
    # Mô tả của vật phẩm
    lore:
      - "&7 "
    # Số lượng vật phẩm
    amount: 1
    # Dành cho 1.14+ nếu máy chủ của bạn có hỗ trợ rsp
    custom-model-data: 1
    # Vật phẩm sẽ không bị phá hủy?
    unbreakable: true
    # Phù phép cho vật phẩm
    enchants:
      DURABILITY: 1
    # Flag cho vật phẩm | Nếu sử dụng ALL: true -> Tất cả flag sẽ được áp dụng cho vật phẩm
    flags:
      ALL: true
  # Thiết lập vật phẩm này để hiển thị tất cả vật phẩm trong kho | Tên không thể thay đổi
  convert_item:
    # Chỉ hỗ trợ nhiều ô cho thiết lập vật phẩm này
    slot: 10, 11, 12, 13, 14, 15, 16, 19, 20, 21, 22, 23, 24, 25, 31
    # lore
    lore:
      - "&7 "
      - "&f#item_amount#/#max_storage# #type_name#"
      - "&7 "
      - "#convert_left_click#"
      - "#convert_right_click#"
    # Số lượng vật phẩm
    amount: 1
    # Dành cho 1.14+ nếu máy chủ của bạn có hỗ trợ rsp
    custom-model-data: 1
    # Vật phẩm sẽ không bị phá hủy?
    unbreakable: true
    # Enchant cho vật phẩm
    enchants:
      DURABILITY: 1
    # Cờ hiệu cho vật phẩm | Nếu bạn sử dụng ALL: true -> Tất cả cờ hiệu sẽ được áp dụng cho vật phẩm
    flags:
      ALL: true
  back:
    slot: 40
    name: "&aQuay lại"
    material: ARROW
    lore:
      - "&eClick để trở về menu kho"
    amount: 1
    # Dành cho 1.14+ nếu máy chủ của bạn có hỗ trợ rsp
    custom-model-data: 1
    unbreakable: true
    enchants:
      DURABILITY: 1
    flags:
      ALL: true
    action:
      left:
        type: command
        action: storage
      right:
        type: command
        action: storage 