package com.hongminh54.storage.Listeners;

import com.hongminh54.storage.Manager.LeaderboardManager;
import com.hongminh54.storage.Manager.MineManager;
import com.hongminh54.storage.Manager.StatsManager;
import com.hongminh54.storage.Storage;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.event.player.PlayerQuitEvent;
import org.jetbrains.annotations.NotNull;

public class JoinQuitListener implements Listener {

    @EventHandler
    public void onJoin(@NotNull PlayerJoinEvent e) {
        Player p = e.getPlayer();

        // Tải dữ liệu trong luồng bất đồng bộ để tránh lag server khi người chơi đăng nhập
        Bukkit.getScheduler().runTaskAsynchronously(Storage.getStorage(), () -> {
            // Đồng bộ hóa cache đơn giản cho người chơi
            // Đồng bộ cache đơn giản
            MineManager.loadPlayerData(p);

            // Ghi log để debug
            Storage.getStorage().getLogger().info("Đã tải dữ liệu kho và thống kê cho " + p.getName() + " (bất đồng bộ)");
        });
    }

    @EventHandler
    public void onQuit(@NotNull PlayerQuitEvent e) {
        Player p = e.getPlayer();

        // Lưu dữ liệu thống kê bất đồng bộ
        StatsManager.savePlayerStatsAsync(p);

        // Sau đó lưu dữ liệu kho bất đồng bộ
        MineManager.savePlayerDataAsync(p);

        // Xóa dữ liệu khỏi cache
        StatsManager.removeFromCache(p.getName());

        // Xóa dữ liệu từ cache bảng xếp hạng
        LeaderboardManager.removePlayerFromCache(p.getName());


        // Xóa dữ liệu chat đang chờ xử lý
        ChatListener.chat_deposit.remove(p);
        ChatListener.chat_withdraw.remove(p);
        ChatListener.chat_sell.remove(p);
        ChatListener.chat_convert_block.remove(p);
        ChatListener.chat_transfer.remove(p);

        // Ghi log để debug
        Storage.getStorage().getLogger().info("Đã lưu dữ liệu kho và thống kê cho " + p.getName() + " (bất đồng bộ)");
    }
}
