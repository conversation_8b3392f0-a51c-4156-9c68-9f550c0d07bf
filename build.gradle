buildscript {
    repositories {
        gradlePluginPortal()
        mavenCentral()
    }
    dependencies {
        classpath 'com.guardsquare:proguard-gradle:7.6.0'
    }
}

plugins {
    id 'java'
    id 'com.github.johnrengelman.shadow' version '8.1.1'
    id 'eclipse'
    id 'idea'
}

group = 'com.hongminh54'
version = '1.0.5'
description = 'Multi-purpose Virtual Resource Storage designed for SkyBlock and Survival servers, featuring many newly added features.'

def devbuild = true

java {
    sourceCompatibility = JavaVersion.VERSION_1_8
    targetCompatibility = JavaVersion.VERSION_1_8
}

repositories {
    mavenCentral()
    maven { url = 'https://hub.spigotmc.org/nexus/content/repositories/snapshots/' }
    maven { url = 'https://s01.oss.sonatype.org/content/groups/public/' }
    maven { url = 'https://jitpack.io' }
    maven { url = 'https://repo.codemc.org/repository/maven-public/' }
    maven { url = 'https://repo.extendedclip.com/content/repositories/placeholderapi/' }
    maven { url = 'https://oss.sonatype.org/content/groups/public/' }
}

dependencies {
    // Provided dependencies
    compileOnly 'org.spigotmc:spigot-api:1.21.4-R0.1-SNAPSHOT'
    compileOnly 'me.clip:placeholderapi:2.11.6'
    compileOnly files('libs/nexo-1.8.jar')

    // Compiled dependencies
    implementation 'com.github.InitSync.XConfig:bukkit:1.1.7'
    implementation 'com.github.cryptomorin:XSeries:13.1.0'
    implementation 'org.codemc.worldguardwrapper:worldguardwrapper:1.2.1-SNAPSHOT'
    implementation 'de.tr7zw:item-nbt-api:2.15.0'
    implementation 'com.tchristofferson:ConfigUpdater:2.1-SNAPSHOT'
    implementation 'com.googlecode.json-simple:json-simple:1.1.1'
}

eclipse {
    classpath {
        downloadSources = true
        downloadJavadoc = true
    }
}

idea {
    module {
        downloadJavadoc = true
        downloadSources = true
    }
}

processResources {
    filesMatching('plugin.yml') {
        expand([
                'version'    : version,
                'name'       : 'Storage',
                'description': project.description,
                'project'    : project
        ])
    }

    filteringCharset 'UTF-8'
}

shadowJar {
    if (devbuild) {
        archiveBaseName.set("Storage")
        archiveVersion.set(project.version + "-DevBuild")
        archiveClassifier.set("")
    } else {
        archiveBaseName.set("Storage")
        archiveVersion.set(project.version)
        archiveClassifier.set("")
    }


    // Relocations equivalent to Maven shade plugin
    relocate 'com.cryptomorin.xseries', 'com.hongminh54.utils'
    relocate 'de.tr7zw.changeme.nbtapi', 'com.hongminh54.nbtapi'
    // SQLite relocation removed due to native library compatibility issues
    relocate 'com.mysql', 'com.hongminh54.mysql'

    // Filters
    minimize {
        exclude(dependency('com.github.cryptomorin:XSeries:.*'))
        exclude(dependency('org.xerial:sqlite-jdbc:.*'))
        exclude(dependency('mysql:mysql-connector-java:.*'))
    }

//    exclude 'com/cryptomorin/xseries/XBiome*'
//    exclude 'com/cryptomorin/xseries/NMSExtras*'
//    exclude 'com/cryptomorin/xseries/NoteBlockMusic*'
//    exclude 'com/cryptomorin/xseries/SkullCacheListener*'
//    exclude 'com/cryptomorin/xseries/SkullUtils*'
//    exclude 'com/cryptomorin/xseries/XBlock*'
//    exclude 'com/cryptomorin/xseries/XEntity*'
//    exclude 'com/cryptomorin/xseries/XPotion*'
//    exclude 'com/cryptomorin/xseries/XSound*'
//    exclude 'com/cryptomorin/xseries/XTag*'
}

// ProGuard obfuscation task
/*task proguard(type: proguard.gradle.ProGuardTask) {
    dependsOn shadowJar

    // Input jar (from shadowJar task)
    injars shadowJar.archiveFile

    // Output obfuscated jar
    outjars "${buildDir}/libs/Storage-obfuscated.jar"

    // Library jars (compatible with multiple Java versions)
    def javaHome = System.getProperty('java.home')
    def rtJar = new File("${javaHome}/lib/rt.jar")

    // Add Java runtime libraries if they exist (Java 8)
    if (rtJar.exists()) {
        libraryjars rtJar
        def jceJar = new File("${javaHome}/lib/jce.jar")
        def jsseJar = new File("${javaHome}/lib/jsse.jar")
        if (jceJar.exists()) {
            libraryjars jceJar
        }
        if (jsseJar.exists()) {
            libraryjars jsseJar
        }
    } else {
        // For Java 9+ - use specific jmods that are commonly needed
        def jmodsDir = new File("${javaHome}/jmods")
        if (jmodsDir.exists()) {
            // Only include essential jmods to avoid version issues
            def essentialJmods = [
                'java.base.jmod',
                'java.desktop.jmod',
                'java.logging.jmod',
                'java.management.jmod',
                'java.sql.jmod'
            ]
            essentialJmods.each { jmod ->
                def jmodFile = new File(jmodsDir, jmod)
                if (jmodFile.exists()) {
                    libraryjars jmodFile
                }
            }
        }
    }

    // Add compile classpath
    libraryjars configurations.compileClasspath

    // ProGuard configuration file
    configuration 'proguard.pro'

    // Create output directory
    doFirst {
        file("${buildDir}/proguard").mkdirs()
    }

    // Print mapping for debugging
    printmapping "${buildDir}/proguard/mapping.txt"
    printseeds "${buildDir}/proguard/seeds.txt"
    printusage "${buildDir}/proguard/usage.txt"
}

// Task to copy obfuscated jar with proper name
task copyObfuscatedJar(type: Copy) {
    dependsOn proguard
    from "${buildDir}/libs/Storage-obfuscated.jar"
    into "${buildDir}/libs"
    rename { "Storage-${version}-obfuscated.jar" }
}

// Task to build only obfuscated version
task buildObfuscated {
    dependsOn proguard, copyObfuscatedJar
    group = 'build'
    description = 'Builds the obfuscated version of the plugin'
}

// Task to clean obfuscated files
task cleanObfuscated(type: Delete) {
    delete "${buildDir}/libs/Storage-obfuscated.jar"
    delete "${buildDir}/libs/Storage-${version}-obfuscated.jar"
    delete "${buildDir}/proguard"
    group = 'build'
    description = 'Cleans obfuscated files and ProGuard output'
}*/

tasks.named('build') {
    dependsOn shadowJar
//    finalizedBy proguard, copyObfuscatedJar
}

// Task to run server in TestServer directory
task runServer(type: JavaExec) {
    dependsOn shadowJar
    group = 'application'
    description = 'Runs the Minecraft server in TestServer directory'

    // Set working directory to TestServer
    workingDir = file('TestServer')

    // Run the server jar
    classpath = files('TestServer/server.jar')
    mainClass = '-jar'
    args = ['server.jar', 'nogui']

    // Enable console interaction
    standardInput = System.in
    standardOutput = System.out
    errorOutput = System.err

    // JVM arguments for server
    jvmArgs = [
            '-Xmx2G',
            '-Xms1G',
            '-XX:+UseG1GC',
            '-XX:+ParallelRefProcEnabled',
            '-XX:MaxGCPauseMillis=200',
            '-XX:+UnlockExperimentalVMOptions',
            '-XX:+DisableExplicitGC',
            '-XX:+AlwaysPreTouch',
            '-XX:G1NewSizePercent=30',
            '-XX:G1MaxNewSizePercent=40',
            '-XX:G1HeapRegionSize=8M',
            '-XX:G1ReservePercent=20',
            '-XX:G1HeapWastePercent=5',
            '-XX:G1MixedGCCountTarget=4',
            '-XX:InitiatingHeapOccupancyPercent=15',
            '-XX:G1MixedGCLiveThresholdPercent=90',
            '-XX:G1RSetUpdatingPauseTimePercent=5',
            '-XX:SurvivorRatio=32',
            '-XX:+PerfDisableSharedMem',
            '-XX:MaxTenuringThreshold=1'
    ]
}

// Task to run server with GUI
task runServerGUI(type: JavaExec) {
    dependsOn shadowJar
    group = 'application'
    description = 'Runs the Minecraft server with GUI in TestServer directory'

    // Set working directory to TestServer
    workingDir = file('TestServer')

    // Run the server jar with GUI
    classpath = files('TestServer/server.jar')
    mainClass = '-jar'
    args = ['server.jar']

    // Enable console interaction
    standardInput = System.in
    standardOutput = System.out
    errorOutput = System.err

    // JVM arguments for server
    jvmArgs = [
            '-Xmx2G',
            '-Xms1G',
            '-XX:+UseG1GC',
            '-XX:+ParallelRefProcEnabled',
            '-XX:MaxGCPauseMillis=200',
            '-XX:+UnlockExperimentalVMOptions',
            '-XX:+DisableExplicitGC',
            '-XX:+AlwaysPreTouch',
            '-XX:G1NewSizePercent=30',
            '-XX:G1MaxNewSizePercent=40',
            '-XX:G1HeapRegionSize=8M',
            '-XX:G1ReservePercent=20',
            '-XX:G1HeapWastePercent=5',
            '-XX:G1MixedGCCountTarget=4',
            '-XX:InitiatingHeapOccupancyPercent=15',
            '-XX:G1MixedGCLiveThresholdPercent=90',
            '-XX:G1RSetUpdatingPauseTimePercent=5',
            '-XX:SurvivorRatio=32',
            '-XX:+PerfDisableSharedMem',
            '-XX:MaxTenuringThreshold=1'
    ]
}