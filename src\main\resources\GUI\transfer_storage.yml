# C<PERSON>u hình <PERSON>UI chọn vật phẩm để chuyển tài nguyên

title: "&#FFD700&l<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> &8| &#32CD32{receiver}"
# K<PERSON>ch thước của giao diện: 1,2,3,4,5,6
size: 6

# Âm thanh
sounds:
  open: "BLOCK_NOTE_BLOCK_PLING:1.0:1.5"
  open_fallback: "BLOCK_CHEST_OPEN:0.5:1.0"
  error: "ENTITY_VILLAGER_NO:0.5:1.0"

items:
  # tài nguyên trang trí với theme transfer
  decorates:
    # Slots cho trang trí
    slot: 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 17, 18, 26, 27, 35, 36, 44, 46, 47, 48, 50, 51, 52
    # Tên hiển thị
    name: ""
    # Vật liệu cho trang trí (theme cam/vàng)
    material: ORANGE_STAINED_GLASS_PANE
    # Lore
    lore:
      - ""
    # Số lượng
    amount: 1
    # Dành cho 1.14+ nếu máy chủ của bạn có hỗ trợ rsp
    custom-model-data: 1
    # Vật phẩm sẽ không bị phá hủy?
    unbreakable: true
    # Enchants để tạo glow effect
    enchants:
      DURABILITY: 1
    # Flags
    flags:
      ALL: true

  # Thiết lập tài nguyên trong chế độ transfer
  storage_item:
    # Số slot
    slot: 10, 11, 12, 13, 14, 15, 16, 19, 20, 21, 22, 23, 24, 25, 31
    # Lore
    lore_available:
      - "&7"
      - "&f{current_amount}&7/&f{max_storage} &7tài nguyên"
      - "&7"
      - "&a&l➤ &aClick để chuyển cho &e{receiver}"
      - "&7"
    # Lore cho tài nguyên không có vật phẩm
    lore_empty:
      - "&7"
      - "&f0&7/&f{max_storage} &7tài nguyên"
      - "&7"
      - "&c&l✕ &cKhông có tài nguyên để chuyển"
      - "&7"
    # Tên hiển thị tài nguyên nếu tài nguyên đó đã có sẵn
    name_available: "{material_name} &7(&f{current_amount}&7)"
    # Tên hiển thị tài nguyên không có sẵn
    name_empty: "{material_name} &8(&f0&8)"
    # Số lượng
    amount: 1
    # Custom model data
    custom-model-data: 1
    # Vật phẩm sẽ không bị phá hủy?
    unbreakable: true
    # Enchants để tạo glow effect
    enchants_available:
      DURABILITY: 1
    # Flags
    flags:
      ALL: true

  # Nút chuyển nhiều loại tài nguyên
  multi_transfer_button:
    # Slot
    slot: 49
    # Tên hiển thị
    name: "&6&lChuyển Nhiều Loại Tài Nguyên"
    # Vật liệu
    material: HOPPER
    # Lore
    lore:
      - "&7"
      - "&eChuyển nhiều loại tài nguyên"
      - "&ecùng lúc cho &a{receiver}"
      - "&7"
      - "&a&l➤ &aClick để mở giao diện"
      - "&7"
    # Số lượng
    amount: 1
    # Custom model data
    custom-model-data: 1
    # Vật phẩm sẽ không bị phá hủy?
    unbreakable: true
    # Enchants để tạo glow effect
    enchants:
      DURABILITY: 1
    # Flags
    flags:
      ALL: true

  # Quay lại kho chính
  back_button:
    # Slot
    slot: 45
    # Tên hiển thị
    name: "&c&l← Quay lại"
    # Vật liệu
    material: BARRIER
    # Mô tả
    lore:
      - "&7"
      - "&7Quay lại kho cá nhân"
      - "&7"
      - "&e➤ &aClick để quay lại"
    # Số lượng
    amount: 1
    # Custom model data
    custom-model-data: 1
    # Vật phẩm sẽ không bị phá hủy?
    unbreakable: true
    # Flags
    flags:
      ALL: true

  # Thông tin người nhận
  receiver_info:
    # Slot
    slot: 53
    # Tên hiển thị
    name: "&aNgười nhận: &e&l{receiver}"
    # Vật liệu
    material: PLAYER_HEAD
    # Mô tả
    lore:
      - "&7"
      - "&7Trạng thái: {status}"
      - "&7"
      - "&7Hãy chọn bất kì tài nguyên có sẵn của bạn để thực hiện chuyển tài nguyên"
      - "&7"
    # Số lượng
    amount: 1
    # Custom model data
    custom-model-data: 1
    # Vật phẩm sẽ không bị phá hủy?
    unbreakable: true
    # Flags
    flags:
      ALL: true

# Trạng thái
status_messages:
  online: "&aOnline"
  offline: "&cOffline"

# Hiệu ứng
effects:
  # Hiệu ứng glow cho tài nguyên có sẵn
  glow_available: true
  # Hiệu ứng glow cho các nút
  glow_buttons: true
