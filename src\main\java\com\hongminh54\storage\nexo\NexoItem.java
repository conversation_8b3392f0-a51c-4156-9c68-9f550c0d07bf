package com.hongminh54.storage.nexo;

import com.hongminh54.storage.Storage;
import com.hongminh54.storage.Utils.Chat;
import com.hongminh54.storage.compatibility.AdvancedCompatibility;
import com.hongminh54.storage.compatibility.MaterialCompatibility;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

/**
 * Utility class để xử lý Nexo items trong GUI và config
 * Đơn giản hóa việc tạo items với hỗ trợ Nexo
 *
 * <AUTHOR>
 */
public class NexoItem {

    /**
     * Tạo ItemStack từ config section với hỗ trợ Nexo
     *
     * @param section ConfigurationSection chứa thông tin item
     * @return ItemStack đã được tạo
     */
    public static ItemStack createItemFromConfig(ConfigurationSection section) {
        if (section == null) {
            return AdvancedCompatibility.createItemWithCustomModel(org.bukkit.Material.PAPER, 0);
        }

        String materialName = section.getString("material", "PAPER");
        int customModelData = section.getInt("custom-model-data", 0);

        // Sử dụng NexoIntegration để tạo item
        ItemStack item = NexoIntegration.createItemWithNexoSupport(materialName, customModelData);

        // Áp dụng các thuộc tính khác từ config
        ItemMeta meta = item.getItemMeta();
        if (meta != null) {
            // Display name
            if (section.contains("name")) {
                meta.setDisplayName(Chat.colorize(section.getString("name")));
            }

            // Lore
            if (section.contains("lore")) {
                meta.setLore(Chat.colorizewp(section.getStringList("lore")));
            }

            // Amount
            if (section.contains("amount")) {
                item.setAmount(section.getInt("amount", 1));
            }

            // Unbreakable (chỉ cho phiên bản 1.13+)
            if (section.contains("unbreakable") && !MaterialCompatibility.isPre113()) {
                meta.setUnbreakable(section.getBoolean("unbreakable"));
            }

            item.setItemMeta(meta);
        }

        return item;
    }

    /**
     * Tạo ItemStack đơn giản với material name và custom model data
     *
     * @param materialName    Tên material hoặc Nexo item ID
     * @param customModelData Custom model data (fallback)
     * @param displayName     Tên hiển thị
     * @return ItemStack đã được tạo
     */
    public static ItemStack createSimpleItem(String materialName, int customModelData, String displayName) {
        ItemStack item = NexoIntegration.createItemWithNexoSupport(materialName, customModelData);

        if (displayName != null && !displayName.isEmpty()) {
            ItemMeta meta = item.getItemMeta();
            if (meta != null) {
                meta.setDisplayName(Chat.colorize(displayName));
                item.setItemMeta(meta);
            }
        }

        return item;
    }

    /**
     * Kiểm tra xem một config section có định nghĩa Nexo item không
     *
     * @param section ConfigurationSection cần kiểm tra
     * @return true nếu có thể là Nexo item
     */
    public static boolean isNexoItemConfig(ConfigurationSection section) {
        if (section == null || !NexoIntegration.isNexoAvailable()) {
            return false;
        }

        String materialName = section.getString("material");
        if (materialName == null || materialName.isEmpty()) {
            return false;
        }

        // Thử tạo Nexo item để kiểm tra
        ItemStack nexoItem = NexoIntegration.createNexoItem(materialName);
        return nexoItem != null;
    }

    /**
     * Lấy thông tin về item (Nexo ID hoặc material name)
     *
     * @param itemStack ItemStack cần kiểm tra
     * @return Thông tin về item
     */
    public static String getItemInfo(ItemStack itemStack) {
        if (itemStack == null) {
            return "null";
        }

        // Kiểm tra xem có phải Nexo item không
        if (NexoIntegration.isNexoAvailable()) {
            String nexoId = NexoIntegration.getNexoItemId(itemStack);
            if (nexoId != null && !nexoId.isEmpty()) {
                return "Nexo:" + nexoId;
            }
        }

        // Fallback về material name và custom model data
        String materialName = itemStack.getType().name();
        int customModelData = AdvancedCompatibility.getCustomModelData(itemStack.getItemMeta());

        if (customModelData > 0) {
            return materialName + " (CMD:" + customModelData + ")";
        } else {
            return materialName;
        }
    }

    /**
     * Áp dụng custom model data an toàn cho ItemMeta
     * Tự động bỏ qua nếu là Nexo item
     *
     * @param meta            ItemMeta cần áp dụng
     * @param itemStack       ItemStack gốc để kiểm tra
     * @param customModelData Custom model data value
     */
    public static void applyCustomModelDataSafely(ItemMeta meta, ItemStack itemStack, int customModelData) {
        if (meta == null || customModelData <= 0) {
            return;
        }

        // Không áp dụng custom model data cho Nexo items
        if (NexoIntegration.isNexoItem(itemStack)) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().info("Bỏ qua custom model data cho Nexo item: " + NexoIntegration.getNexoItemId(itemStack));
            }
            return;
        }

        // Áp dụng custom model data thông thường
        AdvancedCompatibility.setCustomModelData(meta, customModelData);
    }

    /**
     * Tạo ItemStack với fallback thông minh
     *
     * @param primaryMaterial  Material chính hoặc Nexo ID
     * @param fallbackMaterial Material dự phòng
     * @param customModelData  Custom model data
     * @param displayName      Tên hiển thị
     * @return ItemStack đã được tạo
     */
    public static ItemStack createItemWithFallback(String primaryMaterial, String fallbackMaterial, int customModelData, String displayName) {
        ItemStack item = null;

        // Thử tạo với material chính
        if (primaryMaterial != null && !primaryMaterial.isEmpty()) {
            item = NexoIntegration.createItemWithNexoSupport(primaryMaterial, customModelData);
        }

        // Nếu không thành công, thử với fallback
        if (item == null && fallbackMaterial != null && !fallbackMaterial.isEmpty()) {
            item = NexoIntegration.createItemWithNexoSupport(fallbackMaterial, customModelData);
        }

        // Nếu vẫn không thành công, tạo item mặc định
        if (item == null) {
            item = AdvancedCompatibility.createItemWithCustomModel(org.bukkit.Material.PAPER, customModelData);
        }

        // Áp dụng display name
        if (displayName != null && !displayName.isEmpty()) {
            ItemMeta meta = item.getItemMeta();
            if (meta != null) {
                meta.setDisplayName(Chat.colorize(displayName));
                item.setItemMeta(meta);
            }
        }

        return item;
    }
}
