<?xml version="1.0" encoding="UTF-8"?>
<projectDescription>
	<name>Storage-Storage-V2</name>
	<comment>Multi-purpose Virtual Resource Storage designed for SkyBlock and Survival servers, featuring many newly added features with ProGuard obfuscation support.</comment>
	<projects>
	</projects>
	<buildSpec>
		<buildCommand>
			<name>org.eclipse.jdt.core.javabuilder</name>
			<arguments>
			</arguments>
		</buildCommand>
		<buildCommand>
			<name>org.eclipse.buildship.core.gradleprojectbuilder</name>
			<arguments>
			</arguments>
		</buildCommand>
	</buildSpec>
	<natures>
		<nature>org.eclipse.jdt.core.javanature</nature>
		<nature>org.eclipse.buildship.core.gradleprojectnature</nature>
	</natures>
	<filteredResources>
		<filter>
			<id>1747734362954</id>
			<name></name>
			<type>30</type>
			<matcher>
				<id>org.eclipse.core.resources.regexFilterMatcher</id>
				<arguments>node_modules|\.git|__CREATED_BY_JAVA_LANGUAGE_SERVER__|build|\.gradle|proguard\.pro</arguments>
			</matcher>
		</filter>
		<filter>
			<id>1747734362955</id>
			<name>build</name>
			<type>10</type>
			<matcher>
				<id>org.eclipse.core.resources.regexFilterMatcher</id>
				<arguments>.*</arguments>
			</matcher>
		</filter>
	</filteredResources>
</projectDescription>
