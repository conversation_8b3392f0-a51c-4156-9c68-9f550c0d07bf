package com.hongminh54.storage.compatibility;

import com.hongminh54.storage.NMS.NMSAssistant;
import com.hongminh54.storage.Storage;
import org.bukkit.Location;
import org.bukkit.World;
import org.bukkit.entity.Entity;
import org.bukkit.entity.EntityType;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;

import java.util.Collection;

/**
 * Lớp hỗ trợ tương thích Entity API cho Minecraft 1.12.2 - 1.21.x
 * Xử lý entity spawning, EntityType changes, và entity manipulation
 */
public class EntityCompatibility {

    private static final NMSAssistant nmsAssistant = new NMSAssistant();
    private static final boolean IS_PRE_113 = nmsAssistant.isVersionLessThan(13);
    private static final boolean IS_PRE_116 = nmsAssistant.isVersionLessThan(16);
    private static final boolean IS_1_20_OR_HIGHER = nmsAssistant.isVersionGreaterThanOrEqualTo(20);

    /**
     * Spawn entity một cách an toàn với tương thích đa phiên bản
     *
     * @param location   Vị trí spawn
     * @param entityType Loại entity
     * @return Entity đã spawn hoặc null nếu có lỗi
     */
    public static Entity spawnEntitySafely(Location location, EntityType entityType) {
        if (location == null || location.getWorld() == null || entityType == null) {
            return null;
        }

        try {
            return location.getWorld().spawnEntity(location, entityType);
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể spawn entity " + entityType + ": " + e.getMessage());
            }
            return null;
        }
    }

    /**
     * Lấy EntityType tương thích đa phiên bản
     *
     * @param modernName Tên entity phiên bản mới (1.13+)
     * @param legacyName Tên entity phiên bản cũ (1.12.2)
     * @return EntityType tương thích
     */
    public static EntityType getCompatibleEntityType(String modernName, String legacyName) {
        try {
            if (IS_PRE_113 && legacyName != null) {
                return EntityType.valueOf(legacyName);
            } else {
                return EntityType.valueOf(modernName);
            }
        } catch (IllegalArgumentException e) {
            // Thử với tên khác nếu không tìm thấy
            try {
                return EntityType.valueOf(IS_PRE_113 ? modernName : legacyName);
            } catch (IllegalArgumentException ex) {
                if (Storage.getStorage().isDebug()) {
                    Storage.getStorage().getLogger().warning("Không tìm thấy EntityType: " + modernName + "/" + legacyName);
                }
                return null;
            }
        }
    }

    /**
     * Kiểm tra xem entity có phải là LivingEntity không
     *
     * @param entity Entity cần kiểm tra
     * @return true nếu là LivingEntity
     */
    public static boolean isLivingEntity(Entity entity) {
        return entity instanceof LivingEntity;
    }

    /**
     * Lấy tất cả entities trong khu vực
     *
     * @param world   World
     * @param centerX Tọa độ X trung tâm
     * @param centerY Tọa độ Y trung tâm
     * @param centerZ Tọa độ Z trung tâm
     * @param radius  Bán kính
     * @return Collection entities trong khu vực
     */
    public static Collection<Entity> getEntitiesInRadius(World world, double centerX, double centerY, double centerZ, double radius) {
        if (world == null) {
            return java.util.Collections.emptyList();
        }

        try {
            Location center = new Location(world, centerX, centerY, centerZ);
            return world.getNearbyEntities(center, radius, radius, radius);
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể lấy entities trong khu vực: " + e.getMessage());
            }
            return java.util.Collections.emptyList();
        }
    }

    /**
     * Lấy tất cả players trong khu vực
     *
     * @param world   World
     * @param centerX Tọa độ X trung tâm
     * @param centerY Tọa độ Y trung tâm
     * @param centerZ Tọa độ Z trung tâm
     * @param radius  Bán kính
     * @return Collection players trong khu vực
     */
    public static Collection<Player> getPlayersInRadius(World world, double centerX, double centerY, double centerZ, double radius) {
        java.util.List<Player> players = new java.util.ArrayList<>();

        try {
            Collection<Entity> entities = getEntitiesInRadius(world, centerX, centerY, centerZ, radius);
            for (Entity entity : entities) {
                if (entity instanceof Player) {
                    players.add((Player) entity);
                }
            }
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể lấy players trong khu vực: " + e.getMessage());
            }
        }

        return players;
    }

    /**
     * Remove entity một cách an toàn
     *
     * @param entity Entity cần remove
     * @return true nếu remove thành công
     */
    public static boolean removeEntitySafely(Entity entity) {
        if (entity == null) {
            return false;
        }

        try {
            entity.remove();
            return true;
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể remove entity: " + e.getMessage());
            }
            return false;
        }
    }

    /**
     * Teleport entity một cách an toàn
     *
     * @param entity   Entity cần teleport
     * @param location Vị trí đích
     * @return true nếu teleport thành công
     */
    public static boolean teleportEntitySafely(Entity entity, Location location) {
        if (entity == null || location == null) {
            return false;
        }

        try {
            return entity.teleport(location);
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể teleport entity: " + e.getMessage());
            }
            return false;
        }
    }

    /**
     * Lấy max health của entity
     *
     * @param entity Entity
     * @return Max health hoặc -1 nếu có lỗi
     */
    public static double getEntityMaxHealth(Entity entity) {
        if (!isLivingEntity(entity)) {
            return -1;
        }

        try {
            LivingEntity livingEntity = (LivingEntity) entity;
            return livingEntity.getMaxHealth();
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể lấy max health của entity: " + e.getMessage());
            }
            return -1;
        }
    }

    /**
     * Lấy health hiện tại của entity
     *
     * @param entity Entity
     * @return Health hiện tại hoặc -1 nếu có lỗi
     */
    public static double getEntityHealth(Entity entity) {
        if (!isLivingEntity(entity)) {
            return -1;
        }

        try {
            LivingEntity livingEntity = (LivingEntity) entity;
            return livingEntity.getHealth();
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể lấy health của entity: " + e.getMessage());
            }
            return -1;
        }
    }

    /**
     * Set health cho entity một cách an toàn
     *
     * @param entity Entity
     * @param health Health mới
     * @return true nếu set thành công
     */
    public static boolean setEntityHealth(Entity entity, double health) {
        if (!isLivingEntity(entity)) {
            return false;
        }

        try {
            LivingEntity livingEntity = (LivingEntity) entity;
            double maxHealth = livingEntity.getMaxHealth();
            livingEntity.setHealth(Math.min(health, maxHealth));
            return true;
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể set health cho entity: " + e.getMessage());
            }
            return false;
        }
    }

    /**
     * Heal entity một cách an toàn
     *
     * @param entity Entity
     * @param amount Số lượng heal
     * @return true nếu heal thành công
     */
    public static boolean healEntity(Entity entity, double amount) {
        if (!isLivingEntity(entity) || amount <= 0) {
            return false;
        }

        try {
            LivingEntity livingEntity = (LivingEntity) entity;
            double currentHealth = livingEntity.getHealth();
            double maxHealth = livingEntity.getMaxHealth();
            double newHealth = Math.min(currentHealth + amount, maxHealth);
            livingEntity.setHealth(newHealth);
            return true;
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể heal entity: " + e.getMessage());
            }
            return false;
        }
    }

    /**
     * Damage entity một cách an toàn
     *
     * @param entity Entity
     * @param damage Số lượng damage
     * @return true nếu damage thành công
     */
    public static boolean damageEntity(Entity entity, double damage) {
        if (!isLivingEntity(entity) || damage <= 0) {
            return false;
        }

        try {
            LivingEntity livingEntity = (LivingEntity) entity;
            livingEntity.damage(damage);
            return true;
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể damage entity: " + e.getMessage());
            }
            return false;
        }
    }

    /**
     * Kiểm tra xem entity có đang sống không
     *
     * @param entity Entity
     * @return true nếu entity đang sống
     */
    public static boolean isEntityAlive(Entity entity) {
        if (!isLivingEntity(entity)) {
            return entity != null && entity.isValid();
        }

        try {
            LivingEntity livingEntity = (LivingEntity) entity;
            return !livingEntity.isDead() && livingEntity.getHealth() > 0;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * Set entity name một cách an toàn
     *
     * @param entity Entity
     * @param name   Tên mới
     * @return true nếu set thành công
     */
    public static boolean setEntityName(Entity entity, String name) {
        if (entity == null) {
            return false;
        }

        try {
            entity.setCustomName(name);
            entity.setCustomNameVisible(name != null && !name.isEmpty());
            return true;
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể set name cho entity: " + e.getMessage());
            }
            return false;
        }
    }

    /**
     * Lấy entity name
     *
     * @param entity Entity
     * @return Tên entity hoặc null
     */
    public static String getEntityName(Entity entity) {
        if (entity == null) {
            return null;
        }

        try {
            return entity.getCustomName();
        } catch (Exception e) {
            return entity.getType().name();
        }
    }
}
