package com.hongminh54.storage.compatibility;

import com.hongminh54.storage.NMS.NMSAssistant;
import com.hongminh54.storage.Storage;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.inventory.meta.SkullMeta;

import java.util.UUID;

/**
 * Lớp hỗ trợ tương thích cho các tính năng nâng cao Minecraft 1.12.2 - 1.21.x
 * Xử lý custom model data, player heads, NBT, và các tính năng khác
 */
public class AdvancedCompatibility {

    private static final NMSAssistant nmsAssistant = new NMSAssistant();
    private static final boolean IS_PRE_113 = nmsAssistant.isVersionLessThan(13);
    private static final boolean IS_PRE_114 = nmsAssistant.isVersionLessThan(14);
    private static final boolean IS_1_20_5_OR_HIGHER = nmsAssistant.is1_20_5OrHigher();
    private static final boolean IS_1_21_4_OR_HIGHER = nmsAssistant.is1_21_4OrHigher();

    /**
     * Áp dụng custom model data một cách an toàn
     *
     * @param meta            ItemMeta cần áp dụng
     * @param customModelData Custom model data value
     */
    public static void setCustomModelData(ItemMeta meta, int customModelData) {
        if (meta == null || customModelData <= 0) {
            return;
        }

        try {
            if (IS_PRE_114) {
                // Phiên bản 1.12.2-1.13.x không hỗ trợ custom model data
                return;
            }

            // Phiên bản 1.14+ hỗ trợ custom model data
            meta.setCustomModelData(customModelData);
        } catch (NoSuchMethodError e) {
            // Method không tồn tại trong phiên bản này
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("CustomModelData không được hỗ trợ trong phiên bản này");
            }
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể set custom model data: " + e.getMessage());
            }
        }
    }

    /**
     * Lấy custom model data một cách an toàn
     *
     * @param meta ItemMeta cần lấy
     * @return Custom model data hoặc 0 nếu không có/không hỗ trợ
     */
    public static int getCustomModelData(ItemMeta meta) {
        if (meta == null || IS_PRE_114) {
            return 0;
        }

        try {
            return meta.hasCustomModelData() ? meta.getCustomModelData() : 0;
        } catch (NoSuchMethodError e) {
            return 0;
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể lấy custom model data: " + e.getMessage());
            }
            return 0;
        }
    }

    /**
     * Set player head skin một cách an toàn
     *
     * @param skullMeta SkullMeta cần set
     * @param player    Player để lấy skin
     */
    public static void setPlayerHead(SkullMeta skullMeta, Player player) {
        if (skullMeta == null || player == null) {
            return;
        }

        try {
            if (IS_PRE_113) {
                // Phiên bản 1.12.2: sử dụng setOwner
                skullMeta.setOwner(player.getName());
            } else {
                // Phiên bản 1.13+: sử dụng setOwningPlayer
                skullMeta.setOwningPlayer(player);
            }
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể set player head: " + e.getMessage());
            }
        }
    }

    /**
     * Set player head skin bằng tên player
     *
     * @param skullMeta  SkullMeta cần set
     * @param playerName Tên player
     */
    public static void setPlayerHead(SkullMeta skullMeta, String playerName) {
        if (skullMeta == null || playerName == null || playerName.trim().isEmpty()) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("SkullMeta hoặc playerName null/empty trong setPlayerHead");
            }
            return;
        }

        try {
            // Làm sạch tên player
            String cleanPlayerName = playerName.trim();

            if (IS_PRE_113) {
                // Phiên bản 1.12.2: sử dụng setOwner
                skullMeta.setOwner(cleanPlayerName);
            } else {
                // Phiên bản 1.13+: sử dụng setOwningPlayer với xử lý an toàn
                try {
                    org.bukkit.OfflinePlayer offlinePlayer = Bukkit.getOfflinePlayer(cleanPlayerName);
                    if (offlinePlayer != null) {
                        skullMeta.setOwningPlayer(offlinePlayer);
                    } else {
                        Storage.getStorage().getLogger().warning("Bukkit.getOfflinePlayer trả về null cho: " + cleanPlayerName);
                    }
                } catch (Exception offlinePlayerException) {
                    Storage.getStorage().getLogger().warning("Lỗi khi lấy OfflinePlayer cho " + cleanPlayerName + ": " + offlinePlayerException.getMessage());
                    // Fallback về setOwner cho 1.13+ nếu setOwningPlayer thất bại
                    try {
                        skullMeta.setOwner(cleanPlayerName);
                    } catch (Exception ownerException) {
                        Storage.getStorage().getLogger().warning("Fallback setOwner cũng thất bại: " + ownerException.getMessage());
                    }
                }
            }
        } catch (Exception e) {
            Storage.getStorage().getLogger().warning("Không thể set player head by name cho " + playerName + ": " + e.getClass().getSimpleName() + " - " + e.getMessage());
            if (Storage.getStorage().isDebug()) {
                e.printStackTrace();
            }
        }
    }

    /**
     * Set player head skin bằng UUID một cách an toàn
     *
     * @param skullMeta SkullMeta cần set
     * @param uuid      UUID của player
     */
    public static void setPlayerHead(SkullMeta skullMeta, UUID uuid) {
        if (skullMeta == null || uuid == null) {
            return;
        }

        try {
            if (IS_PRE_113) {
                // Phiên bản 1.12.2: lấy tên từ UUID
                try {
                    String playerName = Bukkit.getOfflinePlayer(uuid).getName();
                    if (playerName != null) {
                        skullMeta.setOwner(playerName);
                    }
                } catch (Exception e) {
                    // Bỏ qua lỗi
                }
            } else {
                // Phiên bản 1.13+: sử dụng setOwningPlayer
                skullMeta.setOwningPlayer(Bukkit.getOfflinePlayer(uuid));
            }
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể set player head by UUID: " + e.getMessage());
            }
        }
    }

    /**
     * Set custom skull texture bằng base64
     *
     * @param skullMeta    SkullMeta cần set
     * @param textureValue Base64 texture value
     */
    public static void setCustomSkullTexture(SkullMeta skullMeta, String textureValue) {
        if (skullMeta == null || textureValue == null || textureValue.isEmpty()) {
            return;
        }

        try {
            // Sử dụng reflection để set texture
            setSkullTextureReflection(skullMeta, textureValue);
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể set custom skull texture: " + e.getMessage());
            }
        }
    }

    /**
     * Kiểm tra xem có hỗ trợ custom model data không
     *
     * @return true nếu hỗ trợ (1.14+)
     */
    public static boolean supportsCustomModelData() {
        return !IS_PRE_114;
    }

    /**
     * Kiểm tra xem có hỗ trợ persistent data container không
     *
     * @return true nếu hỗ trợ (1.14+)
     */
    public static boolean supportsPersistentDataContainer() {
        return !IS_PRE_114;
    }

    /**
     * Set persistent data cho ItemMeta (1.14+)
     *
     * @param meta  ItemMeta
     * @param key   Key
     * @param value Value
     */
    public static void setPersistentData(ItemMeta meta, String key, String value) {
        if (meta == null || key == null || value == null || IS_PRE_114) {
            return;
        }

        try {
            // Sử dụng reflection để tránh lỗi trong phiên bản cũ
            setPersistentDataReflection(meta, key, value);
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể set persistent data: " + e.getMessage());
            }
        }
    }

    /**
     * Lấy persistent data từ ItemMeta (1.14+)
     *
     * @param meta ItemMeta
     * @param key  Key
     * @return Value hoặc null nếu không có
     */
    public static String getPersistentData(ItemMeta meta, String key) {
        if (meta == null || key == null || IS_PRE_114) {
            return null;
        }

        try {
            // Sử dụng reflection để tránh lỗi trong phiên bản cũ
            return getPersistentDataReflection(meta, key);
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể lấy persistent data: " + e.getMessage());
            }
            return null;
        }
    }

    /**
     * Kiểm tra ItemStack có persistent data không
     *
     * @param item ItemStack
     * @param key  Key
     * @return true nếu có data
     */
    public static boolean hasPersistentData(ItemStack item, String key) {
        if (item == null || key == null || !item.hasItemMeta()) {
            return false;
        }

        return getPersistentData(item.getItemMeta(), key) != null;
    }

    /**
     * Tạo ItemStack với custom model data
     *
     * @param material        Material
     * @param customModelData Custom model data
     * @return ItemStack với custom model data
     */
    public static ItemStack createItemWithCustomModel(org.bukkit.Material material, int customModelData) {
        ItemStack item = new ItemStack(material);

        if (customModelData > 0 && supportsCustomModelData()) {
            ItemMeta meta = item.getItemMeta();
            if (meta != null) {
                setCustomModelData(meta, customModelData);
                item.setItemMeta(meta);
            }
        }

        return item;
    }

    /**
     * Set skull texture sử dụng reflection
     */
    private static void setSkullTextureReflection(SkullMeta skullMeta, String textureValue) {
        try {
            // Tạo GameProfile với random UUID
            Class<?> gameProfileClass = Class.forName("com.mojang.authlib.GameProfile");
            Object gameProfile = gameProfileClass.getConstructor(UUID.class, String.class)
                    .newInstance(UUID.randomUUID(), "CustomHead");

            // Tạo Property với texture
            Class<?> propertyClass = Class.forName("com.mojang.authlib.properties.Property");
            Object property = propertyClass.getConstructor(String.class, String.class)
                    .newInstance("textures", textureValue);

            // Thêm property vào profile
            Object properties = gameProfile.getClass().getMethod("getProperties").invoke(gameProfile);
            properties.getClass().getMethod("put", Object.class, Object.class)
                    .invoke(properties, "textures", property);

            // Set profile cho skull meta
            java.lang.reflect.Field profileField = skullMeta.getClass().getDeclaredField("profile");
            profileField.setAccessible(true);
            profileField.set(skullMeta, gameProfile);
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Reflection skull texture thất bại: " + e.getMessage());
            }
        }
    }

    /**
     * Set persistent data sử dụng reflection
     */
    private static void setPersistentDataReflection(ItemMeta meta, String key, String value) {
        try {
            // Lấy PersistentDataContainer
            Object container = meta.getClass().getMethod("getPersistentDataContainer").invoke(meta);

            // Tạo NamespacedKey
            Class<?> namespacedKeyClass = Class.forName("org.bukkit.NamespacedKey");
            java.lang.reflect.Constructor<?> namespacedKeyConstructor = namespacedKeyClass.getConstructor(org.bukkit.plugin.Plugin.class, String.class);
            Object namespacedKey = namespacedKeyConstructor.newInstance(Storage.getStorage(), key);

            // Lấy PersistentDataType.STRING
            Class<?> persistentDataTypeClass = Class.forName("org.bukkit.persistence.PersistentDataType");
            Object stringType = persistentDataTypeClass.getField("STRING").get(null);

            // Set data
            container.getClass().getMethod("set", namespacedKeyClass, persistentDataTypeClass, Object.class)
                    .invoke(container, namespacedKey, stringType, value);
        } catch (Exception e) {
            throw new RuntimeException("Failed to set persistent data", e);
        }
    }

    /**
     * Lấy persistent data sử dụng reflection
     */
    private static String getPersistentDataReflection(ItemMeta meta, String key) {
        try {
            // Lấy PersistentDataContainer
            Object container = meta.getClass().getMethod("getPersistentDataContainer").invoke(meta);

            // Tạo NamespacedKey
            Class<?> namespacedKeyClass = Class.forName("org.bukkit.NamespacedKey");
            java.lang.reflect.Constructor<?> namespacedKeyConstructor2 = namespacedKeyClass.getConstructor(org.bukkit.plugin.Plugin.class, String.class);
            Object namespacedKey = namespacedKeyConstructor2.newInstance(Storage.getStorage(), key);

            // Lấy PersistentDataType.STRING
            Class<?> persistentDataTypeClass = Class.forName("org.bukkit.persistence.PersistentDataType");
            Object stringType = persistentDataTypeClass.getField("STRING").get(null);

            // Get data
            return (String) container.getClass().getMethod("get", namespacedKeyClass, persistentDataTypeClass)
                    .invoke(container, namespacedKey, stringType);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * Tạo player head ItemStack
     *
     * @param playerName Tên player
     * @return ItemStack player head
     */
    public static ItemStack createPlayerHead(String playerName) {
        // Kiểm tra tham số đầu vào
        if (playerName == null || playerName.trim().isEmpty()) {
            Storage.getStorage().getLogger().warning("PlayerName null hoặc empty trong createPlayerHead");
            return createFallbackPlayerHead("Unknown");
        }

        try {
            ItemStack head;

            // Tạo skull item tương thích đa phiên bản
            try {
                if (IS_PRE_113) {
                    head = new ItemStack(org.bukkit.Material.valueOf("SKULL_ITEM"), 1, (short) 3);
                } else {
                    head = new ItemStack(org.bukkit.Material.valueOf("PLAYER_HEAD"));
                }
            } catch (Exception materialException) {
                Storage.getStorage().getLogger().warning("Lỗi khi tạo skull material: " + materialException.getMessage());
                return createFallbackPlayerHead(playerName);
            }

            // Kiểm tra và set skull meta
            if (head.getItemMeta() instanceof org.bukkit.inventory.meta.SkullMeta) {
                org.bukkit.inventory.meta.SkullMeta meta = (org.bukkit.inventory.meta.SkullMeta) head.getItemMeta();
                if (meta != null) {
                    setPlayerHead(meta, playerName);
                    head.setItemMeta(meta);
                } else {
                    Storage.getStorage().getLogger().warning("SkullMeta null cho player head: " + playerName);
                    return createFallbackPlayerHead(playerName);
                }
            } else {
                Storage.getStorage().getLogger().warning("ItemMeta không phải SkullMeta cho: " + playerName);
                return createFallbackPlayerHead(playerName);
            }

            return head;
        } catch (Exception e) {
            Storage.getStorage().getLogger().warning("Không thể tạo player head cho " + playerName + ": " + e.getClass().getSimpleName() + " - " + e.getMessage());
            if (Storage.getStorage().isDebug()) {
                e.printStackTrace();
            }
            return createFallbackPlayerHead(playerName);
        }
    }

    /**
     * Tạo fallback player head khi không thể tạo player head thông thường
     *
     * @param playerName Tên player
     * @return ItemStack fallback
     */
    private static ItemStack createFallbackPlayerHead(String playerName) {
        try {
            ItemStack fallback = new ItemStack(org.bukkit.Material.PAPER);
            org.bukkit.inventory.meta.ItemMeta meta = fallback.getItemMeta();
            if (meta != null) {
                String safeName = (playerName != null && !playerName.trim().isEmpty()) ? playerName.trim() : "Unknown";
                meta.setDisplayName("§e" + safeName);
                java.util.List<String> lore = new java.util.ArrayList<>();
                lore.add("§7Player: §f" + safeName);
                lore.add("§c(Không thể tải player head)");
                meta.setLore(lore);
                fallback.setItemMeta(meta);
            }
            return fallback;
        } catch (Exception e) {
            Storage.getStorage().getLogger().severe("Lỗi nghiêm trọng khi tạo fallback player head: " + e.getMessage());
            // Fallback cuối cùng
            return new ItemStack(org.bukkit.Material.PAPER);
        }
    }

    /**
     * Tạo ItemStack với hỗ trợ Nexo và custom model data
     *
     * @param materialName    Tên material hoặc Nexo item ID
     * @param customModelData Custom model data (fallback nếu không phải Nexo)
     * @return ItemStack đã được tạo
     */
    public static ItemStack createItemWithNexoSupport(String materialName, int customModelData) {
        try {
            // Import NexoIntegration
            Class<?> nexoIntegrationClass = Class.forName("com.hongminh54.storage.nexo.NexoIntegration");
            java.lang.reflect.Method createItemMethod = nexoIntegrationClass.getMethod("createItemWithNexoSupport", String.class, int.class);

            Object result = createItemMethod.invoke(null, materialName, customModelData);
            if (result instanceof ItemStack) {
                return (ItemStack) result;
            }
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể sử dụng Nexo integration: " + e.getMessage());
            }
        }

        // Fallback về cách tạo item thông thường
        return createItemWithCustomModel(org.bukkit.Material.PAPER, customModelData);
    }
}
