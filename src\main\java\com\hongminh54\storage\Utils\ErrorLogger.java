package com.hongminh54.storage.Utils;

import com.hongminh54.storage.Storage;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.logging.Level;

/**
 * Lớp này cung cấp các phương thức để ghi lỗi vào cơ sở dữ liệu
 */
public class ErrorLogger {
    private static final String ERROR_TABLE = "storage_errors";

    /**
     * Khởi tạo lớp ErrorLogger và tạo bảng lỗi nếu chưa tồn tại
     */
    public static void initialize() {
        createErrorTable();
    }

    /**
     * Tạo bảng lỗi nếu chưa tồn tại
     */
    private static void createErrorTable() {
        // Kiểm tra database đã được khởi tạo chưa
        if (Storage.db == null) {
            Storage.getStorage().getLogger().warning("Database chưa được khởi tạo, không thể tạo bảng lỗi");
            return;
        }

        Connection conn = null;
        PreparedStatement ps = null;

        try {
            conn = Storage.db.getConnection();

            // Kiểm tra kết nối có hợp lệ không
            if (conn == null) {
                Storage.getStorage().getLogger().severe("Không thể lấy kết nối database để tạo bảng lỗi");
                return;
            }

            String createTable = "CREATE TABLE IF NOT EXISTS " + ERROR_TABLE + " (" +
                    "id INTEGER PRIMARY KEY AUTOINCREMENT, " +
                    "player_name TEXT, " +
                    "error_type TEXT, " +
                    "error_info TEXT, " +
                    "timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP" +
                    ")";

            ps = conn.prepareStatement(createTable);
            ps.executeUpdate();

            Storage.getStorage().getLogger().info("Đã khởi tạo bảng ghi lỗi");
        } catch (SQLException e) {
            Storage.getStorage().getLogger().log(Level.SEVERE, "Không thể tạo bảng ghi lỗi: " + e.getMessage(), e);
        } catch (Exception e) {
            Storage.getStorage().getLogger().log(Level.SEVERE, "Lỗi khi khởi tạo hệ thống giám sát: " + e.getMessage(), e);
        } finally {
            try {
                if (ps != null) ps.close();
                if (conn != null) Storage.db.returnConnection(conn);
            } catch (SQLException e) {
                Storage.getStorage().getLogger().log(Level.SEVERE, "Lỗi khi đóng kết nối: " + e.getMessage(), e);
            } catch (Exception e) {
                Storage.getStorage().getLogger().log(Level.SEVERE, "Lỗi khi dọn dẹp tài nguyên: " + e.getMessage(), e);
            }
        }
    }

    /**
     * Ghi lỗi vào cơ sở dữ liệu
     *
     * @param playerName Tên người chơi liên quan đến lỗi
     * @param errorType  Loại lỗi
     * @param errorInfo  Thông tin chi tiết về lỗi
     */
    public static void logError(String playerName, String errorType, String errorInfo) {
        // Kiểm tra database đã được khởi tạo chưa
        if (Storage.db == null) {
            Storage.getStorage().getLogger().warning("Database chưa được khởi tạo, không thể ghi lỗi: " + errorType);
            return;
        }

        Connection conn = null;
        PreparedStatement ps = null;

        try {
            conn = Storage.db.getConnection();

            // Kiểm tra kết nối có hợp lệ không
            if (conn == null) {
                Storage.getStorage().getLogger().warning("Không thể lấy kết nối database để ghi lỗi: " + errorType);
                return;
            }

            String sql = "INSERT INTO " + ERROR_TABLE + " (player_name, error_type, error_info, timestamp) VALUES (?, ?, ?, ?)";

            ps = conn.prepareStatement(sql);
            ps.setString(1, playerName);
            ps.setString(2, errorType);
            ps.setString(3, errorInfo);
            ps.setTimestamp(4, new Timestamp(System.currentTimeMillis()));

            ps.executeUpdate();

            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().info("Đã ghi lỗi vào DB: " + errorType + " - " + playerName);
            }
        } catch (SQLException e) {
            Storage.getStorage().getLogger().log(Level.SEVERE, "Không thể ghi lỗi vào cơ sở dữ liệu: " + e.getMessage(), e);
        } catch (Exception e) {
            Storage.getStorage().getLogger().log(Level.SEVERE, "Lỗi không xác định khi ghi lỗi: " + e.getMessage(), e);
        } finally {
            try {
                if (ps != null) ps.close();
                if (conn != null) Storage.db.returnConnection(conn);
            } catch (SQLException e) {
                Storage.getStorage().getLogger().log(Level.SEVERE, "Lỗi khi đóng kết nối: " + e.getMessage(), e);
            } catch (Exception e) {
                Storage.getStorage().getLogger().log(Level.SEVERE, "Lỗi khi dọn dẹp tài nguyên: " + e.getMessage(), e);
            }
        }
    }

    /**
     * Xóa lỗi cũ hơn một khoảng thời gian nhất định
     *
     * @param days Số ngày để giữ lại lỗi
     */
    public static void cleanupOldErrors(int days) {
        Connection conn = null;
        PreparedStatement ps = null;

        try {
            conn = Storage.db.getConnection();
            String sql = "DELETE FROM " + ERROR_TABLE + " WHERE timestamp < datetime('now', '-" + days + " days')";

            ps = conn.prepareStatement(sql);
            int deleted = ps.executeUpdate();

            if (deleted > 0) {
                Storage.getStorage().getLogger().info("Đã xóa " + deleted + " lỗi cũ hơn " + days + " ngày");
            }
        } catch (SQLException e) {
            Storage.getStorage().getLogger().log(Level.SEVERE, "Không thể xóa lỗi cũ: " + e.getMessage(), e);
        } finally {
            try {
                if (ps != null) ps.close();
                if (conn != null) Storage.db.returnConnection(conn);
            } catch (SQLException e) {
                Storage.getStorage().getLogger().log(Level.SEVERE, "Lỗi khi đóng kết nối: " + e.getMessage(), e);
            }
        }
    }
} 