package com.hongminh54.storage.compatibility;

import com.hongminh54.storage.NMS.NMSAssistant;
import com.hongminh54.storage.Storage;
import org.bukkit.attribute.Attribute;
import org.bukkit.attribute.AttributeInstance;
import org.bukkit.attribute.AttributeModifier;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;

import java.util.UUID;

/**
 * Lớp hỗ trợ tương thích Attribute API cho Minecraft 1.12.2 - 1.21.x
 * X<PERSON> lý các vấn đề tương thích với attribute system
 */
public class AttributeCompatibility {

    private static final NMSAssistant nmsAssistant = new NMSAssistant();
    private static final boolean IS_PRE_113 = nmsAssistant.isVersionLessThan(13);
    private static final boolean IS_PRE_116 = nmsAssistant.isVersionLessThan(16);
    private static final boolean IS_1_16_OR_HIGHER = nmsAssistant.isVersionGreaterThanOrEqualTo(16);
    private static final boolean IS_1_20_5_OR_HIGHER = nmsAssistant.is1_20_5OrHigher();
    private static final boolean IS_1_21_4_OR_HIGHER = nmsAssistant.is1_21_4OrHigher();

    /**
     * Lấy Attribute tương thích đa phiên bản
     *
     * @param modernName Tên attribute phiên bản mới (1.16+)
     * @param legacyName Tên attribute phiên bản cũ (1.12.2-1.15)
     * @return Attribute tương thích
     */
    public static Attribute getCompatibleAttribute(String modernName, String legacyName) {
        try {
            if (IS_1_16_OR_HIGHER) {
                return Attribute.valueOf(modernName);
            } else {
                // Phiên bản cũ có thể không có Attribute enum
                return getAttributeByReflection(legacyName);
            }
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không tìm thấy Attribute: " + modernName + "/" + legacyName);
            }
            return null;
        }
    }

    /**
     * Lấy attribute instance một cách an toàn
     *
     * @param entity    LivingEntity
     * @param attribute Attribute
     * @return AttributeInstance hoặc null nếu có lỗi
     */
    public static AttributeInstance getAttributeInstance(LivingEntity entity, Attribute attribute) {
        if (entity == null || attribute == null) {
            return null;
        }

        try {
            return entity.getAttribute(attribute);
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể lấy AttributeInstance: " + e.getMessage());
            }
            return null;
        }
    }

    /**
     * Set attribute value một cách an toàn
     *
     * @param entity    LivingEntity
     * @param attribute Attribute
     * @param value     Giá trị mới
     * @return true nếu set thành công
     */
    public static boolean setAttributeValue(LivingEntity entity, Attribute attribute, double value) {
        if (entity == null || attribute == null) {
            return false;
        }

        try {
            AttributeInstance instance = entity.getAttribute(attribute);
            if (instance != null) {
                instance.setBaseValue(value);
                return true;
            }
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể set attribute value: " + e.getMessage());
            }
        }
        return false;
    }

    /**
     * Lấy attribute value một cách an toàn
     *
     * @param entity    LivingEntity
     * @param attribute Attribute
     * @return Giá trị attribute hoặc -1 nếu có lỗi
     */
    public static double getAttributeValue(LivingEntity entity, Attribute attribute) {
        if (entity == null || attribute == null) {
            return -1;
        }

        try {
            AttributeInstance instance = entity.getAttribute(attribute);
            if (instance != null) {
                return instance.getBaseValue();
            }
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể lấy attribute value: " + e.getMessage());
            }
        }
        return -1;
    }

    /**
     * Thêm attribute modifier một cách an toàn
     *
     * @param entity    LivingEntity
     * @param attribute Attribute
     * @param modifier  AttributeModifier
     * @return true nếu thêm thành công
     */
    public static boolean addAttributeModifier(LivingEntity entity, Attribute attribute, AttributeModifier modifier) {
        if (entity == null || attribute == null || modifier == null) {
            return false;
        }

        try {
            AttributeInstance instance = entity.getAttribute(attribute);
            if (instance != null) {
                instance.addModifier(modifier);
                return true;
            }
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể thêm attribute modifier: " + e.getMessage());
            }
        }
        return false;
    }

    /**
     * Remove attribute modifier một cách an toàn
     *
     * @param entity    LivingEntity
     * @param attribute Attribute
     * @param modifier  AttributeModifier
     * @return true nếu remove thành công
     */
    public static boolean removeAttributeModifier(LivingEntity entity, Attribute attribute, AttributeModifier modifier) {
        if (entity == null || attribute == null || modifier == null) {
            return false;
        }

        try {
            AttributeInstance instance = entity.getAttribute(attribute);
            if (instance != null) {
                instance.removeModifier(modifier);
                return true;
            }
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể remove attribute modifier: " + e.getMessage());
            }
        }
        return false;
    }

    /**
     * Tạo AttributeModifier tương thích
     *
     * @param name      Tên modifier
     * @param amount    Giá trị
     * @param operation Operation type
     * @return AttributeModifier tương thích
     */
    public static AttributeModifier createAttributeModifier(String name, double amount, AttributeModifier.Operation operation) {
        try {
            if (IS_1_21_4_OR_HIGHER) {
                // Minecraft 1.20.5+: Sử dụng NamespacedKey
                return createModifierWithNamespacedKey(name, amount, operation);
            } else {
                // Phiên bản cũ: Sử dụng UUID và String name (deprecated nhưng vẫn hoạt động)
                @SuppressWarnings("deprecation")
                AttributeModifier modifier = new AttributeModifier(UUID.randomUUID(), name, amount, operation);
                return modifier;
            }
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể tạo AttributeModifier: " + e.getMessage());
            }
            // Fallback
            @SuppressWarnings("deprecation")
            AttributeModifier fallbackModifier = new AttributeModifier(UUID.randomUUID(), name, amount, operation);
            return fallbackModifier;
        }
    }

    /**
     * Set max health cho player một cách an toàn
     *
     * @param player    Player
     * @param maxHealth Max health mới
     * @return true nếu set thành công
     */
    public static boolean setPlayerMaxHealth(Player player, double maxHealth) {
        if (player == null || maxHealth <= 0) {
            return false;
        }

        try {
            Attribute healthAttribute = getCompatibleAttribute("GENERIC_MAX_HEALTH", "MAX_HEALTH");
            if (healthAttribute != null) {
                return setAttributeValue(player, healthAttribute, maxHealth);
            }
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể set max health: " + e.getMessage());
            }
        }
        return false;
    }

    /**
     * Lấy max health của player một cách an toàn
     *
     * @param player Player
     * @return Max health hoặc -1 nếu có lỗi
     */
    public static double getPlayerMaxHealth(Player player) {
        if (player == null) {
            return -1;
        }

        try {
            Attribute healthAttribute = getCompatibleAttribute("GENERIC_MAX_HEALTH", "MAX_HEALTH");
            if (healthAttribute != null) {
                return getAttributeValue(player, healthAttribute);
            }
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể lấy max health: " + e.getMessage());
            }
        }
        return player.getMaxHealth(); // Fallback
    }

    /**
     * Set movement speed cho player một cách an toàn
     *
     * @param player Player
     * @param speed  Movement speed mới (0.0 - 1.0)
     * @return true nếu set thành công
     */
    public static boolean setPlayerMovementSpeed(Player player, double speed) {
        if (player == null || speed < 0) {
            return false;
        }

        try {
            Attribute speedAttribute = getCompatibleAttribute("GENERIC_MOVEMENT_SPEED", "MOVEMENT_SPEED");
            if (speedAttribute != null) {
                return setAttributeValue(player, speedAttribute, speed);
            }
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể set movement speed: " + e.getMessage());
            }
        }
        return false;
    }

    /**
     * Lấy movement speed của player một cách an toàn
     *
     * @param player Player
     * @return Movement speed hoặc -1 nếu có lỗi
     */
    public static double getPlayerMovementSpeed(Player player) {
        if (player == null) {
            return -1;
        }

        try {
            Attribute speedAttribute = getCompatibleAttribute("GENERIC_MOVEMENT_SPEED", "MOVEMENT_SPEED");
            if (speedAttribute != null) {
                return getAttributeValue(player, speedAttribute);
            }
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể lấy movement speed: " + e.getMessage());
            }
        }
        return 0.1; // Default speed
    }

    /**
     * Reset tất cả attributes của player về mặc định
     *
     * @param player Player
     * @return true nếu reset thành công
     */
    public static boolean resetPlayerAttributes(Player player) {
        if (player == null) {
            return false;
        }

        try {
            // Reset max health
            setPlayerMaxHealth(player, 20.0);

            // Reset movement speed
            setPlayerMovementSpeed(player, 0.1);

            // Reset attack damage nếu có
            Attribute attackAttribute = getCompatibleAttribute("GENERIC_ATTACK_DAMAGE", "ATTACK_DAMAGE");
            if (attackAttribute != null) {
                setAttributeValue(player, attackAttribute, 1.0);
            }

            return true;
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể reset attributes: " + e.getMessage());
            }
            return false;
        }
    }

    /**
     * Lấy attribute bằng reflection cho phiên bản cũ
     */
    private static Attribute getAttributeByReflection(String attributeName) {
        try {
            // Thử với Attribute enum trước
            return Attribute.valueOf(attributeName);
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Attribute không tồn tại trong phiên bản này: " + attributeName);
            }
            return null;
        }
    }

    /**
     * Tạo AttributeModifier với NamespacedKey cho phiên bản 1.20.5+
     */
    private static AttributeModifier createModifierWithNamespacedKey(String name, double amount, AttributeModifier.Operation operation) {
        try {
            // Sử dụng reflection để tạo NamespacedKey
            Class<?> namespacedKeyClass = Class.forName("org.bukkit.NamespacedKey");
            java.lang.reflect.Constructor<?> constructor = namespacedKeyClass.getConstructor(String.class, String.class);
            Object namespacedKey = constructor.newInstance("storage", name.toLowerCase().replace(" ", "_"));

            // Tạo AttributeModifier với NamespacedKey
            java.lang.reflect.Constructor<?> modifierConstructor = AttributeModifier.class.getConstructor(
                    namespacedKeyClass, double.class, AttributeModifier.Operation.class);
            return (AttributeModifier) modifierConstructor.newInstance(namespacedKey, amount, operation);
        } catch (Exception e) {
            // Fallback với UUID
            @SuppressWarnings("deprecation")
            AttributeModifier fallbackModifier = new AttributeModifier(UUID.randomUUID(), name, amount, operation);
            return fallbackModifier;
        }
    }
}
