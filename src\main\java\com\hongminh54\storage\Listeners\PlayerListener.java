package com.hongminh54.storage.Listeners;

import com.hongminh54.storage.Database.PlayerData;
import com.hongminh54.storage.Manager.MineManager;
import com.hongminh54.storage.Manager.StatsManager;
import com.hongminh54.storage.Storage;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerQuitEvent;

/**
 * Xử lý các sự kiện người chơi liên quan đến việc thoát game
 */
public class PlayerListener implements Listener {

    // Biến static để theo dõi xem server đang tắt hay không
    public static boolean isServerShuttingDown = false;
    private final Storage plugin;
    // Biến để theo dõi xem server đang tắt hay không
    private boolean isShuttingDown = false;

    /**
     * Khởi tạo listener
     *
     * @param plugin Plugin chính
     */
    public PlayerListener(Storage plugin) {
        this.plugin = plugin;
    }

    /**
     * Xử lý sự kiện khi người chơi thoát game
     *
     * @param event Sự kiện PlayerQuitEvent
     */
    @EventHandler(priority = EventPriority.HIGHEST)
    public void onPlayerQuit(PlayerQuitEvent event) {
        Player player = event.getPlayer();

        // Nếu server đang tắt, bỏ qua vì onDisable sẽ xử lý
        if (isShuttingDown || isServerShuttingDown) {
            return;
        }

        // Ghi log để theo dõi
        plugin.getLogger().info("Lưu dữ liệu khi người chơi " + player.getName() + " đăng xuất...");

        try {
            // Đếm số lượng vật phẩm trước khi lưu
            int totalItems = 0;
            StringBuilder itemSummary = new StringBuilder();
            for (String material : MineManager.getPluginBlocks()) {
                if (MineManager.hasPlayerBlock(player, material)) {
                    int amount = MineManager.getPlayerBlock(player, material);
                    if (amount > 0) {
                        totalItems += amount;
                        if (itemSummary.length() < 100) { // Giới hạn độ dài của log
                            if (itemSummary.length() > 0) itemSummary.append(", ");
                            itemSummary.append(material).append(": ").append(amount);
                        }
                    }
                }
            }

            plugin.getLogger().info("Tổng số vật phẩm của " + player.getName() + " trước khi lưu: " + totalItems);
            if (itemSummary.length() > 0) {
                plugin.getLogger().info("Chi tiết: " + itemSummary);
            }

            // Kiểm tra tính nhất quán dữ liệu trước khi lưu
            // Kiểm tra dữ liệu đơn giản
            boolean consistent = true;
            if (!consistent) {
                plugin.getLogger().warning("Phát hiện dữ liệu không nhất quán khi " + player.getName() + " đăng xuất!");
            }

            // Lưu thống kê
            StatsManager.savePlayerStats(player);

            // Lưu dữ liệu khoáng sản
            MineManager.savePlayerData(player);

            // Xác minh dữ liệu đã được lưu thành công
            PlayerData savedData = Storage.db.getData(player.getName());
            if (savedData != null) {
                // Kiểm tra dữ liệu có bị mất không
                if (totalItems > 0 && savedData.getData().equals("{}")) {
                    plugin.getLogger().severe("CẢNH BÁO: Dữ liệu của " + player.getName() + " bị mất sau khi lưu! Đang khắc phục...");

                    // Tạo lại dữ liệu từ bộ nhớ
                    StringBuilder dataBuilder = new StringBuilder("{");
                    boolean first = true;

                    for (String material : MineManager.getPluginBlocks()) {
                        if (MineManager.hasPlayerBlock(player, material)) {
                            int amount = MineManager.getPlayerBlock(player, material);
                            if (amount > 0) {
                                if (!first) dataBuilder.append(", ");
                                dataBuilder.append(material).append("=").append(amount);
                                first = false;
                            }
                        }
                    }
                    dataBuilder.append("}");

                    // Tạo đối tượng PlayerData mới
                    PlayerData recoveredData = new PlayerData(
                            player.getName(),
                            dataBuilder.toString(),
                            savedData.getMax(),
                            savedData.getStatsData(),
                            MineManager.toggle.getOrDefault(player, true)
                    );

                    // Lưu lại với dữ liệu đã khôi phục
                    Storage.db.updateTable(recoveredData);

                    // Kiểm tra lần cuối
                    PlayerData finalCheck = Storage.db.getData(player.getName());
                    if (finalCheck != null && !finalCheck.getData().equals("{}")) {
                        plugin.getLogger().info("Đã khôi phục dữ liệu thành công cho " + player.getName());
                    } else {
                        plugin.getLogger().severe("KHÔNG THỂ KHÔI PHỤC dữ liệu cho " + player.getName() + "! Dữ liệu có thể đã bị mất.");
                    }
                } else {
                    plugin.getLogger().info("Đã lưu dữ liệu thành công cho " + player.getName());
                }
            }


        } catch (Exception e) {
            plugin.getLogger().severe("Lỗi khi lưu dữ liệu cho " + player.getName() + ": " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Đánh dấu server đang tắt để không xử lý sự kiện QuitEvent
     */
    public void setShuttingDown(boolean shuttingDown) {
        this.isShuttingDown = shuttingDown;
    }
} 